<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item label="节点" prop="node">
        <el-input v-model="formData.node" placeholder="请输入节点" />
      </el-form-item>
      <el-form-item label="对应模块" prop="type">
        <el-select v-model="formData.type" placeholder="请选择对应模块">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_SERVER_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="简体中文" prop="zhCn">
        <el-input v-model="formData.zhCn" placeholder="请输入简体中文" />
      </el-form-item>
      <el-form-item label="英语" prop="enUs">
        <div style="display: flex;">
          <el-input v-model="formData.enUs" placeholder="请输入英语" style="width: 430px;margin-right: 5px;" />
          <el-button type="success" round @click="translateRemark">翻译</el-button>
        </div>
      </el-form-item>
      <el-form-item label="繁体中文" prop="zhTw">
        <el-input v-model="formData.zhTw" placeholder="请输入繁体中文" />
      </el-form-item>

    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { InternalizationApi, InternalizationVO } from '@/api/infra/internalization'

/** 国际化翻译管理 表单 */
defineOptions({ name: 'InternalizationForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  type: undefined,
  zhCn: undefined,
  enUs: undefined,
  zhTw: undefined,
  node: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, node?: string) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (node) {
    formLoading.value = true
    try {
      formData.value = await InternalizationApi.getInternalization(node)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as InternalizationVO
    if (formType.value === 'create') {
      await InternalizationApi.createInternalization(data)
      message.success(t('admin_common_createSuccess'))
    } else {
      await InternalizationApi.updateInternalization(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    type: undefined,
    zhCn: undefined,
    enUs: undefined,
    zhTw: undefined,
    node: undefined
  }
  formRef.value?.resetFields()
}


const translateRemark = async () => {
  if (!formData.value.zhCn) return;

  try {
    const translatedText = await InternalizationApi.translateToEn(formData.value.zhCn);
    formData.value.enUs = translatedText;
  } catch (error) {
    console.error('翻译错误:', error);
  }
};
</script>
