<template>
  <div class="dr-table-options">
    <component
      :is="defaultValueType?.wrapper"
      :model-value="defaultCheckedValue"
      @change="onDefauleValueChange"
    >
      <VueDraggable
        :model-value="modelValue"
        item-key="value"
        :force-fallback="true"
        :animation="200"
        handle=".drag-icon"
        @change="onDraggableChange"
      >
        <template #item="{ element, index }">
          <div class="drag-item">
            <component
              :is="defaultValueType?.children"
              :value="element.value"
              @click="onRadioClick(element.value)"
            />
            <el-color-picker
              :model-value="element.color"
              :predefine="predefineColors"
              @change="onChangeColor($event, index)"
            />
            <el-input
              :model-value="element.label"
              style="flex: 1"
              @input="onLabelChange($event, index)"
            >
              <!-- <template #append>
                <el-popover
                  placement="bottom-end"
                  :width="300"
                  :hide-after="0"
                  trigger="click"
                  ref="pop"
                  popper-class="_fd-language-popover"
                >
                  <template #reference>
                    <i class="fc-icon icon-language"></i>
                  </template>
                  <div class="_fd-language-list">
                    <div class="_fd-language-row">
                      <span class="_fd_lanuage_title">中文</span>
                      <span class="_fd_lanuage_content">
                        <el-input :model-value="element.label" :disabled="true" />
                      </span>
                    </div>
                    <div class="_fd-language-row">
                      <span class="_fd_lanuage_title">英文</span>
                      <span class="_fd_lanuage_content">
                        <el-input v-model="element.labelEn" @input="onLabelEnChange($event, index)"/>
                      </span>
                    </div>
                  </div>
                </el-popover>
              </template> -->
            </el-input>
            <el-icon class="cursor-pointer" :size="18" color="#2e73ff" @click="insertItem(index)">
              <CirclePlusFilled />
            </el-icon>
            <el-icon class="cursor-pointer" :size="18" @click="deleteItem(index)">
              <Delete class="text-red-5" />
            </el-icon>
            <el-icon class="drag-icon" :size="18"><Rank /></el-icon>
          </div>
        </template>
      </VueDraggable>
    </component>
    <div class="bottom-tools">
      <el-button type="primary" size="small" style="width: 100%" @click="openBatchEditDialog">
        批量添加
      </el-button>
    </div>

    <el-dialog v-model="dialogVisible" title="批量添加选项" width="500" @closed="handleClose">
      <el-alert
        title="批量添加列表选项，输入选项值"
        description="每行一个选项"
        type="info"
        :closable="false"
      />
      <el-input
        v-model="optionsText"
        :rows="20"
        type="textarea"
        placeholder="Please input"
        style="margin-top: 10px"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmBatchInsert"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { arrayMoveImmutable } from 'array-move'
import VueDraggable from 'vuedraggable'
import { Rank, Delete, CirclePlusFilled } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'
import uniqueId from '@form-create/utils/lib/unique'

type PropsType = {
  modelValue?: { label: string; value: string; color: string; checked: boolean, labelEn: string }[]
  type: 'radio' | 'checkbox'
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => []
})

const emit = defineEmits(['update:modelValue', 'change'])

const defaultValueType = computed(() => {
  if (props.type === 'radio') {
    return {
      wrapper: 'el-radio-group',
      children: 'el-radio'
    }
  }

  if (props.type === 'checkbox') {
    return {
      wrapper: 'el-checkbox-group',
      children: 'el-checkbox'
    }
  }

  return {
    wrapper: 'div',
    children: 'div'
  }
})

const defaultCheckedValue = computed(() => {
  if (props.type === 'radio') {
    const res = props.modelValue.find((item) => item.checked)
    console.log('res', res)

    if (!res) {
      return undefined
    }
    return res.value
  }
  if (props.type === 'checkbox') {
    const res = props.modelValue.filter((item) => item.checked).map((item) => item.value)
    return res
  }

  return void 0
})

const onDefauleValueChange = (value: string | string[]) => {
  const newValue = cloneDeep(props.modelValue)

  if (props.type === 'radio') {
    return
    newValue.forEach((item) => {
      if (item.value === value) {
        item.checked = true
      } else {
        item.checked = false
      }
    })
  }

  if (props.type === 'checkbox') {
    newValue.forEach((item) => {
      if (value.includes(item.value)) {
        item.checked = true
      } else {
        item.checked = false
      }
    })
  }

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const onChangeColor = (color: string | null, index: number) => {
  const newValue = cloneDeep(props.modelValue)
  newValue[index].color = color || '#EFEFEF'

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const onLabelChange = (label: string, index: number) => {
  const newValue = cloneDeep(props.modelValue)
  newValue[index].label = label

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

// const onLabelEnChange = (label: string, index: number) => {
//   const newValue = cloneDeep(props.modelValue)
//   newValue[index].labelEn = label

//   emit('update:modelValue', newValue)
//   emit("change", newValue)
//   //@ts-ignore
//   emit('updateLanguageInput', props.formCreateInject.api, newValue)
// }

const onDraggableChange = (e: any) => {
  if (e.moved) {
    const newValue = arrayMoveImmutable(props.modelValue, e.moved.oldIndex, e.moved.newIndex)

    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
  if (e.added) {
  }
  if (e.removed) {
  }
}

const onRadioClick = (value) => {
  if (props.type === 'radio') {
    const newValue = cloneDeep(props.modelValue)

    newValue.forEach((item) => {
      if (item.value === value && item.checked === false) {
        item.checked = true
      } else {
        item.checked = false
      }
    })

    emit('update:modelValue', newValue)
    emit('change', newValue)
  }
}

const insertItem = (index: number) => {
  const value = uniqueId()
  const addItem = {
    value,
    label: `选项${value}`,
    labelEn: "",
    color: '#EFEFEF',
    checked: false
  }
  const newValue = cloneDeep(props.modelValue)
  newValue.splice(index + 1, 0, addItem)

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const deleteItem = (index: number) => {
  const newValue = cloneDeep(props.modelValue)
  newValue.splice(index, 1)

  emit('update:modelValue', newValue)
  emit('change', newValue)
}

const dialogVisible = ref<boolean>(false)
const optionsText = ref<string>('')

const openBatchEditDialog = () => {
  dialogVisible.value = true
}

const handleClose = () => {
  console.log('handleClose')
  optionsText.value = ''
}

const confirmBatchInsert = () => {
  const insertOptions = optionsText.value
    .split('\n')
    .filter((label) => label.trim() !== '')
    .map((label) => {
      const value = uniqueId()
      const addItem = {
        value,
        label: label.trim(),
        labelEn: "",
        color: '#EFEFEF',
        checked: false
      }
      return addItem
    })

  const newValue = cloneDeep(props.modelValue)
  newValue.push(...insertOptions)

  emit('update:modelValue', newValue)
  emit('change', newValue)
  dialogVisible.value = false
}

// normal: #EFEFEF
const predefineColors = [
  '#DCD5FE',
  '#C2D3FF',
  '#C9F0FF',
  '#BDEEDA',
  '#C6F5B2',
  '#FEE5B8',
  '#FFD7B1',
  '#FECED8',
  '#FEE1F2',
  '#E1F0A4',
  '#C4F1EC',
  '#C4BAFC',
  '#95B4FC',
  '#8BDEBE',
  '#9CEA78',
  '#FDD587',
  '#FFAD5F',
  '#FEACBB',
  '#F8C4E2',
  '#C7DE60',
  '#6EE8D8'
]
</script>

<style scoped lang="scss">
.dr-table-options {
  width: 100%;

  .drag-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
    user-select: none;
    padding: 5px 0;

    .drag-icon {
      cursor: move;
    }

    :deep(.el-radio) {
      margin-right: 0;
    }
  }
}

.el-input {
  ::v-deep(.el-input-group__append) {
    padding: 0 10px !important;
  }
}
</style>
