<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery" />
  </ContentWrap>

  <ContentWrap>
    <Table ref="tableRef" :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @SortChange="handleSortChange">
    </Table>
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { TeamApi, TeamVO } from '@/api/user/team'
import businessConfig from './components/index.config'

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as TeamVO[]
})

defineOptions({ name: 'InviteCoopRecord' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  keyword = ''
  createTime = []
  joinTime = []
  deleteTime = []
  sortBy = ''
  descending: any = null
}
const queryParams = reactive(new DefaultQuery())
const tableRef = ref()

const handleQuery = (type: string) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      tableRef.value.clearSort()
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'export':
      console.log('export')
      break;
    default:
      break;
  }
}

const handleSortChange = (data: any) => {
  console.log('handleSortChange', data)
  if (data.order == null) {
    queryParams.sortBy = ''
    queryParams.descending = null
  } else {
    queryParams.sortBy = data.prop
    queryParams.descending = data.order == 'descending'
  }
  getList()
}

/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await TeamApi.getInviteCoopRecord(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})
</script>
