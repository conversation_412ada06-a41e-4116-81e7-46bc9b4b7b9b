<template>
  <ContentWrap :title="t('admin_user_sccsDetail')">
    <!-- 基础信息 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_basicInfo') }}
      </div>
      <div class="menu-content mt-12px">
        <el-descriptions :column="4" size="default" direction="vertical">
          <el-descriptions-item label="id" :span="1">{{ pageData.id || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_sccsCode')" :span="1">{{ pageData.sccsCode || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_common_name')" :span="1">{{ pageData.sccsName || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_inTeamName')" :span="1">{{ pageData.teamName || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_teamCode')" :span="1">
            {{ pageData.teamCode || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('admin_sccs_team_owner_account')">
            {{ pageData.username || '-' }} ( {{ pageData.account || '-' }} )</el-descriptions-item>
          <el-descriptions-item :label="t('admin_common_createTime')">{{ formatDate(pageData.createTime) || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_templateInfo')">
            {{ pageData.typeName || '-' }} | {{ pageData.industryName || '-' }} | {{ pageData.typeCode || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_templateUpdateTime')">{{ formatDate(pageData.updateTime) || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_template_updater')">{{ pageData.updateUsername || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_orderNumber')" :span="1">
            {{ pageData.orderNumber || '0' }}
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <!-- 成员信息 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_memberInfo') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
    pageData.sccsMemberList?.length || 0 }} {{ t('admin_user_people') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.memberColumns" :data="pageData.sccsMemberList">
        </Table>
      </div>
    </div>
    <!-- 角色列表 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_roleData') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
    pageData.sccsRoleAdminList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.roleColumns" :data="pageData.sccsRoleAdminList">
        </Table>
      </div>
    </div>
    <!-- 协作团队 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_sccsInviteTeam') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
    pageData.sccsCoopTeamList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.inviteTeamColumns" :data="pageData.sccsCoopTeamList">
        </Table>
      </div>
    </div>
    <!-- 协作角色列表  -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_inviteTeamRole') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
    pageData.sccsCoopTeamRoleAdminList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.inviteOurTeamColumn" :data="pageData.sccsCoopTeamRoleAdminList">
        </Table>
      </div>
    </div>

  </ContentWrap>
</template>
<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import businessConfig from './index.config'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { SccsBizTemplateApi } from '@/api/user/sccs'

/** 运营端行业管理 表单 */
defineOptions({ name: 'SccsDetail' })

const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false)
const sccsId = ref('')
const pageData: any = ref({}) // 页面数据
const tableState = reactive({
  memberColumns: businessConfig.detailMemberTableThs,
  roleColumns: businessConfig.detailRoleTableThs,
  inviteTeamColumns: businessConfig.detailInviteTeamListTableThs,
  inviteOurTeamColumn: businessConfig.detailInviteOurTeamListTableThs
})


const getDetail = async () => {
  loading.value = true
  try {
    pageData.value = await SccsBizTemplateApi.getSccs(sccsId.value)
  } finally {
    loading.value = false
  }
}
const init = async () => {
  sccsId.value = route.query.id as string
  getDetail()
}
init()



</script>
<style scoped lang="scss">
:deep(.el-descriptions__label) {
  font-weight: 700 !important;
}

:deep(.el-descriptions__cell) {
  width: 25% !important;
  padding: 0 10px;
  vertical-align: top;
}
</style>