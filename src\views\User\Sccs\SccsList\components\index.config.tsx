import { DICT_TYPE, getIntDictOptions, getBoolDictOptions, getStrDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
import LinKinTip from '@/components/LinKinTip/index.vue'
// import { useI18n } from 'vue-i18n'

const { t } = useI18n() // 国际化

const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: "id",
      prop: 'id',
      'min-width': 180
    },
    {
      label: t('admin_sccs_code'),
      prop: 'sccsCode'
    },
    {
      label: t('admin_sccs_name'),
      prop: 'sccsName'
    },
    {
      label: t('admin_home_inTeam'),
      prop: 'teamName'
    },
    {
      label: t('admin_team_teamCode'),
      prop: 'teamCode'
    },
    {
      label: t('admin_sccs_member_count'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.sccsMemberCount + '/' + row.teamMemberCount}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_sccs_team_owner_account'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.ownerName}</p>
              <p class={'my-2px'}>{row.ownerEmail}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_sccs_coop_team_count'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.sccsCoopTeamCount + '/' + row.teamCoopTeamCount}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_sccs_order_count'),
      prop: 'orderCount'
    },
    {
      label: t('admin_template_name'),
      prop: 'bizTemplateName'
    },
    {
      label: t('admin_template_typeCode'),
      prop: 'typeCode'
    },
    {
      label: t('admin_template_industry'),
      prop: 'industryName'
    },
    {
      label: t('admin_template_creator'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.createUserName}</p>
              <p class={'my-2px'}>{row.createUserEmail}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'datetimerange',
      'min-width': 165
    },
    {
      label: t('admin_sccs_template_update_time'),
      prop: 'templateUpdateTime',
      type: 'datetimerange',
      'min-width': 165
    },
    {
      label: t('admin_template_updater'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.templateUpdateUserName}</p>
              <p class={'my-2px'}>{row.templateUserEmail}</p>
            </div>
          )
        }
      }
    }
  ],
  tableBtnList: [
    { label: t('admin_common_detail'), key: "detail", type: "primary", link: true },
    { label: t('admin_sccs_template_update'), key: 'design', type: 'primary', link: true },
    { label: t('admin_sccs_btn_copy_to_library'), key: 'copy', type: 'primary', link: true },
    { label: t('admin_common_delect'), key: 'delete', type: 'primary', link: true }
  ],
  searchConfig: [
    //头部筛选数据
    {
      label: t('admin_common_inputSearch'),
      prop: 'keyword',
      placeholder: t('admin_sccs_query_keyword_placeholder')
    },
    {
      label: t('admin_template_typeCode'),
      prop: 'typeCode',
      type: 'select',
      loading: false,
      options: getStrDictOptions(DICT_TYPE.SYSTEM_TEMPLATE_CREATE_TYPE)
    },
    {
      label: t('admin_template_industry'),
      prop: 'industryId',
      type: 'select',
      loading: false,
      options: [],
      'popper-append-to-body': false,
      'popper-class': 'cmSelect'
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': '-',
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': 'YYYY-MM-DD HH:mm:ss'
    }
  ],
  searchBtnList: [
    // {
    //   label: t('admin_common_add'),
    //   key: 'add',
    //   icon: 'ep:plus',
    //   elProps: {
    //     type: 'primary'
    //   }
    // }
  ],
  detailMemberTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_memberAccount'),
      prop: "account",
    },
    {
      label: t('admin_user_memberName'),
      prop: "username",
    },
    {
      label: t('admin_user_role'),
      prop: "sccsMemberRoleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.sccsMemberRoleList?.length > 0 ? (
                row.sccsMemberRoleList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', data.manager ? 'bg-#eef8ff' : 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color={data.manager ? '#0070d2' : '#808080'}
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.name}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_user_creatorName'),
      prop: "creatorName",
    },
    {
      label: t('admin_user_joinTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  detailRoleTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_roleName'),
      prop: "name",
    },
    {
      label: t('admin_user_teamMemberNumber'),
      prop: "teamMemberNumber",
    },
    {
      label: t('admin_user_memberList'),
      prop: "teamMemberList",
      'show-overflow-tooltip': false,
      'min-width': '240',
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.teamMemberList?.length > 0 ? (
                row.teamMemberList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.account + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    }
  ],
  detailInviteTeamListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_teamName'),
      prop: "teamName"
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_user_sccsCoopMember'),
      prop: "coopTeamMemberList",
      'show-overflow-tooltip': false,
      'min-width': 280,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.coopTeamMemberList?.length > 0 ? (
                row.coopTeamMemberList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'flex', 'items-center', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    {data.isDocking ? <div class={['color-#409eff', 'mr-3px', 'min-w-50px', 'leading-20px']}>对接人</div> : ''}
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.account + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      }
    },
    {
      label: t('admin_user_role'),
      prop: "sccsRoleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.sccsRoleList?.length > 0 ? (
                row.sccsRoleList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', data.manager ? 'bg-#eef8ff' : 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color={data.manager ? '#0070d2' : '#808080'}
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.name}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_creatorName'),
      prop: "creatorName"
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  detailInviteOurTeamListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_roleName'),
      prop: "name"
    },
    {
      label: t('admin_user_coopTeamNumber'),
      prop: "teamNumber",
    },
    {
      label: t('admin_user_coopTeamInfo'),
      prop: "teamInfoAdminDTOList",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.teamInfoAdminDTOList?.length > 0 ? (
                row.teamInfoAdminDTOList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color={'#808080'}
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.teamName + ' (' + data.teamCode + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
      'min-width': 180
    }
  ],
}

export default fixedParameter
