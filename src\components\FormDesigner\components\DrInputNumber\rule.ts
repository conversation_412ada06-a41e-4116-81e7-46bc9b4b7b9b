import { props } from '@/plugins/fcDesignerPro/locale/en'
import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrInputNumber'
const label = '数字'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-number',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  condition: 'number',
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      readMode: 'custom',
      props: {
        defaultValueConfig: { type: 'none', content: '' },
        placeholder: '请输入',
        precision: 0,
        useThousandSeparator: false,
        unitPosition: 'before'
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(data, { t }) {
    return [
      {
        type: 'DrDefaultValue',
        field: 'defaultValueConfig',
        title: '默认内容',
        props: {
          currentField: data.field,
          componentName: name,
          currentWidgetId: data._fc_id
        }
      },
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("placeholderEn", langValue);
            api.activeRule.props["placeholderEn"] = langValue;
          },
        },
      },
      {
        type: 'LanguageInput',
        title: '单位',
        field: 'unit',
        props: {
          maxlength: 20,
          showWordLimit: true
        },
        on: {
          updateI18n(api, data, langValue) {
            api.setData("unitEn", langValue);
            api.activeRule.props["unitEn"] = langValue;
          },
        },
      },
      {
        type: 'radio',
        field: 'unitPosition',
        value: 'before',
        title: '单位位置',
        options: [
          { value: 'before', label: '前' },
          { value: 'after', label: '后' }
        ]
      },
      {
        type: 'inputNumber',
        title: '小数位数',
        field: 'precision',
        props: {
          min: 0,
          max: 20,
          precision: 0
        }
      },
      {
        type: 'inputNumber',
        title: '最小值',
        field: 'min'
      },
      {
        type: 'inputNumber',
        title: '最大值',
        field: 'max'
      },
      {
        type: 'switch',
        title: '千分位',
        field: 'useThousandSeparator'
      }
    ]
  }
}

export default Rule
