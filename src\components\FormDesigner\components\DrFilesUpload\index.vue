<template>
  <div class="dr-images-upload">
    <el-upload
      v-model:file-list="images"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      multiple
      :limit="limit"
      :on-remove="handleRemove"
    >
      <el-button type="primary">选择文件</el-button>
    </el-upload>
  </div>
</template>

<script setup lang="ts">
import { UploadProps } from 'element-plus'

type PropsType = {
  limit?: number
  maxFileSize?: number
  accept?: string[]
}

withDefaults(defineProps<PropsType>(), {
  limit: 9,
  maxFileSize: 0
})

const images = defineModel<{ name: string; url: string }[]>({ required: false, default: [] })

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}
</script>

<style scoped lang="scss"></style>
