<template>
  <div class="formulas-editor">
    <el-button type="primary" plain style="width: 100%" @click="openDialog">
      {{ formulaText }}
    </el-button>

    <el-dialog
      v-model="dialogVisible"
      title="编辑公式"
      width="1000"
      @closed="codemirrorVisible = false"
    >
      <div class="dialog-body">
        <div class="codemirror-header">
          <span class="codemirror-title">{{ formulasTitle }}</span> =
          在下方列表中选择函数和字段变量，且在英文输入法下编辑
        </div>
        <div class="codemirror-top">
          <div class="codemirror-top-left">公式预览</div>
          <div class="codemirror-top-right">
            <el-button type="primary" small @click="handleClearCodemirror">清空</el-button>
          </div>
        </div>
        <div class="codemirror-editer">
          <Codemirror
            v-if="codemirrorVisible"
            ref="codemirrorEditor"
            v-model:value="formulasValue"
            :options="cmOptions"
            :height="250"
            @paste="handlePaste"
          />
        </div>
        <div class="slider-container">
          <div>
            <el-input
              v-model="filterVariable"
              placeholder="请输入要搜索的字段名"
              style="width: 240px; margin: 10px 15px"
              size="default"
              clearable
            />
            <el-tree
              ref="VariableTreeRef"
              class="field-tree"
              :data="flatTemplateList"
              node-key="id"
              accordion
              :filter-node-method="handleVariableFilterNode"
              @node-click="handleFieldNodeClick"
            >
              <template #default="{ data }">
                {{ data.label }}
                <span v-if="data.type && data.type !== 'DrTableForm'" class="field-span-row">{{
                  widgetFormula[data.type]
                }}</span>
              </template>
            </el-tree>
          </div>
          <div>
            <el-input
              v-model="filterFormulaText"
              placeholder="请输入要搜索的函数名"
              style="width: 240px; margin: 10px 15px"
              size="default"
              clearable
            />
            <el-tree
              ref="FormulaTreeRef"
              class="formula-tree"
              :data="formulaData"
              node-key="id"
              accordion
              :filter-node-method="handleFormulasFilterNode"
              @node-click="handleFuncNodeClick"
            >
              <template #default="{ data }">
                <div class="hover-node" @mouseover="nodeOver(data)">
                  <span class="custom-tree-node"> {{ data.formulaFunName }} </span>
                </div>
              </template>
            </el-tree>
          </div>

          <div class="hover-tips">
            <div
              v-if="formulaFunObject.formulaTips || formulaFunObject.formulaExample"
              class="hover-tips-content"
            >
              <p v-if="formulaFunObject.formulaName"
                >函数名称：{{ formulaFunObject.formulaName }}
              </p>
              <p v-if="formulaFunObject.formulaTips"
                >函数说明：{{ formulaFunObject.formulaTips }}
              </p>
              <p v-if="formulaFunObject.formulaExample"
                >函数示例：{{ formulaFunObject.formulaExample }}
              </p>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="saveFomulasValue"> 确认 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, nextTick, computed } from 'vue'
import Codemirror, { CmComponentRef } from 'codemirror-editor-vue3'
import 'codemirror/addon/display/placeholder.js'
import 'codemirror/mode/javascript/javascript.js'
import 'codemirror/theme/dracula.css'
import 'codemirror/lib/codemirror.css'
import { formCreateInjectionData } from '../../inject'
import { getRelateFieldFromTree } from '../../utils'
import { formulaData, widgetFormula } from './formula'
import { cloneDeep } from 'lodash-es'

type PropsType = {
  modelValue?: any
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: {}
})
const emit = defineEmits(['update:modelValue', 'change'])
// 自定义配置 看文档要效果
const cmOptions = ref({
  mode: 'javascript',
  theme: 'default',
  lineNumbers: false,
  lineWrapping: true,
  lint: true,
  styleActiveLine: true,
  readonly: true,
  viewportMargin: Infinity, // 使编辑器自动匹配容器大小
  // gutters: ['CodeMirror-lint-markers'],
  placeholder: ''
})
const injectionData: any = inject(formCreateInjectionData)
const injectDesigner: any = inject('designer')
const formulasValue = ref<string>('')
const dialogVisible = ref<boolean>(false)
const codemirrorVisible = ref<boolean>(false)
const codemirrorEditor = ref<CmComponentRef>(null)
const filterVariable = ref<string>('')
const filterFormulaText = ref<string>('')
const VariableTreeRef = ref<HTMLElement | any>(null)
const FormulaTreeRef = ref<HTMLElement | any>(null)
const formulasTitle = ref<any>('')

const formulaText = computed(() => {
  return props.modelValue.formulas ? '修改公式' : '设置公式'
})

const openDialog = () => {
  dialogVisible.value = true
  nextTick(() => {
    codemirrorVisible.value = true
    formulasValue.value = cloneDeep(props.modelValue.formulas)
    nextTick(() => {
      const cm = codemirrorEditor.value?.cminstance
      if (!cm) return

      // 清除所有现有标记
      cm.getAllMarks().forEach((mark) => mark.clear())

      // 在文本中查找并标记所有字段ID
      const content = cm.getValue()

      Object.keys(fieldToLabelMap.value).forEach((fieldId) => {
        let pos = 0
        while ((pos = content.indexOf(fieldId, pos)) !== -1) {
          const startIndex = pos
          pos += fieldId.length

          // 计算行和列位置
          const from = cm.posFromIndex(startIndex)
          const to = cm.posFromIndex(startIndex + fieldId.length)

          // 添加新标记
          cm.markText(from, to, {
            replacedWith: createMarkElement(fieldToLabelMap.value[fieldId])
          })
        }
      })
      //@ts-ignore
      formulasTitle.value = props.formCreateInject.api?.activeRule.title
    })
  })
}

const handlePaste = () => {
  console.log(formulasValue.value)
  nextTick(() => {
    const cm = codemirrorEditor.value?.cminstance
    if (!cm) return

    // 清除所有现有标记
    cm.getAllMarks().forEach((mark) => mark.clear())

    // 在文本中查找并标记所有字段ID
    const content = cm.getValue()

    Object.keys(fieldToLabelMap.value).forEach((fieldId) => {
      let pos = 0
      while ((pos = content.indexOf(fieldId, pos)) !== -1) {
        const startIndex = pos
        pos += fieldId.length

        // 计算行和列位置
        const from = cm.posFromIndex(startIndex)
        const to = cm.posFromIndex(startIndex + fieldId.length)

        // 添加新标记
        cm.markText(from, to, {
          replacedWith: createMarkElement(fieldToLabelMap.value[fieldId])
        })
      }
    })
  })
}

const handleClearCodemirror = () => {
  formulasValue.value = ''
}

const saveFomulasValue = () => {
  let newFormulasValue = formulasValue.value
  let formulasParams = formulasValue.value.match(/[^"\s@,()]+(?:@@[^"\s@,()]+){1,2}/g)
  formulasParams?.forEach((formulas) => {
    const fieldName = `"${formulas}"`
    newFormulasValue = newFormulasValue.replace(fieldName, fieldToLabelMap.value[fieldName])
  })
  dialogVisible.value = false
  // 保存公式
  emit('update:modelValue', {
    formulas: formulasValue.value,
    formulasText: newFormulasValue,
    formulasParams: formulasParams
  })
}

const createMarkElement = (text: string) => {
  const span = document.createElement('span')
  span.textContent = text
  span.style.backgroundColor = '#265fcc'
  span.style.padding = '2px 4px'
  span.style.borderRadius = '2px'
  span.style.color = '#fff'
  return span
}

const handleFieldNodeClick = (data) => {
  if (!data.isField) return
  const cursor = codemirrorEditor.value?.cminstance.getCursor()
  codemirrorEditor.value?.cminstance.replaceRange(data.id, cursor)
  const from = { line: cursor.line, ch: cursor.ch }
  const to = { line: cursor.line, ch: cursor.ch + data.id.length }

  codemirrorEditor.value?.cminstance.markText(from, to, {
    replacedWith: createMarkElement(fieldToLabelMap.value[data.id] || data.id)
  })
  codemirrorEditor.value?.cminstance.focus()
}

// 添加 watch 以处理 formulaValue 变化
watch(
  () => formulasValue,
  () => {
    if (dialogVisible.value) {
      nextTick(() => {
        codemirrorVisible.value = true
      })
    }
  },
  {
    deep: true,
    immediate: true
  }
)

watch(
  () => filterVariable.value,
  () => {
    VariableTreeRef.value!.filter(filterVariable.value.toLocaleLowerCase())
  }
)

watch(
  () => filterFormulaText.value,
  () => {
    FormulaTreeRef.value!.filter(filterFormulaText.value)
  }
)

const handleVariableFilterNode = (value: string, data: any) => {
  if (!value) return true
  return data.label.toLocaleLowerCase().includes(value)
}

const handleFormulasFilterNode = (value: string, data: any) => {
  if (!value) return true
  return data.formulaFunName.toLocaleLowerCase().includes(value.toLocaleLowerCase())
}

const getChildrenWidgetList = (
  subTableData: any,
  templateId: string,
  subParentTableData: string
) => {
  if (subTableData.type === 'DrTableForm') {
    return getSubTableData(subTableData, templateId, subParentTableData)
  } else if (subTableData.type === 'DrRelateCard') {
    return subTableData.props.relatedValue.rules.map((relatedWidget) => {
      return {
        id: `"${templateId}@@${subTableData._fc_id}@@${relatedWidget._fc_id}"`,
        label: getWidgetTitle(relatedWidget),
        replaceLabel: `${subParentTableData}.${getWidgetTitle(relatedWidget)}`,
        type: relatedWidget.type,
        isField: !['DrTableForm', 'DrRelateCard'].includes(relatedWidget.type),
        children: getSubTableData(
          relatedWidget,
          templateId,
          `${subParentTableData}.${getWidgetTitle(relatedWidget)}`
        )
      }
    })
  } else {
    return []
  }
}

/**
 * 渲染子表
 * @param subTableData  子表控件
 * @param templateId 模版id
 * @param subParentTableData 子表前缀标题
 */
const getSubTableData = (subTableData: any, templateId: string, subParentTableData: string) => {
  return subTableData.children && subTableData.children.length > 0
    ? allowedUseSubColumnWidgetList(subTableData).map((child) => {
        return {
          id: `"${templateId}@@${subTableData._fc_id}@@${child._fc_id}"`,
          label: child.title,
          replaceLabel: `${subParentTableData}.${child.title}`,
          type: child.type,
          isField: true
        }
      })
    : []
}

const allowedUseSubColumnWidgetList = (subTableData: any) => {
  return allowedUseWidgetList(
    [...subTableData.children.slice(1)].map((child) => {
      return child.children[0]
    })
  )
}

const allowedUseWidgetList = (widgetJsonList: any) => {
  const widgetList =
    widgetJsonList && widgetJsonList instanceof Array
      ? widgetJsonList.filter((widget) =>
          [
            'DrInput',
            'DrTextarea',
            'DrInputNumber',
            'DrPercentage',
            'DrRadio',
            'DrCheckbox',
            'DrDatePicker',
            'DrLocation',
            'DrRate',
            'DrFormulas',
            'DrRelateCard',
            'DrTableForm'
          ].includes(widget.type)
        )
      : []
  return widgetList
}

const getWidgetTitle = (item) => {
  return item.type === 'DrRelateCard' ? '关联引用' : item.title
}

const flatTemplateList = computed(() => {
  const list: any = []
  const widgetId = injectDesigner.setupState.activeRule._fc_id
  const mainFormTemplate = injectionData?.templateTreeData.value.find(
    (template) => template.formType === 'MAIN_FORM'
  )
  const msFormTemplate = injectionData?.templateTreeData.value.filter(
    (template) => template.formType === 'MILESTONE'
  )

  const currentFormType = injectionData?.formType.value
  const currentReplyFormType = injectionData?.msFormType.value
  const currentWidgetJsonList = injectDesigner.setupState.getFormDescription()

  list.push({
    label: mainFormTemplate.name,
    id: mainFormTemplate.id,
    children: allowedUseWidgetList(
      getRelateFieldFromTree(
        currentFormType === 'MAIN_FORM'
          ? currentWidgetJsonList
          : mainFormTemplate.widgetJsonList || [],
        widgetId,
        false
      )
    ).map((item) => {
      return {
        id:
          item.type === 'DrFormulas'
            ? `"Formulas@@${item._fc_id}"`
            : `"${mainFormTemplate.id}@@${item._fc_id}"`,
        replaceLabel: `${mainFormTemplate.name}.${getWidgetTitle(item)}`,
        label: getWidgetTitle(item),
        isField: !['DrTableForm', 'DrRelateCard'].includes(item.type),
        type: item.type,
        children: getChildrenWidgetList(
          item,
          mainFormTemplate.id,
          `${mainFormTemplate.name}.${getWidgetTitle(item)}`
        )
      }
    })
  })

  for (let msForm of msFormTemplate) {
    if (msForm.replyForm) {
      const widgetJsonList =
        currentFormType === 'MILESTONE' &&
        currentReplyFormType === 'REPLY_TO_MILESTONE' &&
        injectionData?.replyFormId.value === msForm.replyForm.id
          ? currentWidgetJsonList
          : msForm.replyForm.widgetJsonList || []
      list.push({
        label: `里程碑批复-${msForm.replyForm.name}`,
        id: msForm.replyForm.id,
        children: allowedUseWidgetList(getRelateFieldFromTree(widgetJsonList, widgetId, false)).map(
          (item) => {
            return {
              id:
                item.type === 'DrFormulas'
                  ? `"Formulas@@${item._fc_id}"`
                  : `"${msForm.replyForm.id}@@${item._fc_id}"`,
              replaceLabel: `${msForm.replyForm.name}.${getWidgetTitle(item)}`,
              label: getWidgetTitle(item),
              isField: !['DrTableForm', 'DrRelateCard'].includes(item.type),
              type: item.type,
              children: getChildrenWidgetList(
                item,
                msForm.replyForm.id,
                `${msForm.replyForm.name}.${getWidgetTitle(item)}`
              )
            }
          }
        )
      })
    }
  }

  // 工单
  for (let msForm of msFormTemplate) {
    let childrenList: any = []
    for (let form of msForm.formList) {
      console.log(currentFormType)
      const widgetJsonList =
        currentFormType === 'FORM' && injectionData.currentFormId.value === form.id
          ? currentWidgetJsonList
          : form.widgetJsonList || []

      childrenList.push({
        label: `${form.name}${injectionData.currentFormId.value === form.id ? '(当前表单)' : ''}`,
        id: form.id,
        children: [
          ...allowedUseWidgetList(getRelateFieldFromTree(widgetJsonList, widgetId, false)).map(
            (item) => {
              return {
                id:
                  item.type === 'DrFormulas'
                    ? `"Formulas@@${item._fc_id}"`
                    : `"${form.id}@@${item._fc_id}"`,
                replaceLabel: `${form.name}.${getWidgetTitle(item)}`,
                label: getWidgetTitle(item),
                isField: !['DrTableForm', 'DrRelateCard'].includes(item.type),
                type: item.type,
                children: getChildrenWidgetList(
                  item,
                  form.id,
                  `${form.name}.${getWidgetTitle(item)}`
                )
              }
            }
          )
        ]
      })
    }
    if (msForm.workOrderReplyForm) {
      const widgetJsonList =
        currentFormType === 'MILESTONE' &&
        currentReplyFormType === 'REPLY_TO_WORK_ORDER' &&
        injectionData?.replyFormId.value === msForm.workOrderReplyForm.id
          ? currentWidgetJsonList
          : msForm.workOrderReplyForm.widgetJsonList || []

      childrenList.push({
        label: `工单批复-${msForm.workOrderReplyForm.name}`,
        id: msForm.workOrderReplyForm.id,
        children: allowedUseWidgetList(getRelateFieldFromTree(widgetJsonList, widgetId, false)).map(
          (item) => {
            return {
              id:
                item.type === 'DrFormulas'
                  ? `"Formulas@@${item._fc_id}"`
                  : `"${msForm.workOrderReplyForm.id}@@${item._fc_id}"`,
              replaceLabel: `${msForm.workOrderReplyForm.name}.${getWidgetTitle(item)}`,
              label: getWidgetTitle(item),
              isField: !['DrTableForm', 'DrRelateCard'].includes(item.type),
              type: item.type,
              children: getChildrenWidgetList(
                item,
                msForm.workOrderReplyForm.id,
                `${msForm.workOrderReplyForm.name}.${getWidgetTitle(item)}`
              )
            }
          }
        )
      })
    }
    list.push({
      label: msForm.name,
      id: msForm.id,
      children: childrenList
    })
  }

  return list
})

const fieldToLabelMap = computed(() => {
  const list = getFieldToLabelMap(flatTemplateList.value)
  return list.reduce((acc, cur) => {
    acc[cur.id] = cur.replaceLabel
    return acc
  }, {})
})

const getFieldToLabelMap = (list) => {
  const map: { id: string; replaceLabel: string }[] = []
  list.forEach((item) => {
    if (item.isField) {
      map.push({
        id: item.id,
        replaceLabel: item.replaceLabel
      })
    } else if (item.children?.length > 0) {
      const res = getFieldToLabelMap(item.children)
      map.push(...res)
    }
  })
  return map
}

const formulaFunObject = ref<any>({})

const nodeOver = (data) => {
  formulaFunObject.value.formulaName = data.formulaFunName || ''
  formulaFunObject.value.formulaTips = data.formulaTips || ''
  formulaFunObject.value.formulaExample = data.formulaInfo || ''
}

const handleFuncNodeClick = (data) => {
  if (data.key) return

  codemirrorEditor.value?.cminstance.replaceRange(
    data.formulaFunName + '()',
    codemirrorEditor.value.cminstance.getCursor()
  )
  codemirrorEditor.value?.cminstance.moveH(-1, 'char')
  codemirrorEditor.value?.cminstance.focus()
}
</script>

<style scoped lang="scss">
.formulas-editor {
  width: 100%;

  .dialog-body {
    width: 100%;

    .codemirror-header {
      display: flex;
      align-items: center;
      background: #f5f7fa;
      padding: 10px 15px;
      margin-bottom: 10px;
      border-radius: 6px;
      font-size: 12px;

      .codemirror-title {
        color: #333;
        font-weight: bolder;
        font-size: 22px;
        margin-right: 5px;
      }
    }

    .codemirror-top {
      display: flex;
      justify-content: space-between;
      margin-bottom: 6px;

      .codemirror-top-left {
        flex: 1;
        font-size: 14px;
        color: #666;
      }

      .codemirror-top-right {
        flex: 1;
        text-align: right;
      }
    }

    .codemirror-editer {
      width: 100%;
    }

    .slider-container {
      width: 100%;
      display: flex;
      justify-content: space-between;
      flex-wrap: nowrap;
      gap: 10px;
    }

    .field-tree,
    .formula-tree {
      flex-shrink: 0;
      width: 250px;
      height: 300px;
      overflow-y: auto;

      .hover-node {
        width: 100%;
      }
    }

    .field-tree {
      width: 400px;

      .field-span-row {
        position: absolute;
        right: 5px;
        height: 18px;
        line-height: 18px;
        border-radius: 8px;
        border: 1px solid #265fcc;
        background: #ecf5ff;
        color: #265fcc;
        font-size: 12px;
        padding: 0 6px;
      }
    }

    .hover-tips {
      flex: 1;

      .hover-tips-content {
        background-color: #f2f2f2;
        border-radius: 8px;
        padding: 10px;

        p {
          margin: 0;
        }
      }
    }
  }
}
</style>
