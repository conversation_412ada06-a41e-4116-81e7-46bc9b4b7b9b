<template>
  <div class="qcode">
    <div class="qcode_header flex-row">
      <div class="header_left">体系文件信息查询</div>
      <div class="header_right">
        <img src="@/assets/imgs/u3.png" style="width: 150px;" />
      </div>
    </div>
    <el-card>
      <div class="qcode_content">
        <p class="qcode_name">{{ qcodeInfo.fileName }}</p>
        <p class="qcode_version">{{ qcodeInfo.fileCode }}</p>
        <p class="qcode_version">{{ qcodeInfo.fileVersion }}</p>
        <p class="file_control">{{ qcodeInfo.label }}</p>
        <div class="file_status"><span>{{ qcodeInfo.fileStatusLabel }}</span></div>
      </div>
    </el-card>
  </div>

</template>

<script lang="ts" setup>
import { FileQrcodeApi } from '@/api/system/fileqrcode'

const { query } = useRoute() // 查询参数
const Type = query.type
const qcodeInfo = ref({
  fileCode: '',
  fileName: '',
  fileStatusLabel: '',
  fileControlTypeLabel: '',
  fileControlTypeExternalLabel: '',
  fileVersion: '',
  label: ''
})
const getFileQrcodeInfo = async (id?: String) => {
  const data = await FileQrcodeApi.getFileQrcodeInfo(String(id))
  qcodeInfo.value = data
  qcodeInfo.value.label = Number(Type) === 1 ? data.fileControlTypeLabel : data.fileControlTypeExternalLabel
}
onMounted(() => {
  getFileQrcodeInfo(String(query.id))
  document.title = '信息查询'
})

</script>

<style lang="scss" scoped>
.qcode {
  padding: 20px;

  .qcode_header {
    align-items: center;
    margin-bottom: 40px;

    .header_left {
      width: 50%;
      font-weight: 700;
      font-style: normal;
      font-size: 18px;
      color: #555555;
      text-align: left;
      font-family: '微软雅黑 Bold', '微软雅黑 Regular', '微软雅黑', sans-serif;
    }

    .header_right {
      width: 50%;
      text-align: right;
    }
  }

  .qcode_content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;

    .qcode_name {
      font-size: 18px;
      color: #555555;
    }

    .qcode_version {
      font-size: 16px;
      color: #7F7F7F;
    }

    .file_control {
      font-size: 16px;
      font-weight: 600;
      color: green
    }

    .file_status {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      height: 96px;
      width: 300px;
      background: inherit;
      background-color: rgba(242, 242, 242, 1);
      border: none;
      border-radius: 8px;

      span {
        font-size: 28px;
        color: rgba(236, 128, 141, 0.996078431372549);
      }
    }
  }
}
</style>
