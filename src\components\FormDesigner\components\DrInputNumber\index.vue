<template>
  <div class="fc-dr-input-number">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <div class="model-value">{{ formatModelValue }}</div>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <div class="wrapper" :class="unitPosition">
        <span v-if="unit && unitPosition === 'before'" class="unit" :class="unitPosition">{{
          unit
        }}</span>
        <el-input-number v-model="modelValue" :min="min" :max="max" :precision="precision" :controls="!unit"
          :placeholder="placeholder" />
        <span v-if="unit && unitPosition === 'after'" class="unit" :class="unitPosition">{{
          unit
        }}</span>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { isNumber } from 'lodash-es'
import { formatNumberWithCommas } from '../../utils'
import { ModelValueType } from '../common/DrDefaultValue/types'

type PropsType = {
  formCreateInject: any
  disabled?: boolean
  min?: number
  max?: number
  precision?: number
  unit?: string
  unitPosition: 'before' | 'after'
  placeholder?: string
  useThousandSeparator: boolean
  defaultValueConfig?: ModelValueType
}

const props = withDefaults(defineProps<PropsType>(), {
  disabled: false,
  unit: '',
  placeholder: '请输入'
})

const modelValue = defineModel<number>({ required: false, default: undefined })

watch(() => modelValue.value, () => {
  // 初始化默认值
  if (
    !modelValue.value &&
    props.defaultValueConfig &&
    props.defaultValueConfig.type !== 'none' &&
    props.defaultValueConfig.content
  ) {
    modelValue.value = Number(props.defaultValueConfig.content)
  }
}, {
  deep: true,
  immediate: true
})


const formatModelValue = computed(() => {
  if (!isNumber(modelValue.value)) {
    return '-'
  }
  if (props.useThousandSeparator) {
    return formatWithUnit(formatNumberWithCommas(modelValue.value))
  }
  return formatWithUnit(modelValue.value)
})

const formatWithUnit = (value) => {
  if (props.unit) {
    if (props.unitPosition === 'before') {
      return `${props.unit} ${value}`
    }
    if (props.unitPosition === 'after') {
      return `${value} ${props.unit}`
    }
  }
  return value
}
</script>
<style scoped lang="scss">
.fc-dr-input-number {
  width: 100%;
  display: flex;

  .wrapper {
    position: relative;
    display: flex;
    align-items: center;

    &.before {
      :deep(.el-input__wrapper) {
        padding-left: 30px;
      }
    }

    &.after {
      :deep(.el-input__wrapper) {
        padding-right: 30px;
      }
    }

    .unit {
      position: absolute;

      z-index: 1;
      font-weight: 600;

      &.before {
        left: 10px;
      }

      &.after {
        right: 10px;
      }
    }
  }
}
</style>
