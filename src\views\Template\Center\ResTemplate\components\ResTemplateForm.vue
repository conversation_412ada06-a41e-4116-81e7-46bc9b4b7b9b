<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item :label="t('admin_template_name')" prop="resTemplateName">
        <el-input v-model="formData.resTemplateName" :placeholder="t('admin_common_inputText') " maxlength="100"
          show-word-limit />
      </el-form-item>
      <el-form-item :label="t('admin_template_icon')" prop="icon">
        <ChooseIcon :colorList="iconData.colorList" :iconList="iconData.iconList" v-model:icon="formData.icon"
          v-model:color="formData.iconColor" />
      </el-form-item>
      <el-form-item :label="t('admin_template_typeCode')" prop="typeCode">
        <el-select v-model="formData.typeCode" :placeholder="t('admin_common_selectText')" size="large"
          style="width: 240px">
          <el-option v-for="item in getStrDictOptions(DICT_TYPE.SYSTEM_TEMPLATE_CREATE_TYPE)" :key="item.value"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('admin_template_industry')" prop="industryId">
        <el-select v-model="formData.industryId" :placeholder="t('admin_common_selectText')" size="large"
          style="width: 240px">
          <el-option v-for="item in props.industryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('admin_common_description')" prop="description">
        <el-input v-model="formData.description" type="textarea" :rows="2" autosize show-word-limit maxlength="200"
          :placeholder="t('admin_common_inputText')" />
      </el-form-item>
      <el-form-item :label="t('admin_common_remark')" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :rows="2" autosize show-word-limit maxlength="200"
          :placeholder="t('admin_common_inputText')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('admin_common_sure') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('admin_common_cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { ResTemplateApi, ResTemplateVO } from '@/api/template/resTemplate'
import { ValidUtil } from '@/utils/validateUtils';
import { FormInstance, FormRules } from 'element-plus';

/** 运营端行业管理 表单 */
defineOptions({ name: 'ResTemplateForm' })

const props = defineProps({
  industryOptions: [] as any[], // 行业列表
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: Ref<ResTemplateVO> = ref({} as ResTemplateVO)
const formRules = reactive<FormRules>({
  resTemplateName: [
    { required: true, message: t('admin_common_inputText'), trigger: 'blur' },
    { min: 1, max: 100, message: t('admin_rule_lengthRules', [1, 100]), trigger: 'blur' }
  ],
  icon: [{ required: true, message: t('admin_common_selectText'), trigger: 'change' }],
  typeCode: [{ required: true, message: t('admin_common_selectText'), trigger: 'change' }],
  industryId: [{ required: true, message: t('admin_common_selectText'), trigger: 'change' }]
})
const formRef: Ref<FormInstance | undefined> = ref() // 表单 Ref

const iconData = {
  colorList: ['#00AAFF', '#21CA70', '#ECBE0B', '#0089FF', '#5B73FE', '#F26B53'],
  iconList: ['link-clothing', 'link-process-center', 'link-brands', 'link-footwear', 'link-resource-monitor', 'link-supplier', 'link-fabric', 'link-factory']
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await ResTemplateApi.getResTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as ResTemplateVO
    if (formType.value === 'create') {
      await ResTemplateApi.createResTemplate(data)
      message.success(t('admin_common_createSuccess'))
    } else {
      await ResTemplateApi.updateResTemplate(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {} as ResTemplateVO
  formRef.value?.resetFields()
}

</script>