import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrSignature'
const label = '签名'

const Rule = {
  //插入菜单位置
  menu: 'extendAdvanced',
  //图标
  icon: 'icon-value',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      title: label,
      field: uniqueId(),
      $required: false,
      props: {},
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return []
  }
}

export default Rule
