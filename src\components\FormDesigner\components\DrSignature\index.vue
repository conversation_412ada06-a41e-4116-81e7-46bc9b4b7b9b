<template>
  <div class="dr-signature">
    <img v-if="modelValue" class="signature-image" :src="modelValue" alt="signature" />
    <el-button :icon="EditPen" @click="openSignatureDialog">{{
      modelValue ? '重新签名' : '添加签名'
    }}</el-button>

    <el-dialog v-model="dialogVisible" title="签名" width="800">
      <SignaturePad ref="SignaturePadRef" />
      <template #footer>
        <el-row class="row-bg" justify="space-between">
          <el-col :span="12">
            <div class="footer-tool">
              <div class="is-save-button" @click="toggleIsSave">
                <el-checkbox label="保存签名，下次使用" :checked="isSave" />
              </div>
              <img
                v-if="savedSignature"
                class="saved-signature-image"
                :src="savedSignature"
                alt="signature"
                @click="useSavedSignature"
              />
              <div v-if="savedSignature" @click="deleteSavedSignature">
                <el-icon><Delete /></el-icon>
              </div>
            </div>
          </el-col>
          <el-col :span="12">
            <div>
              <el-button @click="closeSignatureDialog">取消</el-button>
              <el-button type="primary" @click="getSignaturePadData"> 确认 </el-button>
            </div>
          </el-col>
        </el-row>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { EditPen, Delete } from '@element-plus/icons-vue'
import SignaturePad from './SignaturePad.vue'
import localforage from 'localforage'

const modelValue = defineModel<string>({ required: false, default: undefined })

const dialogVisible = ref<boolean>(false)

const openSignatureDialog = () => {
  getSignature()
  dialogVisible.value = true
}

const closeSignatureDialog = () => {
  SignaturePadRef.value?.clear()
  dialogVisible.value = false
}

const SignaturePadRef = ref<any>(null)

const getSignaturePadData = () => {
  const data = SignaturePadRef.value?.toDataURL()
  modelValue.value = data

  if (isSave.value) {
    saveSignature(data)
  }

  closeSignatureDialog()
}

const savedSignature = ref<string | null>(null)

const isSave = ref(false)
const toggleIsSave = () => {
  isSave.value = !isSave.value
}

const useSavedSignature = () => {
  modelValue.value = savedSignature.value || ''
  closeSignatureDialog()
}

const getSignature = async () => {
  try {
    const res = await localforage.getItem<string>('drSignature')
    savedSignature.value = res
  } catch (error) {
    console.log(error)
  }
}

const saveSignature = (value: string) => {
  localforage.setItem('drSignature', value)
}
const deleteSavedSignature = () => {
  savedSignature.value = null
  localforage.removeItem('drSignature')
}
</script>

<style scoped lang="scss">
.dr-signature {
  width: 100%;

  .signature-image {
    width: 100px;
    height: 70px;
    border: 1px solid #f2f2f2;
    border-radius: 4px;
    display: block;
    margin-bottom: 10px;
  }

  .row-bg {
    padding: 0 20px;
  }

  .footer-tool {
    display: flex;
    align-items: center;
    gap: 10px;

    .is-save-button {
      display: flex;
      align-items: center;
      height: 32px;
      background: #ffffff;
      border: 1px solid #dddddd;
      border-radius: 4px;
      padding: 0 10px;
    }

    .saved-signature-image {
      width: 88px;
      height: 32px;
      background: #f5f7fa;
      border: 1px solid #e0dfe4;
      border-radius: 4px;
    }
  }
}
</style>
