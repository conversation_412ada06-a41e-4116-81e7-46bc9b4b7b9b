<template>
  <div class="dr-layout-select">
    <el-row :gutter="6">
      <el-col
        v-for="span in colSpanOptions"
        :key="span"
        :span="8"
        style="margin-bottom: 6px"
        @click="selectSpan(span)"
      >
        <div class="layout-option" :class="{ selected: modelValue === span }">
          <div v-for="num in (24/span)" :key="num" class="col-span"></div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
type PropsType = {
  modelValue?: number
}

defineProps<PropsType>()

const emit = defineEmits(['update:modelValue', 'change'])

const colSpanOptions = ref([24, 12, 8, 6, 4])

const selectSpan = (span: number) => {
  emit('update:modelValue', span)
  emit('change', span)
}
</script>

<style scoped lang="scss">
.dr-layout-select {
  width: 100%;

  .layout-option {
    width: 100%;
    display: flex;
    justify-content: space-between;
    padding: 5px;
    border-radius: 4px;
    border: 1px solid #ddd;
    gap: 5px;
    box-sizing: border-box;
    cursor: pointer;

    &.selected {
      border-color: #2e73ff;
    }

    .col-span {
      flex: 1;
      height: 50px;
      background-color: #ddd;
      border-radius: 4px;
    }
  }
}
</style>
