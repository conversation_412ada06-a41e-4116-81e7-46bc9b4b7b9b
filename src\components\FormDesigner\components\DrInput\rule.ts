import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrInput'
const label = '单行文本'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-input',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {
        defaultValueConfig: { type: 'none', content: '' },
        placeholder: '请输入',
        scanType: [],
        scanEdit: false
      }
    }
  },
  //自定义组件的属性配置
  props(data, { t }) {
    return [
      {
        type: 'DrDefaultValue',
        field: 'defaultValueConfig',
        title: '默认内容',
        props: {
          currentField: data.field,
          componentName: name,
          currentWidgetId: data._fc_id
        }
      },
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("placeholderEn", langValue);
            api.activeRule.props["placeholderEn"] = langValue;
          },
        },
      },
      {
        type: 'checkbox',
        title: '扫码录入（仅手机端支持）',
        field: 'scanType',
        options: [
          {
            label: '二维码',
            value: 'qrcode'
          },
          {
            label: '条形码',
            value: 'barcode'
          }
        ]
      },
      {
        type: 'switch',
        title: '允许扫码修改内容',
        field: 'scanEdit',
      }
    ]
  }
}

export default Rule
