<template>
  <ContentWrap :title="t('admin_user_teamDetail')">
    <!-- 基础信息 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_basicInfo') }}
      </div>
      <div class="menu-content mt-12px">
        <el-descriptions :column="4" size="default" direction="vertical">
          <el-descriptions-item :label="t('admin_user_avatar')" :span="1">
            <el-image v-if="pageData.teamAvatar" :src="pageData.teamAvatar" class="h-80px w-80px"
              @click="imagePreview(pageData.teamAvatar)" />
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item :label="t('admin_common_name')" :span="1">{{ pageData.teamName || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_teamShortName')" :span="1">{{ pageData.teamShortName || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_teamCode')" :span="1">{{ pageData.teamCode || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_user_teamOwnerName')" :span="1">
            {{ pageData.teamOwnerName || '-' }}
          </el-descriptions-item>
          <el-descriptions-item :label="t('admin_template_creator')">{{ pageData.creatorName || '-'
            }}</el-descriptions-item>
          <el-descriptions-item :label="t('admin_common_createTime')">{{ formatDate(pageData.createTime) || '-'
            }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </div>
    <!-- 成员信息 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_memberInfo') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.teamMemberList?.length || 0 }} {{ t('admin_user_people') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.memberColumns" :data="pageData.teamMemberList">
        </Table>
      </div>
    </div>
    <!-- 角色列表 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_roleData') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.teamRoleList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.roleColumns" :data="pageData.teamRoleList">
        </Table>
      </div>
    </div>
    <!-- 本团队邀请的协作团队 -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_inviteTeam') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.inviteTeamList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.inviteTeamColumns" :data="pageData.inviteTeamList">
        </Table>
      </div>
    </div>
    <!-- 本团队参与外部协作的团队  -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_invitedTeam') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.inviteOurTeamList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.inviteOurTeamColumn" :data="pageData.inviteOurTeamList">
        </Table>
      </div>
    </div>
    <!-- 本团队的SCCS -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_teamSccsList') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.sccsList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.sccsListColumns" :data="pageData.sccsList">
        </Table>
      </div>
    </div>
    <!--  本团队参与的外部协作SCCS -->
    <div class="menu mb-50px">
      <div class="menu-title flex items-center font-700"><span
          class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
        {{ t('admin_user_coopSccsList') }}
        <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
          pageData.coopSccsList?.length || 0 }} {{ t('admin_user_individual') }}</span>
      </div>
      <div class="menu-content mt-12px">
        <Table :columns="tableState.coopSccsListColumn" :data="pageData.coopSccsList">
        </Table>
      </div>
    </div>

  </ContentWrap>
</template>
<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import businessConfig from './index.config'
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { TeamApi, TeamVO } from '@/api/user/team'
import { createImageViewer } from '@/components/ImageViewer'

/** 运营端行业管理 表单 */
defineOptions({ name: 'TeamDetail' })

const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false)
const teamId = ref('')
const pageData: any = ref({}) // 页面数据
const tableState = reactive({
  memberColumns: businessConfig.detailMemberTableThs,
  roleColumns: businessConfig.detailRoleTableThs,
  inviteTeamColumns: businessConfig.detailInviteTeamListTableThs,
  inviteOurTeamColumn: businessConfig.detailInviteOurTeamListTableThs,
  coopSccsListColumn: businessConfig.detailCoopSccsListTableThs,
  sccsListColumns: businessConfig.detailSccsListTableThs
})


const getDetail = async () => {
  loading.value = true
  try {
    pageData.value = await TeamApi.getTeam(teamId.value)
  } finally {
    loading.value = false
  }
}
const init = async () => {
  teamId.value = route.query.id as string
  getDetail()
}
init()

const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}


</script>
<style scoped lang="scss">
:deep(.el-descriptions__label) {
  font-weight: 700 !important;
}

:deep(.el-descriptions__cell) {
  width: 25% !important;
  padding: 0 10px;
  vertical-align: top;
}
</style>
