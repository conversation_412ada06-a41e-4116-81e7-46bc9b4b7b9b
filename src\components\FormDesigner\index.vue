<template>
  <div class="form-designer">
    <fc-designer ref="designer" :config="config" :menu="MenuList" :locale="locale" height="100%" />
  </div>
</template>

<script setup lang="ts">
import fcDesigner from '@/plugins/fcDesignerPro/core/index.es'
// @ts-ignore
import Zh from '@/plugins/fcDesignerPro/locale/zh-cn.es'
// @ts-ignore
import En from '@/plugins/fcDesignerPro/locale/en.es'
import { cloneDeep } from 'lodash-es'
import { onMounted, onBeforeUnmount, PropType, ref, nextTick } from 'vue'
import { useLocaleStore } from '@/store/modules/locale'
import init, { MenuList, unInit } from './init'
import { formCreateInjectionData } from './inject'
// import { Rule } from '@form-create/element-ui'

const currentFormId = ref<string>('')
const msId = ref<string>('')
const templateTreeData = ref<any[]>([])
const formType = ref<string>('')
const msFormType = ref<string>('')
const formVisibleSettingList = ref<any>([])
const msSccsId = ref<string>('')
const replyFormId = ref<string>('')

const props = defineProps({
  designerReadonly: {
    type: Boolean as PropType<any>,
    default: false
  }
})

const setInjectionData = (data: {
  currentFormId: string
  templateTreeData: any[]
  formType: string
  msFormType: string
  formVisibleSettingList: any[]
  sccsId: string
  msId: string
  replyFormId: string
}) => {
  console.log(data)
  currentFormId.value = data.currentFormId
  templateTreeData.value = cloneDeep(data.templateTreeData)
  formType.value = data.formType
  msFormType.value = data.msFormType
  formVisibleSettingList.value = data.formVisibleSettingList
  msSccsId.value = data.sccsId
  replyFormId.value = data.replyFormId
  msId.value = data.msId
}

const localeStore = useLocaleStore()
const currentLocale = computed(() => localeStore.getCurrentLocale)

const localeModules = {
  'zh-CN': Zh,
  'en-US': En
}

const locale = computed(() => localeModules[currentLocale.value.lang])

const designer = ref<any>(null)

const config = {
  switchType: false,
  showFormConfig: true,
  appendConfigData: ['formVisibleSetting'],
  denyDrag: {
    col: {
      item: ['DrCard', 'DrTableForm', 'DrTabs']
    }
  },
  formRule: {
    prepend: false,
    rule() {
      return [
        {
          type: 'DrFormVisibleSetting',
          field: 'formVisibleSetting',
          title: ''
        }
      ]
    }
  },
  formOptions: {
    submitBtn: false,
    form: {
      labelPosition: 'top'
    }
  },
  showBaseForm: true,
  showAdvancedForm: false,
  showStyleForm: false,
  showEventForm: false,
  showDevice: true,
  showControl: false,
  hiddenItemConfig: {
    default: ['field']
  },
  validateRule: () => {
    return [
      {
        type: 'Required',
        field: '$required',
        title: '是否必填',
        on: {
          updateI18n(api, data, langValue) {
            api.setData('requiredEn', langValue)
            api.activeRule.props['requiredEn'] = langValue
          }
        }
      }
    ]
  }
}

onMounted(() => {
  // 挂载拖拽规则
  init(designer)
})

onBeforeUnmount(() => {
  unInit(designer)
})

const setRule = (rule: string) => {
  designer.value.setRule(rule)

  // 设置新规则后，记录原始状态
  nextTick(() => {
    setOriginalFormData()
  })
}

const getRule = (isJson: boolean = false) => {
  if (isJson) {
    return designer.value.getJson()
  }
  return designer.value.getRule()
}

const getOption = () => {
  return designer.value.getOption()
}

const setOption = (opt: any) => {
  return designer.value.setOption(opt)
}

// 新增：表单变更状态管理
const originalFormData = ref('')

// 设置原始表单数据
const setOriginalFormData = () => {
  if (!designer.value) return

  try {
    originalFormData.value = JSON.stringify({
      rule: designer.value.getRule(),
      option: designer.value.getOption()
    })
  } catch (error) {
    console.warn('设置原始表单数据失败:', error)
    originalFormData.value = ''
  }
}

// 新增：检查表单数据是否已修改
const hasChanged = () => {
  if (!designer.value || !originalFormData.value) return false

  try {
    const currentData = JSON.stringify({
      rule: designer.value.getRule(),
      option: designer.value.getOption()
    })

    return currentData !== originalFormData.value
  } catch (error) {
    console.warn('检查表单变更状态失败:', error)
    return false
  }
}

// 新增：清除修改状态
const clearChangeStatus = () => {
  setOriginalFormData()
}

defineExpose({
  setRule,
  getRule,
  getOption,
  setOption,
  setInjectionData,
  hasChanged,
  clearChangeStatus
})

provide(designer, designer.value)
provide(formCreateInjectionData, {
  currentFormId,
  templateTreeData,
  replyFormId,
  formType,
  msFormType,
  formVisibleSettingList,
  msSccsId,
  msId,
  designerReadonly: props.designerReadonly
}) // 将业务中数据注入设计器内，提供给内部组件使用
</script>

<style scoped lang="scss">
.form-designer {
  height: 100%;
}
</style>
