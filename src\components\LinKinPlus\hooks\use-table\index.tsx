//表格配置
import { shallowRef, shallowReactive, watch, onMounted, nextTick } from 'vue'
import type { ResType } from '../types'

/**
 * 默认表格操作
 * @param fetch 请求数据方法
 * @param props 参数
 * @param immediate 是否立即请求
 * @returns
 */
function useTable<D>(fetch: (val: object) => Promise<ResType<D>>, props: object, immediate: boolean = true) {
  const selection = shallowRef([])
  const handleSelectionChange = (val: []) => {
    selection.value = val
  }

  const sortState = shallowRef({})
  const handleSortChange = ({ prop, order }: { prop: string; order: string }) => {
    sortState.value = prop ? { [`${prop}_sort`]: order === 'ascending' } : {}
    getTableData()
  }
  //表格配置
  const tableState = shallowReactive<{
    loading: boolean
    data: D[]
    [key: string]: any
  }>({
    loading: false,
    data: [],
    ...props,
    onSortChange: handleSortChange,
    onSelectionChange: handleSelectionChange
  })
  //分页配置
  const pagination = shallowReactive({
    currentPage: 1,
    pageSize: 20,
    total: 0,
    background: true,
    'page-sizes': [10, 20, 30, 40, 50],
    layout: 'sizes, total, prev, pager, next, jumper',
    'onUpdate:page-size': (v: number) => {
      pagination.pageSize = v
      pagination.currentPage = 1
    },
    'onUpdate:current-page': (v: number) => (pagination.currentPage = v)
  })

  const indexMethod = (index: number) => {
    return (pagination.currentPage - 1) * pagination.pageSize + index + 1
  }
  const serialColumn = <el-table-column label='序号' type='index' align='center' index={indexMethod} width={80} />

  const getMaxPage = () => {
    // total变换后page也会变化，但此时仍在loading
    const { currentPage: page, pageSize: size, total } = pagination
    if (tableState.data.length === 0 && page > 1)
      nextTick().then(() => {
        pagination.currentPage = Math.max(1, Math.min(Math.ceil(total / size), page - 1))
      })
  }
  //表格数据请求
  const getTableData = () => {
    if (tableState.loading) return
    tableState.loading = true
    selection.value = []
    const { currentPage: page, pageSize: size } = pagination
    return fetch?.({ ...sortState.value, page, size })
      .then((res: ResType<D>) => {
        tableState.data = res?.data ?? res?.items ?? res?.list ?? ([] as D[])
        pagination.total = res?.total ?? tableState.data.length
        getMaxPage()
      })
      .catch(() => {
        tableState.data = []
        pagination.total = 0
      })
      .finally(() => {
        tableState.loading = false
      })
  }

  watch([() => pagination.currentPage, () => pagination.pageSize], getTableData)
  immediate && onMounted(getTableData)

  return {
    selection,
    tableState,
    getTableData,
    serialColumn,
    pagination
  }
}

export default useTable
