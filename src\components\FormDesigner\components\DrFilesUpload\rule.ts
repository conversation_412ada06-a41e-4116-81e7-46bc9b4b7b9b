import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrFilesUpload'
const label = '附件'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-upload',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {},
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'inputNumber',
        title: '最大上传数量',
        field: 'limit',
        props: {
          min: 1,
          precision: 0
        }
      },
      {
        type: 'inputNumber',
        title: '单个附件最大上传大小（MB）',
        field: 'maxFileSize',
        props: {
          min: 1,
          precision: 0,
          max: 50
        }
      },
      {
        type: 'select',
        title: '文件类型（不设置则支持所有格式）',
        field: 'accept',
        props: {
          multiple: true,
          filterable: true,
          allowCreate: true,
          defaultFirstOption: true
        },
        options: [
          { label: 'xls', value: 'xls' },
          { label: 'xlsx', value: 'xlsx' },
          { label: 'pdf', value: 'pdf' },
          { label: 'doc', value: 'doc' },
          { label: 'docx', value: 'docx' },
          { label: 'csv', value: 'csv' },
          { label: 'xml', value: 'xml' },
        ]
      }
    ]
  }
}

export default Rule
