import { rules } from './components/index'

// Object.keys(components).forEach((key) => {
//   const BLACK_LIST = ['DrTableFormView', 'DrRelateCardView']
//   if (key === 'DrTableForm') {
//     fcDesigner.component(key, components[key], components['DrTableFormView'])
//   } else if (key === 'DrRelateCard') {
//     fcDesigner.component(key, components[key], components['DrRelateCardView'])
//   } else if (!BLACK_LIST.includes(key)) {
//     fcDesigner.component(key, components[key])
//   }
// })

export const MenuList = [
  {
    //菜单名
    title: '布局组件',
    //菜单id
    name: 'extendLayout',
    //拖拽组件列表
    list: []
  },
  {
    //菜单名
    title: '基础组件',
    //菜单id
    name: 'extendBase',
    //拖拽组件列表
    list: []
  },
  {
    //菜单名
    title: '高级组件',
    //菜单id
    name: 'extendAdvanced',
    //拖拽组件列表
    list: []
  }
]

const init = (designer: any) => {
  for (const rule of rules) {
    designer.value.addComponent(rule);
  }
}

export const unInit = (designer: any) => {
  for (const rule of rules) {
    designer.value.removeMenuItem(rule);
  }
}

export default init
