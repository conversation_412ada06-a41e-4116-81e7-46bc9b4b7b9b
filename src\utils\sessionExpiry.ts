import { useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache('sessionStorage')


// 设置缓存
export const setWithExpiry = (key, value, ttl) => {
  const now = new Date();
  // ttl是过期时间（单位：毫秒）
  const item = {
      value: value,
      expiry: now.getTime() + (ttl * 1000),
  };
  wsCache.set(key, JSON.stringify(item));
}

// 获取缓存
export const getWithExpiry = (key) => {
  const itemStr = wsCache.get(key);
  if (!itemStr) {
      return null;
  }
  const item = JSON.parse(itemStr);
  const now = new Date();
  if (now.getTime() > item.expiry) {
    wsCache.delete(key);
      return null;
  }
  return item.value;
}