<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="节点" prop="node">
        <el-input v-model="queryParams.node" placeholder="请输入节点" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="对应模块" prop="type">
        <el-select v-model="queryParams.type" placeholder="请选择对应模块" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.COMMON_SERVER_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="简体中文" prop="zhCn">
        <el-input v-model="queryParams.zhCn" placeholder="请输入简体中文" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="英语" prop="enUs">
        <el-input v-model="queryParams.enUs" placeholder="请输入英语" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="繁体中文" prop="zhTw">
        <el-input v-model="queryParams.zhTw" placeholder="请输入繁体中文" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="是否为空" prop="language">
        <el-select v-model="queryParams.language" placeholder="请选择对应语言" clearable class="!w-240px"
          @change="handleSelectLanguage">
          <el-option v-for="dict in languageOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['infra:internalization:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleExport" :loading="exportLoading"
          v-hasPermi="['infra:internalization:export']">
          <Icon icon="ep:download" class="mr-5px" /> 导出
        </el-button>
        <el-button type="warning" @click="handleInitialization">
          <Icon icon="ep:refresh" class="mr-5px" /> 初始化
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border>
      <el-table-column label="节点" align="center" prop="node" />
      <el-table-column label="对应模块" align="center" prop="type">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_SERVER_TYPE" :value="scope.row.type" />
        </template>
      </el-table-column>
      <el-table-column label="简体中文" align="center" prop="zhCn" />
      <el-table-column label="英语" align="center" prop="enUs" />
      <el-table-column label="繁体中文" align="center" prop="zhTw" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.node)"
            v-hasPermi="['infra:internalization:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.node)"
            v-hasPermi="['infra:internalization:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <InternalizationForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import download from '@/utils/download'
import { InternalizationApi, InternalizationVO } from '@/api/infra/internalization'
import InternalizationForm from './InternalizationForm.vue'

/** 国际化翻译管理 列表 */
defineOptions({ name: 'Internalization' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<InternalizationVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  type: undefined,
  zhCn: undefined,
  enUs: undefined,
  zhTw: undefined,
  language: undefined,
  createTime: [],
  node: undefined
})
const languageOptions = ref([
  { label: '简体中文', value: 'zh-CN' },
  { label: '繁體中文', value: 'zh-TW' },
  { label: '英文', value: 'en-US' },
])
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 初始化 */
const handleInitialization = async () => {
  try {
    await InternalizationApi.Initialization()
    message.success(t('admin_common_dealSuccess'))
  } catch (e) {
    console.log(e)
  }
  // todo
}

/** 过滤出为空的语言 */
const handleSelectLanguage = async () => {
  if (queryParams.language === '') {
    resetQuery()
    return
  }
  try {
    const data = await InternalizationApi.languageIsNull(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await InternalizationApi.getInternalizationPage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, node?: string) => {
  formRef.value.open(type, node)
}

/** 删除按钮操作 */
const handleDelete = async (node: string) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await InternalizationApi.deleteInternalization(node)
    message.success(t('admin_common_delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 导出按钮操作 */
const handleExport = async () => {
  try {
    // 导出的二次确认
    await message.exportConfirm()
    // 发起导出
    exportLoading.value = true
    const data = await InternalizationApi.exportInternalization(queryParams)
    download.excel(data, '国际化翻译管理.xls')
  } catch {
  } finally {
    exportLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
