<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item v-if="formType == 'copy'" :label="t('admin_template_info')" prop="bizTemplateInfo">
        <span style="color:darkgrey">{{ t('admin_template_milestone') }}:{{ formData.milestoneNumber }}
          &nbsp; {{ t('admin_template_form') }}:{{ formData.formNumber }}
        </span>
      </el-form-item>
      <el-form-item :label="t('admin_template_name')" prop="bizTemplateName">
        <el-input v-model="formData.bizTemplateName" :placeholder="t('admin_common_inputText')" maxlength="100"
          show-word-limit />
      </el-form-item>
      <el-form-item :label="t('admin_template_icon')" prop="icon">
        <ChooseIcon :colorList="iconData.colorList" :iconList="iconData.iconList" v-model:icon="formData.icon"
          v-model:color="formData.iconColor" />
      </el-form-item>
      <el-form-item :label="t('admin_template_typeCode')" prop="typeCode">
        <el-select v-model="formData.typeCode" :placeholder="t('admin_common_selectText')" size="large"
          style="width: 240px">
          <el-option v-for="item in getStrDictOptions(DICT_TYPE.SYSTEM_TEMPLATE_CREATE_TYPE)" :key="item.value"
            :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('admin_template_industry')" prop="industryId">
        <el-select v-model="formData.industryId" :placeholder="t('admin_common_selectText')" size="large"
          style="width: 240px">
          <el-option v-for="item in props.industryOptions" :key="item.value" :label="item.label" :value="item.value" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('admin_common_description')" prop="description">
        <el-input v-model="formData.description" type="textarea" :rows="2" autosize show-word-limit maxlength="200"
          :placeholder="t('admin_common_inputText')" />
      </el-form-item>
      <el-form-item :label="t('admin_common_remark')" prop="remark">
        <el-input v-model="formData.remark" type="textarea" :rows="2" autosize show-word-limit maxlength="200"
          :placeholder="t('admin_common_inputText')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('admin_common_sure') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('admin_common_cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
import { BizTemplateApi, BizTemplateVO } from '@/api/template/bizTemplate'
import { FormInstance, FormRules } from 'element-plus';
import { ValidUtil } from '@/utils/validateUtils';
import { SccsBizTemplateApi } from '@/api/user/sccs';

/** 运营端业务模板库 表单 */
defineOptions({ name: 'BizTemplateForm' })
const props = defineProps({
  industryOptions: [] as never[], // 行业列表
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const isSccsTemplate = ref(false) //是否是sccs模板 
const formData: Ref<BizTemplateVO> = ref({} as BizTemplateVO)
const formRules = reactive<FormRules>({
  bizTemplateName: [
    { required: true, message: t('admin_common_inputText'), trigger: 'blur' },
    { min: 1, max: 100, message: t('admin_rule_lengthRules', [1, 100]), trigger: 'blur' }
  ],
  icon: [{ required: true, message: t('admin_common_selectText'), trigger: 'change' }],
  typeCode: [ValidUtil.validRequiredForm()],
  industryId: [ValidUtil.validRequiredForm()],
})
const formRef: Ref<FormInstance | undefined> = ref() // 表单 Ref

const iconData = {
  colorList: ['#00AAFF', '#21CA70', '#ECBE0B', '#0089FF', '#5B73FE', '#F26B53'],
  iconList: ['link-clothing', 'link-process-center', 'link-brands', 'link-footwear', 'link-resource-monitor', 'link-supplier', 'link-fabric', 'link-factory']
}


/** 打开弹窗 */
const open = async (type: string, id?: number, isSccs?: boolean) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  if (isSccs && isSccs == true) {
    isSccsTemplate.value = true;
  }
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      if (isSccsTemplate.value) {
        formData.value = await SccsBizTemplateApi.getBizTemplate(id)
      } else {
        formData.value = await BizTemplateApi.getBizTemplate(id)
      }
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as BizTemplateVO
    if (formType.value === 'create') {
      data['id'] = ''
      await BizTemplateApi.createBizTemplate(data)
      message.success(t('admin_common_createSuccess'))
    } else if (formType.value === 'copy') {
      if (isSccsTemplate.value) {
        await SccsBizTemplateApi.copyBizTemplate(data)
      } else {
        await BizTemplateApi.copyBizTemplate(data)
      }
      message.success(t('admin_common_copySuccess'))
    }
    else {
      await BizTemplateApi.updateBizTemplate(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {} as BizTemplateVO
  formRef.value?.resetFields()
}
</script>
