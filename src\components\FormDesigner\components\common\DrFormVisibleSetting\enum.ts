// 运算符
export const MathOperatorOptions = {
  widget: [
    {
      label: "等于",
      value: "EQ"
    },
    {
      label: "不等于",
      value: "NE"
    },
    {
      label: "包含",
      value: "IN"
    },
    {
      label: "不包含",
      value: "NOT_IN"
    },
    {
      label: "为空",
      value: "IS_NULL"
    },
    {
      label: "不为空",
      value: "IS_NOT_NULL"
    }
  ],
  number: [
    {
      label: "=",
      value: "EQ"
    },
    {
      label: "≠",
      value: "NE"
    },
    {
      label: "<",
      value: "LT"
    },
    {
      label: ">",
      value: "GT"
    },
    {
      label: "≤",
      value: "LE"
    },
    {
      label: "≥",
      value: "GE"
    },
    {
      label: "为空",
      value: "IS_NULL"
    },
    {
      label: "不为空",
      value: "IS_NOT_NULL"
    }
  ],
  date: [
    {
      label: "等于",
      value: "EQ"
    },
    {
      label: "早于",
      value: "BEFORE"
    },
    {
      label: "晚于",
      value: "AFTER"
    },
    {
      label: "在范围内",
      value: "BETWEEN"
    },
    {
      label: "为空",
      value: "IS_NULL"
    },
    {
      label: "不为空",
      value: "IS_NOT_NULL"
    }
  ],
  empty: [
    {
      label: "为空",
      value: "IS_NULL"
    },
    {
      label: "不为空",
      value: "IS_NOT_NULL"
    }
  ]
};

export const FieldTypeSelectOptions = [
  {
    label: "自定义",
    value: "CUSTOM"
  },
  {
    label: "其他字段",
    value: "OTHER_FIELD"
  }
];

// 日期控件范围类型
export const DateRangeOptions = [
  {
    label: "指定范围",
    value: "DATE_RANGE"
  },
  {
    label: "过去",
    value: "LAST"
  },
  {
    label: "未来",
    value: "NEXT"
  },
  {
    label: "本周",
    value: "THIS_WEEK"
  },
  {
    label: "上周",
    value: "LAST_WEEK"
  },
  {
    label: "下周",
    value: "NEXT_WEEK"
  },
  {
    label: "本月",
    value: "THIS_MONTH"
  },
  {
    label: "上月",
    value: "LAST_MONTH"
  },
  {
    label: "下月",
    value: "NEXT_MONTH"
  },
  {
    label: "今年",
    value: "THIS_YEAR"
  },
  {
    label: "去年",
    value: "LAST_YEAR"
  },
  {
    label: "明年",
    value: "NEXT_YEAR"
  }
];

// 日期控件早于、晚于,等于类型
export const DatePickerOptions = [
  {
    label: "指定日期",
    value: "ASSIGN"
  },
  {
    label: "前",
    value: "BEFORE"
  },
  {
    label: "后",
    value: "AFTER"
  },
  {
    label: "今天",
    value: "TODAY"
  },
  {
    label: "昨天",
    value: "YESTERDAY"
  },
  {
    label: "明天",
    value: "TOMORROW"
  },
  {
    label: "本月",
    value: "THIS_MONTH"
  },
  {
    label: "上月",
    value: "LAST_MONTH"
  },
  {
    label: "下月",
    value: "NEXT_MONTH"
  },
  {
    label: "今年",
    value: "THIS_YEAR"
  },
  {
    label: "去年",
    value: "LAST_YEAR"
  },
  {
    label: "明年",
    value: "NEXT_YEAR"
  }
];

// 日期控件早于、晚于,等于类型
export const DayPickerOptions = [
  {
    label: "前",
    value: "BEFORE"
  },
  {
    label: "后",
    value: "AFTER"
  },
  {
    label: "当天",
    value: "TODAY"
  }
];
