import LinKinTip from '@/components/LinKinTip/index.vue'

const { t } = useI18n() // 国际化


const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: t('admin_user_teamName'),
      prop: "teamName",
    },
    {
      label: t('admin_user_teamShortName'),
      prop: "teamShortName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_user_inviteTeamNumber'),
      prop: "teamMemberNumber",
    },
    {
      label: t('admin_user_teamOwnerName'),
      prop: "teamOwnerName",
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.teamOwnerName + '(' + row.teamOwnerAccount + ')'}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_user_inviteTeam'),
      prop: "inviteTeamNumber",
    },
    {
      label: t('admin_user_invitedTeam'),
      prop: "invitedTeamNumber",
    },
    {
      label: 'SCCS',
      prop: "sccsNumber"
    },
    {
      label: t('admin_user_sccsCoop'),
      prop: "sccsCoopNumber",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    }
  ],
  tableBtnList: [
    { label: t('admin_common_detail'), key: "detail", type: "primary", link: true },
    { label: t('admin_common_delect'), key: 'delete', type: 'primary', link: true }
  ],
  searchConfig: [
    //头部筛选数据
    {
      label: t('admin_user_searchInput'),
      placeholder: t('admin_user_teamSearchTip'),
      prop: 'keyword'
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': "-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    }
  ],
  searchBtnList: [],
  detailMemberTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_memberAccount'),
      prop: "account",
    },
    {
      label: t('admin_user_memberName'),
      prop: "username",
    },
    {
      label: t('admin_user_role'),
      prop: "teamRoleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.teamRoleList?.length > 0 ? (
                row.teamRoleList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', data.manager ? 'bg-#eef8ff' : 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color={data.manager ? '#0070d2' : '#808080'}
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.roleName}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_user_registerTime'),
      prop: "registerTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_username'),
      prop: "inviteUsername",
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  detailRoleTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_roleName'),
      prop: "roleName",
      width: '280'
    },
    {
      label: t('admin_user_teamMemberNumber'),
      prop: "teamMemberNumber",
      width: '150'
    },
    {
      label: t('admin_user_memberList'),
      prop: "teamRoleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.teamMemberList?.length > 0 ? (
                row.teamMemberList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.account + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    }
  ],
  detailInviteTeamListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_teamName'),
      prop: "teamName"
    },
    {
      label: t('admin_user_teamShortName'),
      prop: "teamShortName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_username'),
      prop: "account",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
              <LinKinTip
                color='#808080'
                placement='top-start'
                content={row.username + ' (' + row.account + ') '}
              ></LinKinTip>
            </div>
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  detailInviteOurTeamListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_teamName'),
      prop: "teamName"
    },
    {
      label: t('admin_user_teamShortName'),
      prop: "teamShortName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_username'),
      prop: "account",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
              <LinKinTip
                color='#808080'
                placement='top-start'
                content={row.username + ' (' + row.account + ') '}
              ></LinKinTip>
            </div>
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  detailSccsListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_sccsName'),
      prop: "sccsName"
    },
    {
      label: t('admin_user_sccsCode'),
      prop: "sccsCode",
    },
    {
      label: t('admin_user_memberNumber'),
      prop: "memberNumber",
    },
    {
      label: t('admin_user_sccsMember'),
      prop: "memberInfo",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.memberInfo?.length > 0 ? (
                row.memberInfo.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.account + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_user_coopTeamNumber'),
      prop: "coopTeamNumber",
    },
    {
      label: t('admin_user_coopTeamInfo'),
      prop: "coopTeamInfo",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_template_creator'),
      prop: "account",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
              <LinKinTip
                color='#808080'
                placement='top-start'
                content={row.creatorUserName + ' (' + row.creatorUserAccount + ') '}
              ></LinKinTip>
            </div>
          </div>
        }
      },
      'min-width': 180
    }
  ],
  detailCoopSccsListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_sccsName'),
      prop: "sccsName"
    },
    {
      label: t('admin_user_sccsCode'),
      prop: "sccsCode",
    },
    {
      label: t('admin_user_sccsTeamName'),
      prop: "teamName",
    },
    {
      label: t('admin_user_sccsCoopTeamNumber'),
      prop: "coopTeamNumber",
    },
    {
      label: t('admin_user_sccsMember'),
      prop: "memberInfo",
      'show-overflow-tooltip': false,
      'min-width': 280,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.memberInfo?.length > 0 ? (
                row.memberInfo.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'flex', 'items-center', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    {data.isDocking ? <div class={['color-#409eff', 'mr-3px', 'min-w-50px', 'leading-20px']}>对接人</div> : ''}
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.account + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      }
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    }
  ]
}

export default fixedParameter