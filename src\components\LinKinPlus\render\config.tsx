/*
 * @Description: renderComponent配置文件
 */

import type { ComponentProps } from '../hooks/types'
import {
  childrenCompNameMap,
  compNameMap,
  compPropsMap,
  date,
  el_date_picker,
  el_input,
  el_select,
  number,
  textarea
} from './static'

/**
 * @msg: 根据父组件名称，获取特定子组件名称
 * @param {string} name element组件名称
 * @return {*} 返回特定子组件名称
 */
const getChildrenComponentName = (name: string): string => childrenCompNameMap.get(name) || el_input

/**
 * @msg: 获取组件名称
 * @param {any} props 属性对象
 * @return {*} 返回组件名称
 */
const getComponentName = (props: ComponentProps): string => {
  const { componentName, type, options } = props
  return (
    componentName ||
    (type && compNameMap.get(type)) ||
    (type?.includes(date) ? el_date_picker : Array.isArray(options) ? el_select : el_input)
  )
}

/**
 * @msg: 获取需要合并的属性
 * @param {string} name 组件名称
 * @param {string} type 组件类型
 * @return {*} 返回要合并的属性对象
 */
const getCompProps = (name: string, type?: string): object => {
  // 参数归一化
  if (type === number || type === textarea) name = type
  return compPropsMap.get(name)?.(type) ?? {}
}

export { getChildrenComponentName, getComponentName, getCompProps }
