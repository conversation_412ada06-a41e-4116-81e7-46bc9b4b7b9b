import is from '@form-create/utils/lib/type'
import { cloneDeep } from 'lodash'

export const localeOptions = (t, options) => {
  return options.map((opt) => {
    // opt.label = t((prefix || 'props') + '.' + (opt.label || opt.value)) || opt.label;
    opt.label = opt.label
    return opt
  })
}

export const localeProps = (t, prefix, rules) => {
  return rules.map((rule) => {
    if (rule.field === 'formCreate$required') {
      rule.title = t('validate.required') || rule.title
    } else if (rule.field && rule.field !== '_optionType') {
      rule.title = t('com.' + prefix + '.' + rule.field) || rule.title
    }
    if (rule.type === 'template' && is.trueArray(rule.children)) {
      rule.children = localeProps(t, prefix, rule.children)
    }
    return rule
  })
}

// 格式化千分位
export const formatNumberWithCommas = (number: number) => {
  return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',')
}

export const currencyOptions = [
  'CNY', // 人民币
  'USD', // 美元
  'EUR', // 欧元
  'JPY', // 日元
  'GBP', // 英镑
  'AUD', // 澳元
  'CAD', // 加元
  'CHF', // 瑞士法郎
  'HKD', // 港币
  'SGD', // 新加坡元
  'INR', // 印度卢比
  'KRW', // 韩元
  'ZAR', // 南非兰特
  'MOP', // 澳门元
  'php', // 菲律宾比索
  'THB', // 泰铢
  'NZD', // 新西兰元
  'DKK', // 丹麦克朗
  'NOK', // 挪威克朗
  'SEK', // 瑞典克朗
  'RUB', // 俄罗斯卢布
  'MYR' // 马来西亚林吉特
]

/**
 * 从表单JSON中，筛选出能够作为关联的字段
 */
export const getRelateFieldFromTree = (
  widgetJsonList: any[],
  widgetId?: string,
  relateCardBool?: boolean
) => {
  // 不能作为关联字段的黑名单列表
  const blackList = ['DrDivider', 'DrPlaceholder']
  if (relateCardBool) {
    blackList.push('DrRelateCard')
  }
  // 布局组件列表，如果是布局组件，通过递归深入查找
  const layoutList = ['DrCard', 'col', 'elTabs', 'elTabPane']

  const result: any[] = []

  for (const widget of widgetJsonList) {
    if (blackList.includes(widget.type)) continue
    if (layoutList.includes(widget.type)) {
      const childWidgetList =
        getRelateFieldFromTree(widget.children || [], widgetId, relateCardBool) || []
      result.push(...childWidgetList)
      continue
    }
    if (widget.type === 'DrTableForm') {
      const tableWidget = cloneDeep(widget)
      // delete tableWidget['children']
      result.push(tableWidget)
      continue
    }
    result.push(widget)
  }
  console.log('getRelateFieldFromTree', widgetJsonList, result)
  return widgetId ? result.filter((widget) => widget._fc_id !== widgetId) : result
}
