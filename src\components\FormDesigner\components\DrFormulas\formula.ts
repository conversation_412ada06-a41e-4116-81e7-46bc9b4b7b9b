export const formulaData = [
  {
    key: 'math',
    formulaFunName: "数字函数",
    children: [
      {
        formulaFunName: "SUM",
        formulaInfo: "SUM(语文成绩,数学成绩, 英语成绩)返回三门课程的总分",
        formulaTips: "SUM函数可以获取一组数值的总和"
      },
      {
        formulaFunName: "AVERAGE",
        formulaInfo: "AVERAGE(语文成绩,数学成绩, 英语成绩)返回三门课程的平均分",
        formulaTips: "AVERAGE函数可以获取一组数值的算术平均值"
      },
      {
        formulaFunName: "MAX",
        formulaInfo: "MAX(瑕疵数,不合格数,合格数)返回三个数中最大值",
        formulaTips: "MAX函数可以获取一组数值的最大值"
      },
      {
        formulaFunName: "MIN",
        formulaInfo: "MIN(瑕疵数,不合格数,合格数)返回三个数中最小值",
        formulaTips: "MIN函数可以获取一组数值的最小值"
      },
      {
        formulaFunName: "CEILING",
        formulaInfo: "CEILING(7,6)，返回12，12比7大的同时，也是6的倍数中最接近7的数字",
        formulaTips: "CEILING函数可以将数字增大到最接近原值的指定因数的倍数"
      },
      {
        formulaFunName: "FLOOR",
        formulaInfo: "FLOOR(7,6)，返回6，6比7小的同时，也是6的倍数中最接近7的数字",
        formulaTips: "FLOOR函数可将数字减小到最接近原值的指定因数的倍数"
      },
      {
        formulaFunName: "INT",
        formulaInfo: "INT(3.1415)，返回3",
        formulaTips: "INT函数可以获取一个数的整数部分"
      },
      {
        formulaFunName: "SUMIF",
        formulaInfo: `SUMIF(子表单.水果种类, "苹果", 子表单.数字)，可对子表单中水果种类填的是苹果的对应数字进行求和；SUMIF([A,B,C,D,A], "A", [1,1,1,1,1])，可得到A种类对应的数字求和结果为2。`,
        formulaTips: "使满足单一条件的数字相加并返回和。"
      },
      {
        formulaFunName: "SUMIFS",
        formulaInfo: `SUMIFS(子表单.数字, 子表单.水果种类, "苹果", 子表单.细分种类, "红富士")，可对子表单中水果种类填的是苹果并且细分种类填的是红富士的对应数字进行求和；SUMIFS([1,1,1,1,1], [A,B,C,D,A], "A", [a,b,c,d,e], "a")，可得到既满足A种类又满足a种类对应的数字求和结果为1。`,
        formulaTips: "使同时满足多个条件的数字相加并返回和。"
      },
      {
        formulaFunName: "COUNTIF",
        formulaInfo: `COUNTIF(子表单.性别, "女")，可得到子表单中性别填的是"女"的数据条数；COUNTIF([1,2,3,4], ">3")，可得到1,2,3,4中大于2的数字数量，结果为1。`,
        formulaTips: "获取数组中满足条件的参数个数。"
      },
      {
        formulaFunName: "COUNTIFS",
        formulaInfo: `COUNTIFS(子表单.性别, "女", 子表单.城市, "厦门")，可得到子表单中性别填的是"女"且子表单中城市是"厦门"的数据条数；COUNTIFS([1,2,3,4], ">2", [1,2,3,4], ">3")，可得到结果为1。`,
        formulaTips: "获取数组中满足多个条件的参数个数。"
      },
      {
        formulaFunName: "COUNTA",
        formulaInfo: "COUNTA(子表单.性别)，可得到子表单中性别已填写的条数。",
        formulaTips: "获取数组中非空值的数量。"
      },
    ]
  },
  {
    key: 'string',
    formulaFunName: "文本函数",
    children: [
      {
        formulaFunName: "CONCATENATE",
        formulaInfo: `CONCATENATE("三年二班", "周杰伦")返回"三年二班周杰伦"`,
        formulaTips: "CONCATENATE函数可以将多个文本合并成一个文本"
      },
      {
        formulaFunName: "TEXTJOIN",
        formulaInfo: `TEXTJOIN("、",TRUE,"三年二班","周杰伦","方文山")返回"三年二班、周杰伦、方文山"`,
        formulaTips: "TEXTJOIN函数可将多个文本项合并为一个文本值，并指定分隔符"
      },
      {
        formulaFunName: "LEFT",
        formulaInfo: `LEFT("三年二班周杰伦", 2)返回"三年"，也就是"三年二班周杰伦"的从左往右的前2个字符`,
        formulaTips: "LEFT函数可以从一个文本的第一个字符开始返回指定个数的字符"
      },
      {
        formulaFunName: "RIGHT",
        formulaInfo: `RIGHT("三年二班周杰伦", 3)返回"周杰伦"，也就是"三年二班周杰伦"从右往左的前3个字符"`,
        formulaTips: "RIGHT函数可以获取由给定文本右端指定数量的字符构成的文本值"
      },
      {
        formulaFunName: "SEARCH",
        formulaInfo: `SEARCH("increase", "Linkincrease")返回5`,
        formulaTips: "SEARCH函数可以获取文本1在文本2中的开始位置"
      },
    ]
  },
  {
    key: 'date',
    formulaFunName: "时间函数",
    children: [
      {
        formulaFunName: "DAYS",
        formulaInfo: "",
        formulaTips: "DAYS函数可以返回两个日期之间相差的天数。"
      },
      {
        formulaFunName: "WORKDAY",
        formulaInfo: "",
        formulaTips: "WORKDAY函数可以获取给定日期之前或者之后指定工作日数的日期，工作日不包括周末和任何其他指定日期"
      },
      {
        formulaFunName: "DATEDIF",
        formulaInfo: `DATEDIF(开始时间,结束时间,[单位])，单位可以是 "y" 、"M"、"d"`,
        formulaTips: `DATEDIF(下单时间, 送达时间, "d")，如果下单时间是2020/9/30，送达时间为2020/10/1，计算得到的天数差为1。`
      },
      {
        formulaFunName: "NETWORKDAYS",
        formulaInfo: "",
        formulaTips: "NETWORKDAYS可以获取两个日期之间的工作日数，工作日不包括周末和任何其他指定日期。"
      },
      {
        formulaFunName: "YEAR",
        formulaInfo: "",
        formulaTips: "YEAR(时间戳), YEAR函数可以返回某日期的年份"
      },
      {
        formulaFunName: "MONTH",
        formulaInfo: "",
        formulaTips: "MONTH(时间戳), MONTH返回某日期的月份",
      },
      {
        formulaFunName: "DAY",
        formulaInfo: "",
        formulaTips: "DAY(时间戳), DAY函数可以获取某日期是当月的第几日",
      },
      {
        formulaFunName: "HOUR",
        formulaInfo: "",
        formulaTips: "HOUR(时间戳), HOUR函数可以返回某日期的小时数",
      },
      {
        formulaFunName: "TODAY",
        formulaInfo: "",
        formulaTips: "TODAY(), 返回今天的日期（年月日），但不会精确到时分秒（默认为00:00:00）。如果想要精确到时分秒，请使用函数NOW。",
      },
      {
        formulaFunName: "MINUTE",
        formulaInfo: "",
        formulaTips: "MINUTE(时间戳), MINUTE函数可以返回某日期的分钟数",
      },
      {
        formulaFunName: "SECOND",
        formulaInfo: "",
        formulaTips: "SECOND(时间戳), SECOND函数可以返回某日期的秒数",
      },
    ]
  },
  {
    key: 'condition',
    formulaFunName: "逻辑函数",
    children: [
      {
        formulaFunName: "IF",
        formulaInfo: `IF(语文成绩>60, "及格", "不及格")，当语文成绩>60时返回及格，否则返回不及格`,
        formulaTips: "IF(逻辑表达式, 为true时返回的值, 为false时返回的值)"
      },
      {
        formulaFunName: "IFS",
        formulaInfo: `IFS(语文成绩>90, "优秀", 语文成绩>80, "良好", 语文成绩>=60, "及格", 语文成绩<60, "不及格")，根据成绩返回对应的评价`,
        formulaTips: "IFS(逻辑表达式1, 逻辑表达式1为true返回该值, 逻辑表达式2, 逻辑表达式2为true返回该值, ...)"
      },
      {
        formulaFunName: "AND",
        formulaInfo: `AND(语文成绩>90, 数学成绩>90, 英语成绩>90)，如果三门课成绩都> 90，返回true，否则返回false`,
        formulaTips: "AND(逻辑表达式1, 逻辑表达式2, ...)"
      },
      {
        formulaFunName: "OR",
        formulaInfo: "OR(语文成绩>90, 数学成绩>90, 英语成绩>90)，任何一门课成绩> 90，返回true，否则返回false",
        formulaTips: "OR(逻辑表达式1, 逻辑表达式2, ...)"
      },
    ]
  }
]

export const widgetFormula = {
  DrInput: "单行文本",
  DrTextarea: "多行文本",
  DrInputNumber: '数字',
  DrPercentage: "百分比",
  DrRadio: "单选",
  DrCheckbox: "多选",
  DrDatePicker: "日期时间",
  DrImagesUpload: "图片",
  DrFilesUpload: "附件",
  DrLocation: "定位",
  DrAddress: "地址",
  DrEditor: "富文本",
  DrRate: "评分",
  DrFormulas: "公式",
  DrSCCSMemberSelect: "SCCS成员",
  DrSCCSGroupMemberSelect: "SCCS协作",
  DrSignature: "签名",
  DrExchangeRates: "汇率",
  DrRelateCard: "关联引用"
}
