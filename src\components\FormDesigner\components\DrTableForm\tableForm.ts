import unique from '@form-create/utils/lib/unique'
import { formCreateInjectionData } from '../../inject'

const label = '子表'
const name = 'DrTableForm'

export default {
  menu: 'extendLayout',
  icon: 'icon-table-form',
  label,
  name,
  drag: false,
  mask: false,
  validate: [],
  //定义组件的事件
  event: [],
  subForm: 'array',
  children: 'DrTableFormColumn',
  childrenLen: 0,
  rule({ t }) {
    return {
      type: name,
      field: unique(),
      // title: t('com.tableForm.name'),
      title: label,
      $required: false,
      props: {
        autoRelate: true,
        addButtonText: '添加'
      },
      children: []
    }
  },
  props(_, { t }) {
    return [
      // {
      //   //修改rule.children[0]
      //   type: 'input',
      //   title: '内容',
      //   field: 'formCreateChild'
      // },
      {
        type: 'LanguageInput',
        field: 'addButtonText',
        title: '添加按钮(贸易端添加行数据按钮名称)',
        props: {
          maxlength: 30,
          showWordLimit: true
        },
        on: {
          updateI18n(api, data, langValue) {
            api.setData("addButtonTextEn", langValue);
            api.activeRule.props["addButtonTextEn"] = langValue;
          },
        },
      },
      {
        type: 'switch',
        title: '自动引用全部关联数据',
        field: 'autoRelate',
        props: {}
      },
      {
        type: 'DrTableFormColumnsEditor',
        field: 'columns',
        props: {}
      }
    ]
  }
}
