import { reactive } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormStateType } from '../types'

export default function useFormDialog<D>(onSubmit: (val: object) => Promise<string | any>, props: FormStateType<D>) {
  const formState = reactive({
    visible: false,
    title: '',
    'onUpdate:visible': (v: boolean) => {
      formState.visible = v
    },
    data: {},
    ...props,
    onSubmit: () =>
      onSubmit(formState.data)?.then(msg => {
        ElMessage.success(msg ?? '操作成功')
        formState.visible = false
      })
  })

  return formState
}
