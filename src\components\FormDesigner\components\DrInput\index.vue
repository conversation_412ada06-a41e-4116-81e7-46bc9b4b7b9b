<template>
  <div class="fc-dr-input">
    <el-input
      v-model="modelValue"
      :disabled="disabled"
      style="width: 100%"
      :placeholder="placeholder"
      clearable
    />
  </div>
</template>
<script setup lang="ts">
import { ModelValueType } from '../common/DrDefaultValue/types'

type PropsType = {
  disabled?: boolean
  placeholder: string
  defaultValueConfig?: ModelValueType
}
const modelValue = defineModel<string>({ required: false, default: '' })

const props = withDefaults(defineProps<PropsType>(), {
  placeholder: '请输入',
  disabled: false
})

// 初始化默认值
if (
  !modelValue.value &&
  props.defaultValueConfig &&
  props.defaultValueConfig.type !== 'none' &&
  props.defaultValueConfig.content
) {
  modelValue.value = props.defaultValueConfig.content
}
</script>
<style scoped>
.fc-dr-input {
  width: 100%;
}
</style>
