import { DICT_TYPE, getIntDictOptions, getBoolDictOptions } from '@/utils/dict'
import { render } from 'nprogress'
import { DictTag } from '@/components/DictTag'

const { t } = useI18n() // 国际化

const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: t('admin_template_industryId'),
      prop: "id",
    },
    {
      label: t('admin_template_industryName'),
      prop: "industryName",
    },
    {
      label: t('admin_template_industryImg'),
      prop: "icon",
      formatter: (row)=>{
        return <i style={{ color: row.iconColor, display: 'inline-block', 'font-size': '26px', margin: '2px 0px ' }} class={['iconfont', row.icon]}></i>
      }
    },
    {
      label: t('admin_template_industryTemplate'),
      prop: "templateNumber",
    },
    {
      label: t('admin_template_creator'),
      prop: "creatorName",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_common_updateTime'),
      prop: "updateTime",
      type: "datetimerange",
      sortable: 'custom',
      'min-width': 165
    },
    {
      label: t('admin_template_status'),
      slotName: 'isShow'
    },
    {
      label: t('admin_common_sort'),
      prop: "sort",
    },
  ],
  tableBtnList: [
    { label: t('admin_common_edit'), key: "edit", type: "primary", link: true },
    { label: t('admin_common_delect'), key: "delete", type: "primary", link: true },
  ],
  searchConfig: [
    //头部筛选数据
    { 
      label: t('admin_template_industryName'),
      prop: 'industryName'
    },
    {
      label: t('admin_common_isShow'),
      prop: 'isShow',
      type: 'select',
      options: getIntDictOptions(DICT_TYPE.COMMON_ENABLED)
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator':"-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time':[new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    }
  ],
  searchBtnList: [
    {
      label: t('admin_common_add'),
      key: 'add',
      icon: 'ep:plus',
      elProps: {
        type: 'primary',
      }
    }
  ]
}

export default fixedParameter