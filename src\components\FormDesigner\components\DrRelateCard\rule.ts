import { localeProps } from '../../utils'
import uniqueId from '@form-create/utils/lib/unique'

const label = '关联引用'
const name = 'DrRelateCard'

export default {
  menu: 'extendAdvanced',
  icon: 'icon-json',
  label,
  name,
  mask: true,
  style: false,
  sfc: false,
  rule() {
    return {
      field: uniqueId(),
      title: label,
      type: name,
      props: {
        relatedValue: {
          formId: '',
          rules: []
        },
        colSpan: 24,
        relateButtonText: '引用数据'
      }
    }
  },
  props(_, { t }) {
    return localeProps(t, name + '.props', [
      {
        type: 'DrRelateRuleTable',
        field: 'relatedValue',
        title: '关联字段'
      },
      {
        type: 'LanguageInput',
        field: 'relateButtonText',
        title: '自定义按钮名',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("relateButtonTextEn", langValue);
            api.activeRule.props["relateButtonTextEn"] = langValue;
          },
        },
      },
      {
        type: 'DrLayoutSelect',
        field: 'colSpan',
        title: '布局'
      },
    ])
  }
}
