import Query from '../../components/Query.vue'
import Table from '../../components/Table.vue'
import FormDialog from '../../components/FormDialog.vue'
import { type UnwrapNestedRefs, reactive, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import useFormDialog from '../use-form-dialog'
import TableClass from '../use-table/TableClass'
import type { FormStateType, ComponentProps, ResType } from '../types'
import type { RequestType, PageProps } from './types'

//删除弹窗
const defaultDeleteProps = {
  message: '确定删除所选的数据吗？',
  title: '确认删除',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  customClass: 'delete-dialog',
  dangerouslyUseHTMLString: true,
  center: true
}

class PageClass<D = any> {
  columns: UnwrapNestedRefs<ComponentProps<D>[]>
  queryState: UnwrapNestedRefs<{ data: {}; columns: ComponentProps<D>[] }>
  formState: UnwrapNestedRefs<FormStateType<D>>
  operateColumn: JSX.Element | null
  tableState: TableClass<D>
  pageClass: string[]
  handleCommand: (type: string, data?: D, index?: number) => void
  prefix?: () => JSX.Element
  API: {
    get: (...arg: any[]) => Promise<ResType<D>>
    post?: RequestType
    put?: RequestType
    delete?: RequestType
    [key: string]: RequestType | undefined
  }
  deleteProps: object

  constructor({
    pageClass = [],
    API = {
      get: () => Promise.resolve({ data: [], total: 0 }),
      post: () => Promise.resolve(),
      put: () => Promise.resolve(),
      delete: () => Promise.resolve()
    },
    prefix,
    deleteProps = {},
    operateProps = {},
    tableColumns,
    queryColumns,
    formColumns,
    columns = [],
    commands = [
      { label: '编辑', key: 'put', type: 'primary' },
      { label: '删除', key: 'delete', type: 'danger' }
    ],
    rules = {},
    query = {},
    handleCommand = () => {}
  }: PageProps<D>) {
    this.pageClass = pageClass

    this.API = API
    this.prefix = prefix
    this.deleteProps = deleteProps
    this.handleCommand = handleCommand

    this.columns = reactive(columns)

    this.queryState = reactive({
      data: query,
      columns: queryColumns ?? columns
    })

    this.operateColumn = commands.length ? (
      <el-table-column
        label='操作'
        align='center'
        width={commands.reduce((t, c) => (t += c.width ?? 85), 10)}
        v-slots={{
          default: ({ row, $index }: { row: D; $index: number }) =>
            commands.map(c =>
              c.render ? (
                c.render(row, $index)
              ) : (
                <el-button {...c} onClick={() => this.tableHandle(c.key, row, $index)}>
                  {c.label}
                </el-button>
              )
            )
        }}
        {...operateProps}
      />
    ) : null

    this.tableState = new TableClass(p => API.get({ ...p, ...this.queryState.data }), {
      ref: 'tableRef',
      // align: 'center',
      columns: tableColumns ?? columns,
      height: '100%'
    })

    this.formState = useFormDialog(
      data => {
        const api = API[this.formState.type as string]
        return api
          ? api(data)?.then((msg?: string) => {
              this.tableState.getTableData()
              return msg
            })
          : Promise.reject()
      },
      { formProps: { columns: formColumns ?? columns, rules } }
    )
  }

  tableHandle(type: string, data?: D, index?: number) {
    this.formState.type = type
    this.formState.data = cloneDeep(data ?? {})
    switch (type) {
      case 'delete':
        ElMessageBox.confirm('', { ...defaultDeleteProps, ...this.deleteProps })
          .then(() =>
            this.API.delete?.(data).then(() => {
              ElMessage.success('删除成功')
              this.tableState.pagination.total--
              this.tableState.getTableData()
            })
          )
          .catch(console.error)
        break
      case 'post':
      case 'put':
      case 'view':
      case 'copy':
        this.formState.visible = true
        nextTick(() => {
          this.formState.formProps.columns.forEach(c => {
            c.hide = c.hideTypes?.includes(type)
            c.disabled = c.disabledTypes?.includes(type)
          })
          this.formState.formProps.disabled = type === 'view'
          this.formState.title = {
            post: '新增',
            put: '编辑',
            view: '详情',
            copy: '复制'
          }[type]
        })
      // 此处不要加break，默认走一次handleCommand
      default:
        this.handleCommand?.(type, data, index)
        break
    }
  }

  handleQuery() {
    this.tableState.pagination.currentPage = 1
    this.tableState.getTableData()
  }

  renderPart(part: string, slot?: JSX.Element) {
    switch (part) {
      case 'query':
        return <Query {...this.queryState} onClick={() => this.handleQuery()} />
      case 'header':
        return (
          <div class='lk-content-page-header'>
            <div class='header-left'>
              {slot ?? (
                <el-button type='primary' onClick={() => this.tableHandle('post')}>
                  {/* <svg-icon icon="plus" /> */}
                  新增
                </el-button>
              )}
            </div>
            {this.renderPart('query')}
          </div>
        )
      case 'table':
        return (
          <Table
            {...this.tableState.tableState}
            v-slots={{
              prefix: () => [this.prefix?.(), this.tableState.serialColumn],
              ...slot
            }}
          >
            {this.operateColumn}
          </Table>
        )
      case 'pagination':
        return <el-pagination class={['lk-pagination']} {...this.tableState.pagination} />
      case 'FormDialog':
        return <FormDialog {...this.formState} v-slots={slot} />
    }
  }

  render(slots?: { header?: JSX.Element; table?: JSX.Element; form?: JSX.Element }) {
    return (
      <div class={['lk-content-page', ...this.pageClass]}>
        {this.renderPart('header', slots?.header)}
        <div class='content-table'>{this.renderPart('table', slots?.table)}</div>
        {this.renderPart('pagination')}
        {this.renderPart('FormDialog', slots?.form)}
      </div>
    )
  }
}

export default PageClass
