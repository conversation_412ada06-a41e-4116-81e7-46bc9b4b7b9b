<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="110px" v-loading="formLoading">
      <el-form-item label="文件编号" prop="fileCode">
        <el-input v-model="formData.fileCode" maxlength="100" placeholder="请输入文件编号" />
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="formData.fileName" maxlength="100" placeholder="请输入文件名称" />
      </el-form-item>
      <el-form-item label="版本号" prop="fileVersion">
        <el-input v-model="formData.fileVersion" maxlength="100" placeholder="请输入版本号" />
      </el-form-item>
      <el-form-item label="文件状态" prop="fileStatus">
        <el-select v-model="formData.fileStatus" placeholder="请选择文件状态">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="控制类型(内)" prop="fileControlType">
        <el-select v-model="formData.fileControlType" placeholder="请请选择控制类型(内)">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="控制类型(外)" prop="fileControlTypeExternal">
        <el-select v-model="formData.fileControlTypeExternal" placeholder="请选择控制类型(外)">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { FileQrcodeApi, FileQrcodeVO } from '@/api/system/fileqrcode'

/** 运营端文件二维码管理 表单 */
defineOptions({ name: 'FileQrcodeForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  fileCode: undefined,
  fileName: undefined,
  fileVersion: undefined,
  fileStatus: undefined,
  fileControlType: undefined,
  fileControlTypeExternal: undefined
})
const formRules = reactive({
  fileCode: [{ required: true, message: '文件编号不能为空', trigger: 'blur' }],
  fileVersion: [{ required: true, message: '版本号不能为空', trigger: 'blur' }],
  fileStatus: [{ required: true, message: '文件状态不能为空', trigger: 'change' }],
  fileControlType: [{ required: true, message: '控制类型(内)不能为空', trigger: 'change' }],
  fileControlTypeExternal: [{ required: true, message: '控制类型(外)不能为空', trigger: 'change' }],
})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await FileQrcodeApi.getFileQrcode(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as FileQrcodeVO
    if (formType.value === 'create') {
      await FileQrcodeApi.createFileQrcode(data)
      message.success(t('admin_common_createSuccess'))
    } else {
      await FileQrcodeApi.updateFileQrcode(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    fileCode: undefined,
    fileName: undefined,
    fileVersion: undefined,
    fileStatus: undefined,
    fileControlType: undefined,
    fileControlTypeExternal: undefined,
  }
  formRef.value?.resetFields()
}
</script>
