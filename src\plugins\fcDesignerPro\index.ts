import type { App } from 'vue'
import FcDesigner from './core/index.es'
import vant from 'vant'
import 'vant/lib/index.css'

import { components } from '@/components/FormDesigner/components/index'

export const setupFormCreate = (app: App<Element>) => {
  Object.keys(components).forEach((key) => {
    const BLACK_LIST = ['DrRelateCardView']
    if (key === 'DrRelateCard') {
      FcDesigner.component(key, components[key], components['DrRelateCardView'])
    } else if (!BLACK_LIST.includes(key)) {
      FcDesigner.component(key, components[key])
    }
  })

  // 挂载 Vant
  app.use(vant)
  // 挂载 fcDesignerPro 组件
  app.use(FcDesigner)
  // 挂载 formCreate
  app.use(FcDesigner.formCreate)
}
