/*
 * @Description: element组件名-硬编码维护
 */

// 组件名前缀/后缀
export const el_prefix = `el`

// input
export const input = `input`
// number
export const number = `number`
// textarea
export const textarea = `textarea`
// select
export const select = `select`
// option
export const option = `option`
// radio
export const radio = `radio`
// checkbox
export const checkbox = `checkbox`
// group
export const group = 'group'
// date
export const date = `date`
// picker
export const picker = `picker`
// time
export const time = `time`

// 定义组件名称
export const el_input = `${el_prefix}-${input}` // el-input
export const el_input_number = `${el_prefix}-${input}-${number}` // el-input-number

export const el_select = `${el_prefix}-${select}` // el-select
export const el_option = `${el_prefix}-${option}` // el-option

export const el_radio_group = `${el_prefix}-${radio}-${group}` // el-radio-group
export const el_radio = `${el_prefix}-${radio}` // el-radio

export const el_checkbox_group = `${el_prefix}-${checkbox}-${group}` // el-checkbox-group
export const el_checkbox = `${el_prefix}-${checkbox}` // el-checkbox

export const el_date_picker = `${el_prefix}-${date}-${picker}` // el-date-picker
export const el_option_group = `${el_prefix}-${option}-${group}` // el-option-group
// 父组件中特定子组件map集合
// pName(父组件名称) => cName(父组件中特定的子组件名称)
export const childrenCompNameMap = new Map([
  [el_select, el_option],
  [el_radio_group, el_radio],
  [el_checkbox_group, el_checkbox]
])

// 组件名称map集合
export const compNameMap = new Map([
  [select, el_select],
  [input, el_input],
  [number, el_input_number],
  [textarea, el_input],
  [checkbox, el_checkbox_group],
  [radio, el_radio_group],
  [date, el_date_picker]
])

// number类型需要合并的属性
const numberProps = {
  step: 1,
  min: 0,
  max: 1000000
}

// 组件属性的map集合
export const compPropsMap = new Map([
  [el_select, _ => ({ 'collapse-tags': true })],
  [el_input_number, _ => ({ ...numberProps })],
  [number, _ => ({ ...numberProps })],
  [
    el_date_picker,
    type => ({
      'value-format': type?.includes(time) ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD',
      'range-separator': '至'
    })
  ],
  [textarea, _ => ({ rows: 4 })]
])
