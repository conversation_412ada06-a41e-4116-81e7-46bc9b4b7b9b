<template>
  <div class="dr-table-columns-editor">
    <el-tag class="dr-table-tag" type="danger">
      请勿在设计器的子表列拖入多个组件，避免数据异常
    </el-tag>
    <div class="table-header">
      <span class="title">组件列表</span>
      <div>
        <el-popover ref="componentsPopoverRef" placement="right-start" :width="200" trigger="click">
          <template #reference>
            <el-button type="text" size="small" :icon="CirclePlus">组件</el-button>
          </template>
          <div class="components-list">
            <div class="title">
              <span>基础组件</span>
            </div>
            <div
              class="item"
              v-for="item in widgetList"
              :key="item.name"
              @click="(event) => handleAddItem(item.name, item.title, event)"
            >
              <el-icon>
                <Document />
              </el-icon>
              <span>{{ item.title }}</span>
            </div>
          </div>
        </el-popover>
        <el-button type="text" size="small" :icon="Setting" @click="openSelectRulesDialog">
          关联
        </el-button>
      </div>
    </div>
    <VueDraggable
      :model-value="modelValue"
      item-key="value"
      :force-fallback="true"
      :animation="200"
      handle=".drag-icon"
      @change="onDraggableChange"
    >
      <template #item="{ element, index }">
        <div class="drag-item">
          <el-input
            :model-value="element.label"
            :disabled="element.dataSourceId"
            class="label-input"
            placeholder="Please input"
            @input="handleLabelInputChange($event, index)"
          />
          <el-tag v-if="element.dataSourceId" type="warning" effect="dark">关联</el-tag>
          <el-icon v-else class="cursor-pointer" :size="18" @click="copyComponent(index)">
            <CopyDocument />
          </el-icon>
          <el-icon class="cursor-pointer" :size="18" @click="deleteItem(index)">
            <Delete class="text-red-5" />
          </el-icon>
          <el-icon class="drag-icon" :size="18">
            <Rank />
          </el-icon>
        </div>
      </template>
    </VueDraggable>

    <el-dialog
      v-model="dialogVisible"
      title="关联字段"
      width="400"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
      @closed="onRelateDialogClosed"
    >
      <el-input
        v-model="relateFormData.relateButtonText"
        size="default"
        placeholder="自定义按钮名，若不设置则默认为引用数据"
      >
        <template #append>
          <el-popover
            placement="bottom-end"
            :width="300"
            :hide-after="0"
            trigger="click"
            ref="pop"
            popper-class="_fd-language-popover"
          >
            <template #reference>
              <i class="fc-icon icon-language"></i>
            </template>
            <div class="_fd-language-list">
              <div class="_fd-language-row">
                <span class="_fd_lanuage_title">中文</span>
                <span class="_fd_lanuage_content">
                  <el-input :model-value="relateFormData.relateButtonText" :disabled="true" />
                </span>
              </div>
              <div class="_fd-language-row">
                <span class="_fd_lanuage_title">英文</span>
                <span class="_fd_lanuage_content">
                  <el-input v-model="relateFormData.relateButtonTextEn" />
                </span>
              </div>
            </div>
          </el-popover>
        </template>
      </el-input>
      <p class="form-label">请选择要关联的数据</p>
      <el-select
        v-model="relateFormData.dataSourceId"
        placeholder="请选择模板"
        size="default"
        style="width: 100%"
      >
        <el-option
          v-for="item in dataSourceList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <div v-if="relateFormData.dataSourceId">
        <p class="form-label">请选择要关联的组件类型</p>
        <el-radio-group
          v-model="relateFormData.isDrTableForm"
          size="default"
          @change="onRelateTypeChange"
        >
          <el-radio :value="false">非子表组件</el-radio>
          <el-radio :value="true">子表组件</el-radio>
        </el-radio-group>

        <div v-if="!relateFormData.isDrTableForm">
          <el-tree-select
            ref="TreeSelectRef"
            v-model="relateFormData.selectedFieldKeys"
            :data="fieldTreeData"
            node-key="field"
            :default-expand-all="true"
            :props="{
              label: (data) => {
                return data.isDrTableForm ? `子表-${data.title}` : data.title
              },
              disabled: (data) => {
                return data.type === 'parent'
              }
            }"
            multiple
            :render-after-expand="true"
            show-checkbox
            check-strictly
            check-on-click-node
            size="default"
            style="width: 100%; margin-top: 10px"
            filterable
            @check="handleCurrentChange"
            @remove-tag="handleRemoveFieldTag"
          />
        </div>
        <div v-else>
          <el-tree-select
            v-model="relateFormData.subTreeId"
            ref="SubTableTreeSelectRef"
            :data="subTreeFields"
            :render-after-expand="false"
            :default-expand-all="true"
            :props="{
              disabled: (data) => {
                return data.type === 'parent'
              }
            }"
            size="default"
            style="width: 100%; margin-top: 10px"
            @current-change="handleChangeSubTreeIdValue"
          />
          <el-select
            v-model="relateFormData.selectedFieldKeys"
            size="default"
            clearable
            style="width: 100%; margin-top: 10px"
            multiple
            @change="handleSelectFieldsKeyChange"
          >
            <el-option
              v-for="item in handleObtainSubColumnData"
              :key="item.field"
              :label="item.title"
              :value="item.field"
            />
          </el-select>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button size="default" @click="dialogVisible = false">取消</el-button>
          <el-button size="default" type="primary" @click="handleConfirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ElMessage } from 'element-plus'
import { CirclePlus, Setting, Document, CopyDocument, Rank, Delete } from '@element-plus/icons-vue'
import unique from '@form-create/utils/lib/unique'
import { arrayMoveImmutable } from 'array-move'
import VueDraggable from 'vuedraggable'
import { cloneDeep } from 'lodash-es'
import { formCreateInjectionData } from '../../inject'
import { generateDataSourceList, generateRelateFieldList } from './utils'
import { ColumnItemType, ColumnType } from './types'

type PropsType = {
  modelValue?: ColumnItemType[]
  formCreateInject: any
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => []
})

const TreeSelectRef = ref<any | HTMLElement>(null)
const SubTableTreeSelectRef = ref<any | HTMLElement>(null)
const subTableColumnList = ref<any>([])
const widgetList = ref<any>([
  {
    title: '单行文本',
    name: 'DrInput'
  },
  {
    title: '多行文本',
    name: 'DrTextarea'
  },
  {
    title: '数字',
    name: 'DrInputNumber'
  },
  {
    title: '百分比',
    name: 'DrPercentage'
  },
  {
    title: '单选',
    name: 'DrRadio'
  },
  {
    title: '多选',
    name: 'DrCheckbox'
  },
  {
    title: '日期时间',
    name: 'DrDatePicker'
  },
  {
    title: '图片',
    name: 'DrImagesUpload'
  },
  {
    title: '附件',
    name: 'DrFilesUpload'
  },
  {
    title: '定位',
    name: 'DrLocation'
  },
  {
    title: '地址',
    name: 'DrAddress'
  },
  {
    title: '富文本',
    name: 'DrEditor'
  },
  {
    title: '评级',
    name: 'DrRate'
  },

  {
    title: '公式型组件',
    name: 'DrFormulas'
  },
  {
    title: 'SCCS成员单选',
    name: 'DrSCCSMemberSingleSelect'
  },
  {
    title: 'SCCS成员多选',
    name: 'DrSCCSMemberMultipleSelect'
  },
  {
    title: 'SCCS协作单选',
    name: 'DrSCCSGroupMemberSingleSelect'
  },
  {
    title: 'SCCS协作多选',
    name: 'DrSCCSGroupMemberMultipleSelect'
  },
  {
    title: '签名',
    name: 'DrSignature'
  },
  {
    title: '汇率',
    name: 'DrExchangeRates'
  }
])

const componentsPopoverRef = ref()
const INIT_RELATE_FORM_DATA = {
  relateButtonText: '',
  relateButtonTextEn: '',
  dataSourceId: '',
  isDrTableForm: false,
  subTreeId: '',
  selectedFieldKeys: [] as string[],
  selectedWidgetIds: [] as string[],
  dataSourceFormId: ''
}
const dialogVisible = ref<boolean>(false)
const relateFormData = ref(cloneDeep(INIT_RELATE_FORM_DATA))
const dataSourceList = ref<any[]>([])
const removeTagList = ref<string[]>([])

const emit = defineEmits(['update:modelValue', 'change'])

const injectionData = inject(formCreateInjectionData)
const injectDesigner: any = inject('designer')

// 监听 dataSourceId 变更，清除相关字段
watch(
  () => relateFormData.value.dataSourceId,
  (newVal, oldVal) => {
    if (newVal !== oldVal && !!oldVal) {
      relateFormData.value.subTreeId = ''
      relateFormData.value.selectedFieldKeys = []
    }
  }
)

const handleSelectFieldsKeyChange = (val: any) => {
  relateFormData.value.selectedWidgetIds = val
}

const handleChangeSubTreeIdValue = (data) => {
  relateFormData.value.dataSourceFormId =
    SubTableTreeSelectRef.value.getNode(data).parent.data.value
  relateFormData.value.selectedFieldKeys = []
}

const handleObtainSubColumnData: any = computed(() => {
  let subTreeData: any = []

  for (let subTreeTable of subTreeFields.value) {
    const subTableColumn = subTreeTable.children.filter((child) =>
      child.hasOwnProperty('subTableChildren')
    )
    subTreeData.push(...subTableColumn)
    const relateTableColumn = subTreeTable.children.filter((child) =>
      child.hasOwnProperty('children')
    )

    if (relateTableColumn.length > 0) {
      for (let relateTableCol of relateTableColumn) {
        subTreeData.push(...relateTableCol.children)
      }
    }
  }

  const tableData: any = subTreeData.find(
    (subData: any) => subData.value === relateFormData.value.subTreeId
  )

  let subTableColumnList = []
  if (tableData) {
    subTableColumnList = tableData.subTableChildren.slice(1).map((tableColumn) => {
      const column = tableColumn.children[0]
      console.log(column)
      return column
    })
  }
  return subTableColumnList
})

const handleCurrentChange = (data: any, Node) => {
  let checkNodeIds: string[] = []
  Node.checkedNodes.forEach((checkNode) => {
    checkNodeIds.push(checkNode._fc_id || checkNode.value)
  })

  relateFormData.value.dataSourceFormId = TreeSelectRef.value.getNode(data).parent.data.field
  relateFormData.value.selectedWidgetIds = checkNodeIds
}

const handleRemoveFieldTag = (tagValue: any) => {
  removeTagList.value.push(tagValue)
}

/**
 * 改行列顺序
 */
const onDraggableChange = (e: any) => {
  if (e.moved) {
    const cloneValue = cloneDeep(props.modelValue)
    const newValue = arrayMoveImmutable(cloneValue, e.moved.oldIndex, e.moved.newIndex)
    emit('update:modelValue', newValue)
    emit('change', newValue)

    const widgetFieldIds = newValue.map((component) => component.fieldId)
    let widgetChildren: any[] = []
    widgetFieldIds.forEach((widgetId) => {
      const widgetItem = injectDesigner.setupState.activeRule.children
        .slice(1)
        .find((childrenData) => {
          return childrenData.children[0]?.children[0]?.children[0]?.children[0]?.field === widgetId
        })
      widgetChildren.push(widgetItem)
    })
    const widgetList = [injectDesigner.setupState.activeRule.children[0]].concat(widgetChildren)
    injectDesigner.setupState.activeRule.children = widgetList
  }
}

/**
 * 修改标题
 */
const handleLabelInputChange = (value: string, index: number) => {
  const cloneValue = cloneDeep(props.modelValue)
  cloneValue[index].label = value
  emit('update:modelValue', cloneValue)
  emit('change', cloneValue)
  injectDesigner.setupState.activeRule.children[
    index + 1
  ].children[0].children[0].children[0].children[0].title = value
}

/**
 * 新增
 */
const handleAddItem = (type: ColumnType, defaultLabel: string, e: Event) => {
  const cloneValue: any = cloneDeep(props.modelValue)
  const fieldId = unique()
  cloneValue.push({
    type,
    label: defaultLabel,
    fieldId: fieldId
  })
  emit('update:modelValue', cloneValue)
  emit('change', cloneValue)
  handleAddTableFormColumn(type, fieldId)
}

const handleAddTableFormColumn = (type: string, fieldId: string) => {
  injectDesigner.setupState.toolHandle(...[injectDesigner.setupState.activeRule, 'addChild'])
  const menuList = [
    ...injectDesigner.setupState.menuList[1].list,
    ...injectDesigner.setupState.menuList[2].list
  ]
  const menuRule = menuList.find((rule) => rule.name === type)
  const index = injectDesigner.setupState.activeRule.children.length - 1
  const widget = injectDesigner.setupState.makeRule(menuRule)
  widget.children[0].field = fieldId
  injectDesigner.setupState.activeRule.children[index].children[0].children[0].children.push(widget)
}

/**
 * 拷贝组件
 */
const copyComponent = (index: number) => {
  const cloneValue = cloneDeep(props.modelValue)
  const copyItem = cloneDeep(cloneValue[index])
  copyItem.fieldId = unique()
  cloneValue.splice(index + 1, 0, copyItem)
  emit('update:modelValue', cloneValue)
  emit('change', cloneValue)

  injectDesigner.setupState.toolHandle(...[injectDesigner.setupState.activeRule, 'addChild'])
  const widget = cloneDeep(injectDesigner.setupState.activeRule.children[index + 1])
  const menuList = [
    ...injectDesigner.setupState.menuList[1].list,
    ...injectDesigner.setupState.menuList[2].list
  ]
  const menuRule = menuList.find(
    (rule) => rule.name === widget.children[0].children[0].children[0].children[0].type
  )
  const copyWidget = injectDesigner.setupState.makeRule(menuRule)
  widget.children[0].children[0].children[0].children[0].field = copyItem.fieldId
  widget.children[0].children[0].children[0].children[0]._fc_id = copyWidget.children[0]._fc_id
  const widgetFieldIds = cloneValue.map((component) => component.fieldId)
  let widgetChildren: any[] = []
  widgetFieldIds.forEach((widgetId) => {
    const widgetItem = injectDesigner.setupState.activeRule.children
      .slice(1)
      .find((childrenData) => {
        return childrenData.children[0]?.children[0]?.children[0]?.children[0]?.field === widgetId
      })
    widgetChildren.push(widgetItem ? widgetItem : widget)
  })
  const widgetList = [injectDesigner.setupState.activeRule.children[0]].concat(widgetChildren)
  injectDesigner.setupState.activeRule.children = widgetList
}

/**
 * 删除 */
const deleteItem = (index: number) => {
  const cloneValue = cloneDeep(props.modelValue)
  cloneValue.splice(index, 1)
  emit('update:modelValue', cloneValue)
  emit('change', cloneValue)

  const rule = injectDesigner.setupState.activeRule.children[index + 1]
  injectDesigner.setupState.toolHandle(...[rule, 'delete'])
}

const onRelateDialogClosed = () => {
  relateFormData.value = cloneDeep(INIT_RELATE_FORM_DATA)
}

const closeComponentsPopover = () => {
  componentsPopoverRef.value?.hide()
}

const onRelateTypeChange = () => {
  removeTagList.value = relateFormData.value.selectedFieldKeys
  relateFormData.value.selectedFieldKeys = []
  relateFormData.value.selectedWidgetIds = []
}

// 子表组件下拉选项值
const subTreeFields = computed(() => {
  const dataSourceId = relateFormData.value.dataSourceId
  if (!dataSourceId) return []
  const selectMain = dataSourceList.value.find((item) => item.id === dataSourceId)

  if (!selectMain) return []
  if (selectMain.formType === 'MAIN_FORM' || selectMain.formType === 'MS_REPLY_FORM') {
    return [
      {
        value: selectMain.id,
        label: selectMain.label,
        type: 'parent',
        children: selectMain.formList
          .filter((widget) => widget.type === 'DrTableForm')
          .map((tableForm) => {
            return {
              type: 'widget',
              value: tableForm._fc_id,
              label: tableForm.title,
              subTableChildren: tableForm.children
            }
          })
      }
    ]
  }

  const formType = injectionData?.formType.value
  if (selectMain.formType === 'MILESTONE' && formType === 'FORM') {
    return [
      {
        value: selectMain.id,
        label: selectMain.label,
        type: 'parent',
        children: selectMain.formList
          .filter((form) => form.formType !== 'FORM')
          .filter((widget) => widget.type === 'DrTableForm')
          .map((tableForm) => {
            return {
              type: 'widget',
              value: tableForm._fc_id,
              label: tableForm.title,
              subTableChildren: tableForm.children
            }
          })
      }
    ]
  }

  return selectMain.formList.map((item) => {
    const RelateCardWidgetList: any[] = item.widgetJsonList.filter(
      (widget) =>
        widget.type === 'DrRelateCard' &&
        widget.props.relatedValue.rules.findIndex((related) => related.type === 'DrTableForm') > -1
    )
    return {
      value: item.id,
      label: item.name,
      type: 'parent',
      children: [
        ...item.widgetJsonList
          .filter((widget) => widget.type === 'DrTableForm')
          .map((tableForm) => {
            return {
              type: 'widget',
              value: tableForm._fc_id,
              label: tableForm.title,
              subTableChildren: tableForm.children
            }
          }),
        ...RelateCardWidgetList.map((relate) => {
          if (relate) {
            const relateTableForm = relate.props.relatedValue.rules.filter(
              (widget) => widget.type === 'DrTableForm'
            )

            return {
              type: 'parent',
              value: relate._fc_id,
              label: relate.title,
              children: relateTableForm.map((relateItem) => {
                return {
                  type: 'widget',
                  value: `${relate._fc_id}.${relateItem._fc_id}`,
                  label: relateItem.title,
                  subTableChildren: relateItem.children
                }
              })
            }
          }
        })
      ]
    }
  })
})

// 非子表组件下拉选项值
const fieldTreeData = computed(() => {
  const dataSourceId = relateFormData.value.dataSourceId
  if (!dataSourceId) return []
  const selectMain = dataSourceList.value.find((item) => item.id === dataSourceId)
  if (!selectMain) return []
  if (selectMain.formType === 'MAIN_FORM' || selectMain.formType === 'MS_REPLY_FORM') {
    return generateRelateFieldList(selectMain.formList, relateFormData.value.isDrTableForm)
  }

  debugger

  const formType = injectionData?.formType.value
  if (selectMain.formType === 'MILESTONE' && formType === 'FORM') {
    return (
      selectMain.formList
        // .filter((form) => form.formType !== 'FORM')
        .map((item) => {
          return {
            field: item.id,
            type: 'parent',
            title: item.name,
            children: generateRelateFieldList(
              item.widgetJsonList,
              relateFormData.value.isDrTableForm
            )
          }
        })
    )
  }

  return selectMain.formList.map((item) => {
    return {
      field: item.id,
      type: 'parent',
      title: item.name,
      children: generateRelateFieldList(item.widgetJsonList, relateFormData.value.isDrTableForm)
    }
  })
})

const openSelectRulesDialog = () => {
  // 有关联组件的时候，则需要判断
  const dataSource: any = props.modelValue.find((modelValue) => modelValue.dataSourceId)
  if (dataSource) {
    relateFormData.value.dataSourceId = dataSource.dataSourceId
    const columnFieldIds = props.modelValue
      .filter((column) => column.dataSourceId)
      .map((column) => column.fieldId)
    relateFormData.value.selectedFieldKeys = columnFieldIds

    const dataSourceItem: any = props.modelValue.find((column) => column.dataSourceId)
    if (dataSourceItem.hasOwnProperty('subTableFormId')) {
      relateFormData.value.isDrTableForm = true
      relateFormData.value.subTreeId = dataSourceItem.subTableFormId
    }
  }
  //@ts-ignore
  dataSourceList.value = generateDataSourceList(injectionData)
  dialogVisible.value = true
}

const handleClose = (done: () => void) => {
  done()
}

const handleConfirm = () => {
  if (!relateFormData.value.dataSourceId) {
    ElMessage.error('请选择数据来源')
    return
  }
  if (relateFormData.value.selectedFieldKeys.length === 0) {
    ElMessage.error('请选择关联字段')
    return
  }

  const cloneValue = cloneDeep(props.modelValue)
  const relateFieldKeys = relateFormData.value.selectedFieldKeys

  // 找出需要删除的关联字段 - 原本有dataSourceId但现在不在选中列表中的
  const currentRelatedItems: any[] = cloneValue.filter((item) => item.dataSourceId)
  const itemsToRemove: any[] = []

  for (let currentItem of currentRelatedItems) {
    const stillSelected = relateFieldKeys.some((selectedId) => {
      if (relateFormData.value.isDrTableForm) {
        // 子表组件：比较 fieldId 和 subTableFormId
        const relateColumn = handleObtainSubColumnData.value.find(
          (columnField) => columnField.field === selectedId
        )
        return (
          relateColumn &&
          relateColumn.field === currentItem.fieldId &&
          relateFormData.value.subTreeId === currentItem.subTableFormId
        )
      } else {
        // 非子表组件：比较 dataSourceWidgetId 和 dataSourceId
        const relateFieldColumns = fieldTreeData.value.reduce((prev, current) => {
          if (current.type === 'parent') {
            return [...prev, ...current.children]
          }
          return [...prev, current]
        }, [])
        const relateColumn = relateFieldColumns.find(
          (columnField) => columnField._fc_id === selectedId
        )
        return (
          relateColumn &&
          relateColumn._fc_id === currentItem.dataSourceWidgetId &&
          relateFormData.value.dataSourceId === currentItem.dataSourceId
        )
      }
    })

    if (!stillSelected) {
      itemsToRemove.push(currentItem)
    }
  }

  // 处理手动移除的标签
  for (let removeTag of removeTagList.value) {
    const index = cloneValue.findIndex(
      (relateItemData: any) => relateItemData.fieldId === removeTag
    )
    if (index !== -1) {
      cloneValue.splice(index, 1)
      const rule = injectDesigner.setupState.activeRule.children[index + 1]
      injectDesigner.setupState.toolHandle(...[rule, 'delete'])
    }
  }
  removeTagList.value = []

  // 删除不再选中的关联字段
  const itemsToRemoveIndices = itemsToRemove
    .map((itemToRemove) =>
      cloneValue.findIndex((item: any) => item.fieldId === itemToRemove.fieldId)
    )
    .filter((index) => index !== -1)
    .sort((a, b) => b - a) // 从大到小排序

  for (let index of itemsToRemoveIndices) {
    cloneValue.splice(index, 1)
    const rule = injectDesigner.setupState.activeRule.children[index + 1]
    injectDesigner.setupState.toolHandle(...[rule, 'delete'])
  }

  const relateFields: any[] = relateFieldKeys
    .map((relateField) => {
      if (relateFormData.value.isDrTableForm) {
        const relateColumn = handleObtainSubColumnData.value.find(
          (columnField) => columnField.field === relateField
        )
        if (!relateColumn) return null

        const { dataSourceId, relateButtonText, subTreeId, relateButtonTextEn, dataSourceFormId } =
          relateFormData.value
        const { field, title, type, _fc_id } = relateColumn

        const rootDataSourceWidgetId = relateColumn.props.rootDataSourceWidgetId; 

        return {
          fieldId: field,
          label: title,
          type: type,
          dataSourceFormId: dataSourceFormId,
          subTableFormId: subTreeId, // 子表特有字段
          dataSourceWidgetId: _fc_id,
          dataSourceId: dataSourceId,
          rootDataSourceWidgetId: rootDataSourceWidgetId
            ? rootDataSourceWidgetId
            : _fc_id,
          relateButtonText: relateButtonText,
          relateButtonTextEn: relateButtonTextEn,
          relateWidgetConfig: relateColumn
        }
      } else {
        const relateFieldColumns = fieldTreeData.value.flatMap((item) =>
          item.children
            ? item.children.map((child) => ({
                ...child,
                dataSourceFormId: item.field,
                parentTitle: item.title
              }))
            : []
        )

        console.log(relateFieldColumns)

        const relateColumn = relateFieldColumns.find(
          (columnField) => columnField.field === relateField
        )
        if (!relateColumn) return null

        const { field, title, type, _fc_id, rootDataSourceWidgetId } = relateColumn
        const { dataSourceId, relateButtonText, relateButtonTextEn } = relateFormData.value

        return {
          fieldId: field,
          label: title,
          type: type,
          rootDataSourceWidgetId: rootDataSourceWidgetId
            ? rootDataSourceWidgetId
            : _fc_id,
          dataSourceFormId: relateColumn.dataSourceFormId,
          dataSourceWidgetId: _fc_id,
          dataSourceId: dataSourceId,
          relateButtonText: relateButtonText,
          relateButtonTextEn: relateButtonTextEn,
          relateWidgetConfig: relateColumn
        }
      }
    })
    .filter(Boolean) // 过滤掉null值

  // 添加新的关联字段或更新现有字段
  let otherRelateFields: any[] = []
  for (let relateField of relateFields) {
    const existingIndex = cloneValue.findIndex((relateItemData: any) => {
      if (relateFormData.value.isDrTableForm) {
        // 子表组件：比较 fieldId 和 subTableFormId
        return (
          relateItemData.fieldId === relateField.fieldId &&
          relateItemData.subTableFormId === relateField.subTableFormId
        )
      } else {
        // 非子表组件：比较 fieldId 和 dataSourceId
        return (
          relateItemData.fieldId === relateField.fieldId &&
          relateItemData.dataSourceId === relateField.dataSourceId
        )
      }
    })

    if (existingIndex === -1) {
      cloneValue.push(relateField)
      otherRelateFields.push(relateField)
    } else {
      // 如果字段已存在，更新其配置
      cloneValue[existingIndex] = { ...cloneValue[existingIndex], ...relateField }
    }
  }

  debugger

  emit('update:modelValue', cloneValue)
  emit('change', cloneValue)

  for (let relateField of otherRelateFields) {
    injectDesigner.setupState.toolHandle(...[injectDesigner.setupState.activeRule, 'addChild'])
    const index = injectDesigner.setupState.activeRule.children.length - 1
    const menuList = [
      ...injectDesigner.setupState.menuList[1].list,
      ...injectDesigner.setupState.menuList[2].list
    ]
    const relateFieldType = ['DrSCCSMemberSelect', 'DrSCCSGroupMemberSelect'].includes(
      relateField.type
    )
      ? relateField.relateWidgetConfig._fc_drag_tag
      : relateField.type
    const menuRule = menuList.find((rule) => rule.name === relateFieldType)
    if (!menuRule) continue

    const designerMenuRule = injectDesigner.setupState.makeRule(menuRule)
    const relateConfig = cloneDeep(relateField.relateWidgetConfig)

    Object.assign(designerMenuRule.children[0], {
      title: relateConfig.title,
      props: Object.assign(relateConfig.props, {
        dataSourceId: relateFormData.value.dataSourceId,
        rootDataSourceWidgetId: relateConfig._fc_id
      }),
      field: relateField.fieldId
    })
    injectDesigner.setupState.activeRule.children[index].children[0].children[0].children.splice(
      0,
      1,
      designerMenuRule
    )
  }

  dialogVisible.value = false
}
</script>

<style scoped lang="scss">
.dr-table-columns-editor {
  width: 100%;

  .dr-table-tag {
    display: block;
    width: 100%;
    height: auto;
    padding: 5px 8px;
    font-size: 12px;
    line-height: 18px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    margin-bottom: 10px;
  }

  .table-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .drag-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
    user-select: none;
    padding: 5px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }

    .label-input {
      flex: 1;
    }

    .drag-icon {
      cursor: move;
    }

    :deep(.el-radio) {
      margin-right: 0;
    }
  }
}

.components-list {
  max-height: 300px;
  overflow-y: auto;

  .title {
    font-size: 14px;
    font-weight: 700;
    padding: 10px;
  }

  .item {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 5px;
    height: 30px;
    background-color: #f2f2f2;
    border-radius: 4px;
    padding: 0 10px;
    cursor: pointer;
  }
}

.form-label {
  font-weight: 600;
  font-size: 14px;
  padding: 10px 0;
  margin: 0;
}
</style>
