<!-- Form组件 -->
<script lang="jsx">
import renderComponent from '../render/renderComponent'
import { getPropRules } from '../render/utils'
import { defineComponent, ref } from 'vue'

export default defineComponent({
  name: 'lk-form',
  props: {
    columns: { type: Array, default: () => [] },
    modelValue: { type: Object, default: () => ({}) },
    rules: { type: Object, default: () => ({}) }
  },
  emits: ['update:modelValue'],
  setup(props, { attrs, slots, expose }) {
    const form = ref()
    // 暴露给外部使用的methods
    expose({
      form,
      validate(cb) {
        form.value.validate(valid => cb?.(valid))
      },
      resetFields() {
        form.value.resetFields()
      }
    })
    return () => (
      <el-form ref={form} class={['lk-form']} onsubmit='return false' model={props.modelValue} {...attrs}>
        {slots.header?.()}
        {props.columns.map(c => {
          const { formItemProps = {}, show, hide, ...others } = c
          {
            /* 明确定义show=false,hide=true时隐藏 */
          }
          return show === false || hide === true ? null : (
            <el-form-item
              class={['lk-form-item']}
              label={c.label}
              prop={c.prop}
              key={c.prop}
              rules={getPropRules(c, props.rules[c.prop])}
              v-slots={formItemProps?.slots}
              {...formItemProps}
            >
              {slots[c.prop]
                ? slots[c.prop]({ column: c, model: props.modelValue })
                : renderComponent(others, props.modelValue)}
            </el-form-item>
          )
        })}
        {slots.default?.()}
        {slots.footer?.()}
      </el-form>
    )
  }
})
</script>
<style lang="scss" scoped>
.lk-form {
  .lk-form-item {
    display: inline-block;
    width: 50%;
    margin-bottom: 20px;
    padding-right: 15px;
    padding-left: 15px;
    box-sizing: border-box;

    [class$='form-item__content'] > * {
      width: 100%;
    }
  }
}
</style>
