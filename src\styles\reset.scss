

img.m {
  width: 20px;
  height: 20px;
}


.bg-w {
  background-color: white;
}


.shadow {
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.13);
}


.flex-row {
  display: flex;
  flex-direction: row;
}

.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.jc-center {
  justify-content: center;
}

.jc-start {
  justify-content: flex-start;
}

.jc-end {
  justify-content: flex-end;
}

.jc-between {
  justify-content: space-between;
}

.jc-around {
  justify-content: space-around;
}

.ai-center {
  align-items: center;
}

.ai-start {
  align-items: flex-start;
}

.ai-end {
  align-items: flex-end;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow-1 {
  flex-grow: 1;
}

.lk-scroll {
  position: relative;
  padding-right: 12px;
  box-sizing: border-box;
  border-right: 1px solid #fff;

  &::after {
    position: absolute;
    top: 0;
    right: -1px;
    content: '';
    width: 12px;
    height: 100%;
    border-left: 1px solid #e8e8e8;
    background-color: #fff;
  }
}

.lk-dialog {
  display: flex;
  flex-direction: column;

  & > .el-dialog__header {
    padding-right: 54px;
    margin-right: 0;

    & > .el-dialog__title {
      flex-shrink: 0;
      display: inline-block;
      color: #202020;
      font-size: 18px;
      font-weight: bold;
      white-space: nowrap;
      text-overflow: ellipsis;
      max-width: 100%;
      padding-right: 90px;
      box-sizing: border-box;
      overflow: hidden;
    }

    & > .el-dialog__headerbtn {
      & > .el-dialog__close {
        color: #000;
        font-size: 22px;
      }
    }
  }

  & > .el-dialog__body {
    flex-grow: 1;
    min-height: 0;
  }

  & > .el-dialog__footer {
    padding: 14px 20px;
  }

  &.full-body {
    & > .el-dialog__body {
      position: relative;
      margin: 0;
      padding: 0;
    }
  }

  &.gray-body {
    & > .el-dialog__body {
      background-color: #f6f6f6;
    }
  }

  &.border {
    & > .el-dialog__header {
      border-bottom: 1px solid #e9eaec;
    }

    & > .el-dialog__footer {
      border-top: 1px solid #e9eaec;
    }
  }
}

.lk-msgbox {
  --el-messagebox-width: 480px;

  .el-message-box__header {
    padding: 20px;
  }

  .el-message-box__title {
    color: #202020;
    font-size: 18px;
    font-weight: bold;
  }

  .el-message-box__content {
    color: #262626;
    font-size: 18px;
    min-height: 90px;
    padding: 10px 24px;
  }

  .el-message-box__headerbtn {
    top: 20px;
    right: 20px;

    .el-message-box__close {
      color: #000;
      font-size: 22px;
    }
  }

  .el-message-box__btns {
    padding: 5px 24px 10px 24px;

    button:nth-child(2) {
      margin-left: 16px;
    }
  }

  .el-button--cancel {
    &:hover,
    &:active,
    &:focus {
      color: var(--el-color-info);
      border-color: var(--el-color-info-light-7);
      background-color: var(--el-color-info-light-9);
    }

    &:active {
      border-color: var(--el-color-info-light-5);
    }
  }

  .el-message-box__status {
    top: 0;
    transform: none;
  }

  &.icon--danger .el-message-box__status {
    color: #e62412 !important;
  }

  &.icon--primary .el-message-box__status {
    color: #2082ed !important;
  }
}

.lk-collapse {
  &.el-collapse {
    border: none;
  }

  .el-collapse-item__header {
    justify-content: space-between;
    color: #262626;
    font-size: 14px;
    font-weight: bold;
    line-height: 42px;
    height: 42px;
    padding-left: 14px;
    border: none;
    background-color: #f6f9fb;
  }

  .el-collapse-item__arrow {
    flex-shrink: 0;
    color: #808080;
    font-size: 18px;
    margin: 0 14px;
  }

  .el-collapse-item__wrap {
    border: none;
  }

  .el-collapse-item__content {
    padding-bottom: 12px;
  }

  &.border {
    .el-collapse-item {
      margin-bottom: 16px;
      border: 1px solid #e6eaf0;
    }

    .el-collapse-item__header {
      height: 54px;
    }

    .el-collapse-item__wrap {
      border-top: 1px solid #e4e4e4;
    }
  }

  .el-collapse-item {
    &.yellow {
      .el-collapse-item__header {
        background-color: #fdf5ed;
      }
    }
  }
}

.lk-dropdown-menu {
  .el-dropdown-menu__item {
    &:not(.is-disabled) {
      color: #595959;

      &:focus {
        color: #595959;
        background-color: #f2f2f2;
      }

      &.danger {
        color: #cf1421;

        &:focus {
          color: #cf1421;
        }
      }
    }
  }
}

.lk-radio-group {
  .el-radio-button {
    --el-radio-button-checked-bg-color: #e1edff;
    --el-radio-button-checked-text-color: #0070d2;
    --el-radio-button-checked-border-color: #dcdfe6;

    &__inner {
      color: #262626;
    }
  }

  .el-radio-button__original-radio:checked + .el-radio-button__inner {
    font-weight: bold;
  }
}

.lk-tooltip {
  &.el-popper {
    max-width: 500px;
  }
}

.lk-user-cascader-popper {
  inset: 64px 4px 4px auto !important;
  .el-popper__arrow {
    display: none;
  }
  .el-cascader-panel {
    height: 100%;
  }

  .el-cascader-menu {
    width: 280px;

    &__wrap {
      &.el-scrollbar__wrap {
        height: 100%;
      }
    }
  }
  .el-cascader-node {
    color: #303133;
    &.is-active {
      font-weight: normal;
    }
    &.in-active-path,
    &.is-selectable.in-checked-path {
      color: var(--el-cascader-menu-selected-text-color);
      font-weight: normal;
    }
    &:not(.is-disabled):hover,
    &:not(.is-disabled):focus {
      background-color: #f2f2f2;
    }
  }
}

.el-button:focus-visible {
  outline: none !important;
}

.el-button.el-button--primary.is-text:not(.is-disabled).is-has-bg {
  background-color: #e1edff;
}

.el-button.is-text:not(
    .is-disabled,
    .el-button--success,
    .el-button--info,
    .el-button--warning,
    .el-button--danger
  ).is-has-bg {
  &:hover,
  &:focus {
    color: var(--el-color-primary);
    background-color: #e1edff;
  }
}

.wrap-label {
  word-break: break-word;
  white-space: break-spaces;
}

.common-tip {
  font-size: 12px;
  font-weight: 400;
  text-align: left;
  color: #797979;
  line-height: 17px;
}

.widget-type-div {
  border-radius: 8px;
  right: 5px;
  top: 6px;
  background: #d7d7d7;
  font-size: 12px;
  word-break: keep-all;
  white-space: nowrap;
  color: #8c8c8c;
  width: fit-content;
}

// label {
//   overflow: hidden;
//   text-overflow: ellipsis;
//   white-space: nowrap;
//   word-break: break-all;
// }

/* .el-dialog__header {
  text-align: left;
}

.mydialog>.el-dialog__header {
  border: 1px solid rgba(0, 0, 0, 0.1);
  border-width: 0 0 1px 0 !important;
  margin-right: 0;
  border-radius: 10px 10px 0px 0px;
}

.el-dialog__footer {
  height: 100%;
  width: 100%;
  background: #f3f2f2;
  border-radius: 0px 0px 6px 6px;
  box-shadow: 0px -1px 6px 0px rgba(0, 0, 0, 0.11);
  padding-top: 18px;
  padding-bottom: 12px;
} */

.one-elips {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-break: break-all;
}

//弹窗弹出后蒙版左移修复
.el-popup-parent--hidden {
  width: 100% !important;
}

.a-span {
  color: #0070d2;
  cursor: pointer;
}

.bb-line {
  border: 1px solid rgba(151, 151, 151, 0.17);
  border-width: 0 0 1px 0 !important;
}

// a {
//   color: --el-color-primary);
// }

// code {
//   border-radius: 2px;
//   padding: 2px 4px;
//   background-color: --el-color-primary-light-9);
//   color: --el-color-primary);
// }

.el-form-item__error {
  padding-top: 4px;
}


// 使得点击表单 label 不触发内容
// .el-form-item__label {
//   pointer-events: none;
// }

.form-title {
  margin-bottom: 58px;
}

// .el-dialog.small {
//   min-width: 480px;
//   max-width: 620px;
//   width: 33%;
// }

// .el-dialog {
//   min-width: 620px;
//   max-width: 800px;
//   width: 43%;
// }

// .el-dialog.middle {
//   min-width: 800px;
//   max-width: 1000px;
//   width: 50%;
// }

// .el-dialog.large {
//   min-width: 1200px;
//   max-width: 1360px;
//   width: 80%;
//   height: 92%;
// }

.font-text.two-line,
.two-line {
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  white-space: normal;
}

.form-submit-error {
  color: var(--el-color-danger);
  font-size: 12px;
  line-height: 1;
  padding-top: 2px;
  text-align: center;
}


.module-title-div {
  justify-content: flex-start;
  align-items: center;
  height: 20px;
}


.form-item-title {
  font-size: 14px;
  line-height: 150%;
  color: #262626;
  font-style: Normal;
}

//全局弹窗padding缩小
.el-dialog__body {
  padding: 0px 20px;
  margin: 10px 0px;
}

.dot-div {
  width: 6px;
  height: 6px;
  background-color: #bbbbbb;
  border-radius: 50%;
}

.el-divider--horizontal {
  margin: 10px;
}

// 以下旧项目 maxima css , 尽量不使用

.mflex-row {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  /* 水平方向两边对齐、撑满 */
  align-items: center;
}

.mflex-row.mflex-left {
  justify-content: flex-start;
}

.mflex-row.mflex-center {
  justify-content: center;
}

.mflex-row.mflex-right {
  justify-content: flex-end;
}

.mflex-row.mflex-hspacing {
  /* 水平方向两边对齐、撑满 */
  justify-content: space-between;
}

.mflex-row.mflex-around {
  /* 水平方向两边对齐、平均分 */
  justify-content: space-around;
}

.mflex-row.mflex-top {
  align-items: flex-start;
}

.mflex-row.mflex-equal {
  align-items: center;
}

.mflex-row.mflex-bottom {
  align-items: flex-end;
}

.mflex-row.mflex-vspacing {
  align-items: stretch;
}

.mflex-col.mflex-around {
  justify-content: space-around;
}

.mflex-row > .mflex-top-self {
  align-self: flex-start;
}

.mflex-row > .mflex-equal-self {
  align-self: center;
}

.mflex-row > .mflex-bottom-self {
  align-self: flex-end;
}

.mflex-col {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  align-items: stretch;
}

.mflex-col.mflex-top {
  justify-content: flex-start;
}

.mflex-col.mflex-equal {
  justify-content: center;
}

.mflex-col.mflex-bottom {
  justify-content: flex-end;
}

.mflex-col.mflex-vspacing {
  justify-content: space-between;
}

.mflex-col.mflex-left {
  align-items: flex-start;
}

.mflex-col.mflex-center {
  align-items: center;
}

.mflex-col.mflex-margin-auto {
  margin: 0 auto 0 auto;
}

.mflex-col.mflex-right {
  align-items: flex-end;
}

.mflex-col.mflex-hspacing {
  align-items: stretch;
}

.mflex-col > .mflex-left-self {
  align-self: flex-start;
}

.mflex-col > .mflex-center-self {
  align-self: center;
}

.mflex-col > .mflex-right-self {
  align-self: flex-end;
}

.icon-delete {
  color: #d60505;
}

:focus-visible {
  outline: none;
}

.vxe-table {
  z-index: 4;
  ::-webkit-scrollbar {
    background-color: #fff;
    width: 8px;
    height: 8px;
  }
  ::-webkit-scrollbar-track {
    background-color: #fff;
  }
  ::-webkit-scrollbar-thumb {
    background-color: rgba(144, 147, 153, 0.3);
    border-radius: 6px;
  }
  ::-webkit-scrollbar-thumb:hover {
    background-color: rgba(144, 147, 153, 0.5);
  }
  ::-webkit-scrollbar-corner {
    background-color: #fff;
  }
  :deep(.activeBg) {
    background-color: #ffe5cb;
  }
  :deep(.active) {
    background-image: none !important;
    border: 2px solid #ef7e0a !important;
  }
  :deep(.vxe-checkbox--label) {
    display: none;
  }
  :deep(.vxe-checkbox--input) {
    display: none;
  }
  :deep(.is--disabled.vxe-checkbox) {
    background-color: #f5f7fa;
    border-color: #dcdfe6;
  }
}
.lktip {
  font-size: 14px;
}
.clamp2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  white-space: normal;
}
.el-input__suffix .tips {
  margin: 0;
  font-size: 12px;
  user-select: none;
  .el-icon {
    cursor: pointer;
    padding: 2px;
  }
  span,
  .active {
    color: #000;
  }
  text > span {
    min-width: 14px;
    text-align: center;
    display: inline-block;
  }
}
