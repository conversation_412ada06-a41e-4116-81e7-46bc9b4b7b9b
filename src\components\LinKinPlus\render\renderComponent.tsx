import { isSelect } from './utils'
import { merge } from 'lodash-es'
import { mergeWith } from 'lodash-es'
import { h, resolveComponent } from 'vue'
import { getChildrenComponentName, getCompProps, getComponentName } from './config'
import type { ComponentProps, Option } from '../hooks/types'
import { el_select, el_option_group } from './static'

const isObject = (o: any) => Object.prototype.toString.call(o) === '[object Object]'

function getDefaultProps(props: ComponentProps): [string, object] {
  const componentName = getComponentName(props)
  const { type } = props
  const defaultProps = {
    clearable: true,
    placeholder: isSelect({ componentName, ...props }) ? useI18n().t('admin_common_selectText') : useI18n().t('admin_common_inputText') // + others.label
  }

  const mergeObj = getCompProps(componentName, type)
  merge(defaultProps, mergeObj)
  return [componentName, defaultProps]
}

//子节点
const children = (options: Option[], componentName: string) => {
  if (!options.length) return []
  return options?.map((o: any) => {
    const item = isObject(o) ? o : { label: o, value: o }
    const { hide, component, slots = {}, ...others } = item
    if (hide) return
    const itemOptions = item?.options
    //el-select选项分组的情况
    if (itemOptions?.length) {
      slots.default = () => children(item.options, el_select)
    }
    if (component) return h(component, others, slots)
    const name = item.componentName
      ? item.componentName
      : itemOptions?.length
        ? el_option_group
        : getChildrenComponentName(componentName)
    return h(resolveComponent(name), others, slots)
  })
}

const renderComponent = (props: ComponentProps, model = {}) => {
  const { prop, options, render, slots = {}, component, ...others } = props
  if (render) return render(model)
  // 组件名设置
  const [componentName, defaultProps] = getDefaultProps(props)
  // 默认值设置, 已有的属性优先
  mergeWith(others, defaultProps, (t, s) => t ?? s)
  // 数据绑定
  others.model = others.model || model
  others.modelValue = others.modelValue || others.model[prop] //初始值
  if (others['onUpdate:modelValue'] == void 0) {
    others['onUpdate:modelValue'] = (value: any) => {
      others.model[prop] = value
    }
  }
  // date组件不能传default,Options的值可以是基本类型（String,Number）eg:[1,2],["M_NKE","N_HIJ"]或者对象{label:'ok',value:1}
  if (Array.isArray(options) && options?.length && !slots.default) {
    slots.default = () => children(options, componentName)
  }
  if (component) return h(component, others, slots)
  return h(resolveComponent(componentName), others, slots)
}

export default renderComponent
