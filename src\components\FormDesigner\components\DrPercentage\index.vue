<template>
  <div class="fc-dr-input-number">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <div class="model-value">{{ formatModelValue }}</div>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <div class="wrapper">
        <el-input-number
          v-model="modelValue"
          :min="min"
          :max="max"
          :precision="precision"
          :controls="false"
          :placeholder="placeholder"
        />
        <span class="unit">%</span>
      </div>
    </template>
  </div>
</template>
<script setup lang="ts">
import { isNumber } from 'lodash-es';
import { formatNumberWithCommas } from '../../utils';
import { ModelValueType } from '../common/DrDefaultValue/types'

type PropsType = {
  formCreateInject: any
  placeholder?: string
  disabled?: boolean
  min?: number
  max?: number
  precision?: number
  useThousandSeparator: boolean
  defaultValueConfig?: ModelValueType
}
const modelValue = defineModel<number>({ required: false, default: undefined })

const props = withDefaults(defineProps<PropsType>(), {
  disabled: false
})

// 初始化默认值
if (
  !modelValue.value &&
  props.defaultValueConfig &&
  props.defaultValueConfig.type !== 'none' &&
  props.defaultValueConfig.content
) {
  modelValue.value = Number(props.defaultValueConfig.content)
}

const formatModelValue = computed(() => {
  if (!isNumber(modelValue.value)) {
    return '-'
  }
  if (props.useThousandSeparator) {
    return `${formatNumberWithCommas(modelValue.value)}%`
  }
  return `${modelValue.value}%`
})
</script>
<style scoped lang="scss">
.fc-dr-input-number {
  width: 100%;
  display: flex;

  .wrapper {
    position: relative;
    display: flex;
    align-items: center;

    :deep(.el-input__wrapper) {
      padding-right: 30px;
    }

    .unit {
      position: absolute;
      right: 10px;
      z-index: 1;
      font-weight: 600;
    }
  }
}
</style>
