<template>
  <div class="signature-pad">
    <div class="canvas-container">
      <canvas :id="CANVAS_ID" width="800px" height="350px"></canvas>
    </div>
  </div>
</template>

<script setup lang="ts">
import { uniqueId } from 'lodash-es'
import SignaturePad from 'signature_pad'

const CANVAS_ID = uniqueId()

const canvasContext = ref<HTMLCanvasElement | null>(null)

const signaturePadInstance = ref<SignaturePad | null>(null)

const toData = () => {
  return signaturePadInstance.value?.toData()
}

const fromData = (data) => {
  signaturePadInstance.value?.fromData(data)
}

const toDataURL = () => {
  return signaturePadInstance.value?.toDataURL()
}

const fromDataURL = (dataUrl) => {
  signaturePadInstance.value?.fromDataURL(dataUrl)
}

const clear = () => {
  signaturePadInstance.value?.clear()
}

defineExpose({ toData, fromData, toDataURL, fromDataURL, clear })

onMounted(() => {
  canvasContext.value = document.getElementById(CANVAS_ID) as HTMLCanvasElement

  if (canvasContext.value) {
    signaturePadInstance.value = new SignaturePad(canvasContext.value, {
      // backgroundColor: 'rgb(0, 0, 0)'
    })
  }
})

onBeforeUnmount(() => {
  window.onresize = null
})

function resizeCanvas() {
  if (canvasContext.value === null) return
  var ratio = Math.max(window.devicePixelRatio || 1, 1)

  canvasContext.value.width = canvasContext.value.offsetWidth * ratio
  canvasContext.value.height = canvasContext.value.offsetHeight * ratio
  canvasContext.value.getContext('2d')?.scale(ratio, ratio)
  signaturePadInstance.value?.clear()
}

window.onresize = () => {
  resizeCanvas()
}
resizeCanvas()
</script>

<style scoped lang="scss">
.signature-pad {
  width: 100%;

  .canvas-container {
    border: 1px dashed #979797;
    border-radius: 4px;
  }
}
</style>
