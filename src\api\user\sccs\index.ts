import { BizTemplateVO } from '@/api/template/bizTemplate'
import request from '@/config/axios'


// 运营端业务模板库 API
export const SccsBizTemplateApi = {
  // 查询运营端业务模板库分页
  getBizTemplatePage: async (params: any) => {
    return await request.get({ url: `/system/trade-sccs/page`, params })
  },

  // 查询运营端业务模板库详情
  getBizTemplate: async (id: number) => {
    return await request.get({ url: `/system/trade-sccs/get?id=` + id })
  },

  // 新增运营端业务模板库
  createBizTemplate: async (data: BizTemplateVO) => {
    return await request.post({ url: `/system/trade-sccs/create`, data })
  },
  // 复制运营端业务模板库
  copyBizTemplate: async (data: BizTemplateVO) => {
    return await request.post({ url: `/system/trade-sccs/copy-new-biz-template`, data })
  },

  // 删除运营端业务模板库
  deleteSccs: async (id: number) => {
    return await request.delete({ url: `/system/trade-sccs/delete?id=` + id })
  },

  // 导出运营端业务模板库 Excel
  exportBizTemplate: async (params) => {
    return await request.download({ url: `/system/trade-sccs/export-excel`, params })
  },
  
  // 行业列表
  getIndustryOptions: async () => {
    return await request.get({ url: `/system/industry-manage/list` })
  },

  // 模板设计详情
  getBizTemplateDesign: async (id) => {
    return await request.get({ url: `/system/trade-sccs-design/get?bizTemplateId=${id}` })
  },
  // 修改模板设计
  updateFormWithWidget: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/update-form-with-widget`, data })
  },
  //发布模板
  publicTemplate: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/public-template`, data })
  },
  //修改表单(名字描述)
  updateFormName: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/update-form`, data })
  },
  //新增里程碑
  addMilestone: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/add-milestone`, data })
  },
  //新增表单
  addForm: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/add-form`, data })
  },
  // 修改里程碑
  updateMilestone: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/update-milestone`, data })
  },
  // 修改里程碑详情
  updateMilestoneDetail: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/update-milestone-detail`, data })
  },
  // 修改表单
  updateForm: async (data) => {
    return await request.post({ url: `/system/trade-sccs-design/update-form`, data })
  },
  // 删除里程碑
  deleteMilestone: async (id: number) => {
    return await request.delete({ url: `/system/trade-sccs-design/delete-milestone?id=` + id })
  },
  // 删除表单
  deleteForm: async (id: number) => {
    return await request.delete({ url: `/system/trade-sccs-design/delete-form?id=` + id })
  },
  //sccs详情
  getSccs: async (id: any) => {
    return await request.get({ url: `/system/trade-sccs/detail?id=` + id })
  },
}
