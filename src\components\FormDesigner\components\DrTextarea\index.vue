<template>
  <el-input
    v-model="modelValue"
    style="width: 100%"
    :rows="rows"
    type="textarea"
    :placeholder="placeholder"
  />
</template>
<script setup lang="ts">
import { ModelValueType } from '../common/DrDefaultValue/types'

type PropsType = {
  rows: number
  placeholder?: string
  defaultValueConfig?: ModelValueType
}

const props = withDefaults(defineProps<PropsType>(), {
  placeholder: 'please input'
})
const modelValue = defineModel<string>({ required: false, default: '' })

// 初始化默认值
if (
  !modelValue.value &&
  props.defaultValueConfig &&
  props.defaultValueConfig.type !== 'none' &&
  props.defaultValueConfig.content
) {
  modelValue.value = props.defaultValueConfig.content
}
</script>
<style lang=""></style>
