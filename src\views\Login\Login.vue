<template>
  <div class="bg flex-col">
    <div style="text-align:right;width: 100%;">
      <el-dropdown @command="handleCommand" style="margin:20px 50px 0px 0px">
        <span class="el-dropdown-link flex-row ai-center">
          <label class="iconfont link-system-language mh-t language-select"></label>
          <Icon :class="$attrs.class" :color="color" :size="18" class="cursor-pointer !p-0" icon="ion:language-sharp"
            style="margin-right: 6px;" />
          <span class="language-select">{{ currentLanguange }}</span>
          <Icon class="el-icon--right language-select" icon="ep:arrow-down" />
        </span>
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="followSystem">
              {{ $t('admin_common_followSystem') }}</el-dropdown-item>
            <el-dropdown-item command="zh-CN">简体中文
            </el-dropdown-item>
            <el-dropdown-item command="en-US">English
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
    <div class="center-div flex-row">

      <div class="center-left-div">
        <img style="margin: 40px 0 0 33px;" :src="logo" />
      </div>
      <div class="center-right-div">
        <el-form :model="loginData.loginForm" label-position="top" class="right-form" ref="formLogin"
          :rules="LoginRules">
          <div class="center-right-label form-title">
            {{ $t('admin_login_title') }}
          </div>
          <el-form-item prop="email" :label="$t('admin_login_username')">
            <el-input class="value-input" size="large" v-model="loginData.loginForm.username"
              :placeholder="$t('admin_login_usernamePlaceholder')" clearable />
          </el-form-item>
          <el-form-item prop="password" :label="$t('admin_login_password')">
            <el-input class="value-input" size="large" v-model="loginData.loginForm.password"
              :placeholder="$t('admin_login_passwordPlaceholder')" show-password clearable />
          </el-form-item>
          <div class="center-right-buttom-div">
            <el-button class="center-right-button" type="primary" @click="getCode()">{{
              $t('admin_login_signInFormTitle')
            }}
            </el-button>
          </div>
          <div class="flex-row buttom-tip-div">
            <el-checkbox v-model="loginData.loginForm.rememberMe" :label="$t('admin_login_rememberMe')" name="type" />
            <el-tooltip popper-class="lk-tooltip" :show-after="500" class="box-item" effect="dark" placement="bottom"
              :content="`<span>${$t('admin_login_contactUs')}<br>${$t('admin_common_ipone')}: 400-888-888</span>`"
              raw-content>
              <span class="a-span mt-s">{{ $t('admin_login_forgetPassword') }}</span>
            </el-tooltip>
          </div>
        </el-form>
      </div>
    </div>
    <div class="buttom-div  flex-row">
      <div class="buttom-text">闽ICP备17004372号</div>
      <div class="buttom-text-2 buttom-text">© Xiamen Daren Supply Chain Management Co., Ltd.</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ElLoading } from 'element-plus'
import type { RouteLocationNormalizedLoaded } from 'vue-router'
import logo from '@/assets/imgs/login-logo.png';
import * as authUtil from '@/utils/auth'
import { usePermissionStore } from '@/store/modules/permission'
import * as LoginApi from '@/api/login'
import { useFormValid } from './components/useLogin'
const { t } = useI18n()
import { useLocaleStore } from '@/store/modules/locale'
import { useLangDataStore } from '@/store/modules/lang'
import { useLocale } from '@/hooks/web/useLocale'
import { propTypes } from '@/utils/propTypes'
import { setWithExpiry, getWithExpiry } from '@/utils/sessionExpiry'
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
import { required } from '@/utils/formRule'
const { wsCache } = useCache()

defineOptions({ name: 'LoginForm' })
defineProps({
  color: propTypes.string.def('')
})
const formLogin = ref()
const { validForm } = useFormValid(formLogin)
const { currentRoute, push } = useRouter()
const permissionStore = usePermissionStore()
const redirect = ref<string>('')
const loginLoading = ref(false)
const verify = ref()

const error = ref("")
const LoginRules = {
  // tenantName: [required],
  username: [required],
  password: [required]
}
const loginData = reactive({
  isShowPassword: false,
  captchaEnable: import.meta.env.VITE_APP_CAPTCHA_ENABLE,
  tenantEnable: import.meta.env.VITE_APP_TENANT_ENABLE,
  loginForm: {
    // tenantName: 'Linkincrease Admin',
    username: 'admin',
    password: 'admin123',
    rememberMe: false
  }
})

let currentLanguange = ref(t('admin_common_followSystem'));
const localeStore = useLocaleStore()
const langStore = useLangDataStore()
const currentLang = computed(() => localeStore.getCurrentLocale)

// 获取验证码
const getCode = async () => {
  // 情况一，未开启：则直接登录
  if (loginData.captchaEnable === 'false') {
    await handleLogin({})
  } else {
    // 情况二，已开启：则展示验证码；只有完成验证码的情况，才进行登录
    // 弹出验证码
    verify.value.show()
  }
}
// 获取租户 ID
const getTenantId = async () => {
  if (loginData.tenantEnable === 'true') {
    const res = await LoginApi.getTenantIdByName(loginData.loginForm.tenantName)
    authUtil.setTenantId(res)
  }
}
// 记住我
const getCookie = () => {
  const loginForm = authUtil.getLoginForm()
  if (loginForm) {
    loginData.loginForm = {
      ...loginData.loginForm,
      username: loginForm.username ? loginForm.username : loginData.loginForm.username,
      password: loginForm.password ? loginForm.password : loginData.loginForm.password,
      rememberMe: loginForm.rememberMe ? true : false,
      // tenantName: loginForm.tenantName ? loginForm.tenantName : loginData.loginForm.tenantName
    }
  }
}
// 根据域名，获得租户信息
const getTenantByWebsite = async () => {
  const website = location.host
  const res = await LoginApi.getTenantByWebsite(website)
  if (res) {
    // loginData.loginForm.tenantName = res.name
    authUtil.setTenantId(res.id)
  }
}

const handleCommand = (command: string, type: string) => {
  const langList = {
    followSystem: t('admin_common_followSystem'),
    'zh-CN': '简体中文',
    'en-US': 'English'
  }
  const sysList = {
    en: 'en-US',
    zh: 'zh-CN'
  }
  currentLanguange.value = langList[command];
  const lang = command == 'followSystem' ? sysList[navigator.language.substring(0, 2)] : command
  if (lang === unref(currentLang).lang && type != 'init') return
  // 需要重新加载页面让整个语言多初始化
  window.location.reload()
  localeStore.setCurrentLocale({ lang })
  const { changeLocale } = useLocale()
  changeLocale(lang)
  setWithExpiry('resetLang', true, 60) // 60 秒 过期
}
const loading = ref() // ElLoading.service 返回的实例
// 登录
const handleLogin = async (params) => {
  loginLoading.value = true
  try {
    await getTenantId()
    const data = await validForm()
    if (!data) {
      return
    }
    const res = await LoginApi.login(loginData.loginForm)
    if (!res) {
      return
    }
    loading.value = ElLoading.service({
      lock: true,
      text: '正在加载系统中...',
      background: 'rgba(0, 0, 0, 0.7)'
    })
    if (loginData.loginForm.rememberMe) {
      authUtil.setLoginForm(loginData.loginForm)
    } else {
      authUtil.removeLoginForm()
    }
    authUtil.setToken(res)
    if (!redirect.value) {
      redirect.value = '/'
    }
    // 判断是否为SSO登录
    if (redirect.value.indexOf('sso') !== -1) {
      window.location.href = window.location.href.replace('/login?redirect=', '')
    } else {
      push({ path: redirect.value || permissionStore.addRouters[0].path })
    }
  } finally {
    loginLoading.value = false
    loading.value.close()
  }
}

watch(
  () => currentRoute.value,
  (route: RouteLocationNormalizedLoaded) => {
    redirect.value = route?.query?.redirect as string
  },
  {
    immediate: true
  }
)
onMounted(() => {
  getCookie()
  getTenantByWebsite()
})

onBeforeMount(() => {
  let type = ''
  if (!getWithExpiry('resetLang')) {
    langStore.resetLang()
    type = 'init'
  } else {
    type = 'notInit'
  }
  handleCommand(wsCache.get(CACHE_KEY.LANG) || 'followSystem', type)
})
</script>

<style lang="scss" scoped>
div {
  text-align: left;
}

.el-dropdown-link {
  cursor: pointer;
}

.el-icon-arrow-down {
  font-size: 12px;
}

.bg {
  height: 100%;
  width: 100%;
  background-image: url('@/assets/imgs/opt-login-bg.png');
  background-repeat: no-repeat;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: relative;

  .center-div {
    width: 1200px;
    height: 620px;
    background-color: white;
    border-radius: 18px;
    box-shadow: 0px 29px 48px 0px rgba(75, 142, 209, 0.27);

    .center-left-div {
      width: 600px;
      height: 100%;
      background-image: url('@/assets/imgs/opt-login-bg-left.png');

    }

    .center-right-div {
      width: 600px;
      height: 100%;

      .right-form {
        margin: 76px 86px 0px 89px;

        .center-right-label {
          font-size: 26px;
          font-weight: 600;
          text-align: left;
          color: #000000;
          line-height: 37px;
        }

        .center-right-text {
          font-size: 14px;
          font-weight: 400;
          text-align: left;
          color: #535a6d;
          line-height: 19px;
        }

        .center-right-email {
          margin-top: 40px;
        }

        .center-right-item-div {
          margin-top: 24px;
        }

        .value-input {
          width: 425px;
        }

        .verify-img {
          width: 100px;
          height: 40px;
          margin-left: 10px;
          border: 1px solid #EBEBEB;
          cursor: pointer;
          border-radius: 4px;
        }

        .center-right-buttom-div {
          margin-top: 60px;

          .center-right-button {
            width: 425px;
            height: 40px;
            background: primary;
            border-radius: 8px;
          }
        }

        .buttom-tip-div {
          height: 20px;
          justify-content: space-between;
        }

      }
    }

  }

  .buttom-div {
    width: 1200px;
    justify-content: center;
    margin-bottom: 10px;

    .buttom-text {
      font-size: 14px;
      font-weight: 400;
      text-align: left;
      color: #7a7e8c;
      line-height: 20px;
    }

    .buttom-text-2 {
      margin-left: 70px
    }
  }
}
</style>
