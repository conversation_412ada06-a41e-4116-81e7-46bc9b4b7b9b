<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item :label="t('admin_template_industryName')" prop="industryName">
        <el-input v-model="formData.industryName" maxlength="50" show-word-limit
          :placeholder="t('admin_common_inputText') " />
      </el-form-item>
      <el-form-item :label="t('admin_template_industryImg')" prop="icon">
        <ChooseIcon :colorList="iconData.colorList" :iconList="iconData.iconList" v-model:icon="formData.icon"
          v-model:color="formData.iconColor" />
      </el-form-item>
      <el-form-item :label="t('admin_common_sort')" prop="sort">
        <el-input v-model="formData.sort" type="number" laceholder="t('admin_common_inputText')" />
        <span class="tips">{{ t('admin_template_sortTip') }}</span>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('admin_common_sure') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('admin_common_cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { IndustryManageApi, IndustryManageVO } from '@/api/template/industryManage'
import ChooseIcon from '@/components/ChooseIcon/index.vue'
import { ValidUtil } from '@/utils/validateUtils';
import { FormInstance, FormRules } from 'element-plus';

/** 运营端行业管理 表单 */
defineOptions({ name: 'IndustryForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: Ref<IndustryManageVO> = ref({} as IndustryManageVO)

const validateSortFn = (rule: any, value: any, callback: any) => {
  if (value < -9999 || value > 9999) {
    callback(new Error(t('admin_rule_rangeRules', ['-9999', '9999'])))
  } else {
    callback()
  }
}

const formRules = reactive<FormRules>({
  industryName: [
    { required: true, message: t('admin_common_inputText'), trigger: 'blur' },
    { min: 1, max: 50, message: t('admin_rule_lengthRules', [1, 50]), trigger: 'blur' }
  ],
  icon: [{ required: true, message: t('admin_common_selectText'), trigger: 'change' }],
  sort: [{ required: true, validator: validateSortFn, trigger: 'blur' }]
})
const formRef: Ref<FormInstance | undefined> = ref() // 表单 Ref

const iconData = {
  colorList: ['#00AAFF', '#21CA70', '#ECBE0B', '#0089FF', '#5B73FE', '#F26B53'],
  iconList: ['link-clothing', 'link-fabric', 'link-electronics', 'link-mechanical', 'link-chemical-industry', 'link-food', 'link-medicine', 'link-furniture', 'link-toys', 'link-sports', 'link-packing', 'link-building', 'link-appliance', 'link-textile', 'link-stationery', 'link-others']
}

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await IndustryManageApi.getIndustryManage(id)
    } finally {
      formLoading.value = false
    }
  } else {
    formData.value.sort = 0
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as IndustryManageVO
    if (formType.value === 'create') {
      await IndustryManageApi.createIndustryManage(data)
      message.success(t('admin_common_createSuccess'))
    } else {
      await IndustryManageApi.updateIndustryManage(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {} as IndustryManageVO
  formRef.value?.resetFields()
}

</script>
<style lang="scss" scoped>
.tips {
  font-size: 12px;
  color: #a19e9e;
}
</style>