<template>
  <el-input v-if="widget.type === 'DrInput'" v-model="bindValue" class="width150" clearable
    @change="handleChangeWidgetValue" />
  <el-date-picker v-if="widget.type === 'DrDatePicker'" v-model="bindValue" :type="widgetDateShowType" class="width180"
    format="YYYY/MM/DD HH:mm:ss" value-format="YYYY/MM/DD HH:mm:ss" @change="handleChangeWidgetValue" />
  <el-input-number v-if="widget.type === 'DrInputNumber'" v-model="bindValue" class="width150" clearable
    @change="handleChangeWidgetValue" />
  <el-input v-if="widget.type === 'DrTextarea'" v-model="bindValue" :rows="1" resize="none" class="width150"
    type="textarea" clearable @change="handleChangeWidgetValue" />
  <el-input-number v-if="widget.type === 'DrRate'" v-model="bindValue" clearable :min="1" :precision="0" :step="1"
    :controls="false" @change="handleChangeWidgetValue" />
  <el-input-number v-if="widget.type === 'DrPercentage'" v-model="bindValue" clearable :controls="false"
    @change="handleChangeWidgetValue">
    <template #suffix>
      <span>%</span>
    </template>
  </el-input-number>
  <el-select-v2 v-if="widget.type === 'DrRadio'" v-model="bindValue" class="width180 mr10" filterable
    :options="widgetOptions" clearable :multiple="multiple" collapse-tags collapse-tags-tooltip :max-collapse-tags="2"
    @change="handleChangeWidgetValue" />
  <el-select-v2 v-if="widget.type === 'DrCheckbox'" v-model="bindValue" class="width180 mr10" filterable
    :options="widgetOptions" clearable :multiple="multiple" collapse-tags collapse-tags-tooltip :max-collapse-tags="2"
    @change="handleChangeWidgetValue" />
  <el-input v-if="widget.type === 'DrLocation'" v-model="bindValue" class="width150" clearable
    @change="handleChangeWidgetValue" />
</template>
<script lang="ts" setup>
import { nextTick, ref, watch } from 'vue'

const props = defineProps({
  widget: {
    type: Object as PropType<any>,
    default: () => { }
  },
  widgetValue: {
    type: Object as PropType<any>,
    default: () => { }
  },
  multiple: {
    type: Boolean as PropType<boolean>,
    default: false
  },
  widgetOptions: {
    type: Array as PropType<any>,
    default: () => []
  },
  widgetDateShowType: {
    type: String as PropType<any>,
    default: 'datetime'
  },
  teleported: {
    type: Boolean as PropType<boolean>,
    default: false
  }
})

const bindValue = ref<any>('')

const emit = defineEmits<{
  (e: 'handleChange', col: any, data: any): void
  (e: 'handleWidgetValue', value: string, label: string): void
}>()

const handleChangeWidgetValue = () => {
  // 在单选，多选控件的情况下获取文本内容
  let label = ''
  if (
    props.widgetOptions &&
    props.widgetOptions.length > 0 &&
    ['DrRadio', 'DrCheckbox'].includes(props.widget.type)
  ) {
    if (bindValue.value instanceof Array) {
      for (let widgetValue of bindValue.value) {
        label += `,${props.widgetOptions.find((widget) => widget.value === widgetValue)?.label}`
      }
    } else {
      label += `,${props.widgetOptions.find((widget) => widget.value === bindValue.value)?.label}`
    }
    label = label.substr(1)
  }
  emit(
    'handleWidgetValue',
    bindValue.value instanceof Array ? bindValue.value.join(',') : bindValue.value,
    label
  )
}

const clearData = () => {
  bindValue.value = null
}

watch(
  [() => props.widgetValue, () => props.multiple],
  () => {
    nextTick(() => {
      if (props.multiple) {
        bindValue.value = props.widgetValue === "" ? [] : props.widgetValue.split(',')
      } else {
        bindValue.value = props.widgetValue
      }
    })
  },
  {
    deep: true,
    immediate: true
  }
)

defineExpose({
  clearData
})
</script>
<style lang="scss" scoped>
.width150 {
  display: inline-flex;
  width: 150px !important;

  .el-select__wrapper {
    width: 100% !important;
  }

  ::v-deep(.el-input) {
    width: 100% !important;
    margin-right: 0 !important;
  }
}

.width180 {
  display: inline-flex;
  width: 180px !important;

  ::v-deep(.el-select__wrapper) {
    width: 100% !important;
  }

  ::v-deep(.el-input) {
    width: 100% !important;
    margin-right: 0 !important;
  }
}
</style>
