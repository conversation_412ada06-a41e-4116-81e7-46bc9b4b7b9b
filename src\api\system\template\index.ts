import request from '@/config/axios'

// 运营端-模版 VO
export interface TemplateVO {
  id: string // 模版ID
  templateName: string // 模版名称
  icon: string // 图标
  iconColor: string // 图标颜色
  industry: string // 行业
  type: string // 模版类型
  description: string // 描述
  enable: boolean // 是否显示
  editable: boolean // 是否编辑
  publishedDate: Date // 发布时间
  sccsCreate: boolean // 是否创建
  fromId: string // 来源ID
  baseInfoUpdateTime: Date // 基础信息更新时间
  enablePdfImport: boolean // 是否开启 pdf 导入
  specialPdfType: string // pdf模板类型
  pdfWidgetId: string // 自动保存上传pdf文件控件id
}

// 运营端-模版 API
export const TemplateApi = {
  // 查询运营端-模版分页
  getTemplatePage: async (params: any) => {
    return await request.get({ url: `/system/template/page`, params })
  },

  // 查询运营端-模版详情
  getTemplate: async (id: number) => {
    return await request.get({ url: `/system/template/get?id=` + id })
  },

  // 新增运营端-模版
  createTemplate: async (data: TemplateVO) => {
    return await request.post({ url: `/system/template/create`, data })
  },

  // 修改运营端-模版
  updateTemplate: async (data: TemplateVO) => {
    return await request.put({ url: `/system/template/update`, data })
  },

  // 删除运营端-模版
  deleteTemplate: async (id: number) => {
    return await request.delete({ url: `/system/template/delete?id=` + id })
  },

  // 导出运营端-模版 Excel
  exportTemplate: async (params) => {
    return await request.download({ url: `/system/template/export-excel`, params })
  }
}