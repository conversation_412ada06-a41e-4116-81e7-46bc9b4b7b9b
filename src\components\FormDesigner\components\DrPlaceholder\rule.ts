const name = 'DrPlaceholder'
const label = '空白占位'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-space',
  //名称
  label,
  //id,唯一!
  name,
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      props: {
        height: 10
      }
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'inputNumber',
        title: '占位高度',
        field: 'height',
        props: {
          min: 0,
          precision: 0
        }
      },
    ]
  }
}

export default Rule
