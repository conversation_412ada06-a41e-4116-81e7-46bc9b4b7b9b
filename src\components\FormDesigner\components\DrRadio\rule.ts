import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrRadio'
const label = '单选'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-radio',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      readMode: 'custom',
      props: {
        options: [
          { value: uniqueId(), label: '选项1', labelEn: '', color: '#EFEFEF', checked: false },
          { value: uniqueId(), label: '选项2', labelEn: '', color: '#EFEFEF', checked: false },
          { value: uniqueId(), label: '选项3', labelEn: '', color: '#EFEFEF', checked: false }
        ],
        layout: 'horizontal',
        placeholder: ''
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'DrTableOptions',
        field: 'options',
        title: '选项内容',
        props: {
          type: 'radio'
        },
        on: {
          updateLanguageInput(api, value) {
            api.setData('options', value)
            api.activeRule.props['options'] = value
          }
        }
      },
      {
        type: 'radio',
        field: 'layout',
        value: 'horizontal',
        title: '排布方式',
        options: [
          { value: 'horizontal', label: '横向' },
          { value: 'vertical', label: '纵向' },
          { value: 'select', label: '下拉' }
        ]
      },
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData('placeholderEn', langValue)
            api.activeRule.props['placeholderEn'] = langValue
          }
        }
      }
    ]
  }
}

export default Rule
