<template>
  <div class="dr-default-value">
    <el-select :model-value="modelValue.type" size="small" style="width: 100%" @change="onTypeChange">
      <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>

    <el-cascader v-if="modelValue.type === 'custom' && componentName === 'DrAddress'" :model-value="modelValue.areas"
      :props="cascaderProps" clearable style="width: 100%" @change="handleChangeCascader" />

    <el-input v-if="
      modelValue.type === 'custom' &&
      (componentName === 'DrInput' ||
        componentName === 'DrTextarea' ||
        componentName === 'DrAddress')
    " :model-value="modelValue.content" style="width: 100%" size="small" placeholder="请输入默认内容"
      @input="onContentInput" />

    <el-input-number v-if="
      modelValue.type === 'custom' &&
      (componentName === 'DrInputNumber' || componentName === 'DrPercentage')
    " :model-value="Number(modelValue.content)" style="width: 100%" size="small" controls-position="right"
      @change="onNumberContentInput" />

    <el-date-picker v-if="modelValue.type === 'custom' && componentName === 'DrDatePicker'" v-model="dateTimeValue"
      type="datetime" style="width: 100%" size="small" @change="onDateTimeContentInput" />

    <el-tree-select v-if="modelValue.type === 'relate'" :model-value="modelValue.content" :data="renderSelectTreeData"
      node-key="_fc_id" :props="{
        label: 'title',
        disabled: (data) => {
          return data.type === 'parent'
        }
      }" :render-after-expand="false" show-checkbox check-strictly check-on-click-node size="small" style="width: 100%"
      filterable @check="onCustomContentChange" />

    <formulasEdit v-if="modelValue.type === 'formula'" :buttonText="modelValue.content ? '修改公式' : '设置公式'"
      :model-value="modelValue.content" @update:model-value="onFormulaUpdate" />
  </div>
</template>

<script setup lang="ts">
import { ModelValueType } from './types'
import formulasEdit from '../../DrFormulas/formulasEdit.vue'
import { formCreateInjectionData } from '../../../inject'
import dayjs from 'dayjs'
import { CascaderNodeValue, CascaderProps } from 'element-plus'

const injectDesigner: any = inject('designer')

const injectionData = inject(formCreateInjectionData)

type PropsType = {
  currentField: string
  formCreateInject?: any
  modelValue: ModelValueType
  componentName?: string
  formatType?: number
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => ({ type: 'none', content: '', areas: [] })
})

console.log('formCreateInject', props.formCreateInject)

const emit = defineEmits(['update:modelValue'])

const typeOptions = computed(() => {
  return [
    { label: '无', value: 'none' },
    { label: '当天', value: 'currentDay', enabledList: ['DrDatePicker'] },
    { label: '自定义', value: 'custom' },
    { label: '关联已有数据', value: 'relate', disabledList: ['DrAddress'] },
    { label: '通过公式计算', value: 'formula', disabledList: ['DrAddress'] }
  ].filter((item) => {
    if (props.componentName && item.disabledList?.includes(props.componentName)) {
      return false
    }

    if (
      props.componentName &&
      item.enabledList &&
      !item.enabledList?.includes(props.componentName)
    ) {
      return false
    }

    return true
  })
})

const relateFieldAllowList = [
  'DrInput',
  'DrTextarea',
  'DrInputNumber',
  'DrPercentage',
  'DrDatePicker',
  'DrRadio',
  'DrCheckbox',
  'DrLocation',
  'DrSCCSMemberSelect',
  'SCCS成员多选',
  'DrSCCSGroupMemberSelect',
  'DrSCCSGroupMemberSelect'
]

const onTypeChange = (type: ModelValueType['type']) => {
  if (props.componentName === 'DrAddress') {
    emit('update:modelValue', { type, content: '', areas: [] })
  } else {
    emit('update:modelValue', { type, content: '' })
  }
}

const handleChangeCascader = (value: any) => {
  emit('update:modelValue', { type: 'custom', content: props.modelValue.content, areas: value })
}

const onContentInput = (content: string | number) => {
  if (props.componentName === 'DrAddress') {
    emit('update:modelValue', { type: 'custom', content: content, areas: props.modelValue.areas })
  } else {
    emit('update:modelValue', { type: 'custom', content: content })
  }
}

const dateTimeValue = ref()

onMounted(() => {
  if (
    props.componentName === 'DrDatePicker' &&
    props.modelValue.type === 'custom' &&
    props.modelValue.content
  ) {
    dateTimeValue.value = dayjs(props.modelValue.content).format('YYYY-MM-DD HH:mm:ss')
  }
})

const requestAreasByCode = async (parentCode: CascaderNodeValue = '0') => {
  const URL =
    import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_URL +
    `/system/area/children?parentCode=${parentCode}`

  try {
    const response = await fetch(URL)
    if (!response.ok) {
      throw new Error(`Response status: ${response.status}`)
    }

    const json = await response.json()
    return json
  } catch (error: any) {
    console.error(error.message)
    return null
  }
}
const isLeaf = (item) => {
  if (item.leaf === 1) {
    return true
  }
  if (item.code.length === 2 && props.formatType === 5) {
    return true
  }

  if (item.code.length === 4 && props.formatType === 4) {
    return true
  }

  if (item.code.length >= 6 && props.formatType === 3) {
    return true
  }

  return false
}

const cascaderProps: CascaderProps = {
  value: 'code',
  label: 'name',
  lazy: true,
  lazyLoad(node, resolve) {
    requestAreasByCode(node.value).then((result) => {
      const nodes = result.data.map((item) => {
        return {
          ...item,
          leaf: isLeaf(item)
        }
      })

      resolve(nodes)
    })
  }
}

const onNumberContentInput = (content: number) => {
  emit('update:modelValue', { type: 'custom', content: content.toString() })
}

const onDateTimeContentInput = (content: string) => {
  emit('update:modelValue', {
    type: 'custom',
    content: dayjs(content).format('YYYY-MM-DD HH:mm:ss')
  })
}

const relateFieldOptions = ref<{ label: string; value: string }[]>([])

onMounted(() => {
  const fields = injectDesigner.setupState.getFormDescription()

  relateFieldOptions.value = fields
    .filter((item: any) => relateFieldAllowList.includes(item.type))
    .map((item: any) => ({ label: item.title, value: item._fc_id }))
})

const onCustomContentChange = (data: any) => {
  emit('update:modelValue', { type: 'relate', content: data._fc_id })
}

const onFormulaUpdate = (content: string) => {
  emit('update:modelValue', { type: 'formula', content })
}

const getFormAllWidget = (widgetJsonList: any) => {
  let widgetIdList: any[] = [];
  widgetJsonList.forEach(widgetItem => {
    if (!["DrCard"].includes(widgetItem.type)) {
      widgetIdList.push(widgetItem);
    }
  });
  const drCardWidget = widgetJsonList.filter(
    widgetItem => widgetItem.type === "DrCard"
  );
  drCardWidget.forEach(widget => {
    widget.children.forEach(widgetChild => {
      if (widgetChild.children) {
        widgetChild.children.forEach(widgetChilds => {
          widgetIdList.push(widgetChilds);
        });
      }
    });
  });
  return widgetIdList;
};

const handleFilterWidgetJsonList = (widgetJsonList: any[], relateFields: any, tableWidgetId: string) => {
  const drTableFormItem = getFormAllWidget(widgetJsonList).filter(widgetItem => widgetItem.type === "DrTableForm");
  let drTableWidget;
  if (drTableFormItem.length > 0) {
    for (let drTableForm of drTableFormItem) {
      const tableColumnWidgetIds = drTableForm.children.slice(1).map(child => child.children[0]._fc_id);
      if (tableColumnWidgetIds.includes(tableWidgetId)) {
        drTableWidget = drTableForm;
      }
    }

  }

  const widgetJsonDataList = getFormAllWidget(widgetJsonList).filter((widget) => (relateFields || []).includes(widget.type) && widget.field !== props.currentField);
  if (drTableWidget) {
    widgetJsonDataList.push(drTableWidget)
  }

  return widgetJsonDataList.map((widgetItem) => {
    if (widgetItem.type === 'DrRelateCard') {
      return {
        field: widgetItem._fc_id,
        title: widgetItem.title,
        type: 'parent',
        children: handleFilterWidgetJsonList(widgetItem.props.relatedValue.rules, relateFields, tableWidgetId)
      }
    } else if (widgetItem.type === 'DrTableForm') {
      const tableColumns = widgetItem.children.slice(1).map(child => child.children[0]);
      return {
        field: widgetItem._fc_id,
        title: widgetItem.title,
        type: 'parent',
        children: handleFilterWidgetJsonList(tableColumns, relateFields, tableWidgetId)
      }
    } else {
      return widgetItem
    }
  })
}

const renderSelectTreeData = computed<any[]>(() => {
  //@ts-ignore
  const tableWidgetId = props.formCreateInject.rule.props.currentWidgetId;
  const formType = unref(injectionData?.formType)
  const templateFormData = unref(injectionData?.templateTreeData)
  let relateFieldObject: any = {
    DrInput: [
      'DrCard',
      'DrInput',
      'DrTextarea',
      'DrInputNumber',
      'DrPercentage',
      'DrCheckbox',
      'DrRadio',
      'DrDatePicker',
      'DrLocation',
      'DrRate',
      'DrAddress',
      'DrSCCSMemberSelect',
      'DrSCCSGroupMemberSelect',
      'DrFormulas',
      'DrRelateCard'
    ],
    DrTextarea: [
      'DrCard',
      'DrInput',
      'DrTextarea',
      'DrInputNumber',
      'DrPercentage',
      'DrCheckbox',
      'DrRadio',
      'DrDatePicker',
      'DrLocation',
      'DrRate',
      'DrAddress',
      'DrSCCSMemberSelect',
      'DrSCCSGroupMemberSelect',
      'DrFormulas',
      'DrRelateCard'
    ],
    DrInputNumber: ['DrCard', 'DrInputNumber', 'DrPercentage', 'DrFormulas', 'DrRelateCard'],
    DrPercentage: ['DrCard', 'DrInputNumber', 'DrPercentage', 'DrFormulas', 'DrRelateCard'],
    DrDatePicker: ['DrDatePicker', 'DrFormulas', 'DrRelateCard']
  }

  const currentFormType = injectionData?.formType.value;
  const currentReplyFormType = injectionData?.msFormType.value;
  const currentWidgetJsonList = injectDesigner.setupState.getFormDescription();

  let defaultValueList: any[] = []
  const mainFormTemplate = [
    templateFormData?.find((template) => template.formType === 'MAIN_FORM')
  ].map((item) => {
    return {
      field: item.id,
      type: 'parent',
      title: item.name,
      children: handleFilterWidgetJsonList(
        currentFormType === 'MAIN_FORM' ? currentWidgetJsonList : item.widgetJsonList,
        //@ts-ignore
        relateFieldObject[props.componentName],
        tableWidgetId
      )
    }
  })
  if (formType === 'MAIN_FORM') {
    // 主表单
    defaultValueList.push(...mainFormTemplate)
  } else if (formType === 'FORM') {
    const milestoneFormList: any = templateFormData?.filter(
      (template) => template.formType !== 'MAIN_FORM'
    )
    let milestoneFormObject = {}
    let milestoneFormChildren: any = []
    let msReplyFormList: any[] = []
    for (let milestoneForm of milestoneFormList) {
      if (milestoneForm.replyForm) {
        msReplyFormList.push({
          field: milestoneForm.id,
          type: 'parent',
          title: `里程碑批复-${milestoneForm.name}`,
          children: handleFilterWidgetJsonList(
            milestoneForm.replyForm.widgetJsonList,
            //@ts-ignore
            relateFieldObject[props.componentName],
            tableWidgetId
          )
        })
      }

      const formIdList = milestoneForm.formList.map((form) => form.id)
      //@ts-ignore
      if (formIdList.includes(injectionData.currentFormId.value)) {
        milestoneFormChildren = milestoneForm.formList.map((form) => {
          const widgetJsonList = form.id === injectionData?.currentFormId.value ? currentWidgetJsonList : form.widgetJsonList;
          return {
            field: form.id,
            type: 'parent',
            title: form.name,
            children: handleFilterWidgetJsonList(
              widgetJsonList,
              //@ts-ignore
              relateFieldObject[props.componentName],
              tableWidgetId
            )
          }
        })
        milestoneFormObject = {
          field: milestoneForm.id,
          type: 'parent',
          title: milestoneForm.name,
          children: milestoneFormChildren
        }
      }
    }
    defaultValueList.push(...mainFormTemplate, milestoneFormObject, ...msReplyFormList)
  } else if (injectionData?.msFormType.value === 'REPLY_TO_MILESTONE') {
    const milestoneFormList: any = templateFormData?.filter(
      (template) => template.formType !== 'MAIN_FORM'
    )
    let msReplyFormList: any[] = []
    for (let milestoneForm of milestoneFormList) {
      if (milestoneForm.replyForm) {
        msReplyFormList.push({
          field: milestoneForm.id,
          type: 'parent',
          title: `里程碑批复-${milestoneForm.name}`,
          children: handleFilterWidgetJsonList(
            currentFormType === 'MILESTONE' && currentReplyFormType === 'REPLY_TO_MILESTONE' ? currentWidgetJsonList : milestoneForm.replyForm.widgetJsonList,
            //@ts-ignore
            relateFieldObject[props.componentName],
            tableWidgetId
          )
        })
      }
    }
    defaultValueList.push(...mainFormTemplate, ...msReplyFormList)
  } else if (injectionData?.msFormType.value === 'REPLY_TO_WORK_ORDER') {
    const milestoneFormList: any = templateFormData?.filter(
      (template) => template.formType !== 'MAIN_FORM'
    )
    let milestoneFormObject = {}
    let milestoneFormChildren: any = []
    let msReplyFormList: any[] = []
    for (let milestoneForm of milestoneFormList) {
      if (milestoneForm.replyForm) {
        msReplyFormList.push({
          field: milestoneForm.id,
          type: 'parent',
          title: `里程碑批复-${milestoneForm.name}`,
          children: handleFilterWidgetJsonList(
            milestoneForm.replyForm.widgetJsonList,
            //@ts-ignore
            relateFieldObject[props.componentName],
            tableWidgetId
          )
        })
      }
      if (!milestoneForm.workOrderReplyForm) {
        continue
      }
      if (injectionData.replyFormId.value === milestoneForm.workOrderReplyForm.id) {
        milestoneFormChildren = milestoneForm.formList.map((form) => {
          return {
            field: form.id,
            type: 'parent',
            title: form.name,
            children: handleFilterWidgetJsonList(
              form.widgetJsonList,
              //@ts-ignore
              relateFieldObject[props.componentName],
              tableWidgetId
            )
          }
        })

        if (milestoneForm.workOrderReplyForm) {
          milestoneFormChildren.push({
            field: milestoneForm.id,
            type: 'parent',
            title: `工单批复-${milestoneForm.name}`,
            children: handleFilterWidgetJsonList(
              currentFormType === 'MILESTONE' && currentReplyFormType === 'REPLY_TO_WORK_ORDER' ? currentWidgetJsonList : milestoneForm.workOrderReplyForm.widgetJsonList,
              //@ts-ignore
              relateFieldObject[props.componentName],
              tableWidgetId
            )
          })
        }

        milestoneFormObject = {
          field: milestoneForm.id,
          type: 'parent',
          title: milestoneForm.name,
          children: milestoneFormChildren
        }
      }
    }
    defaultValueList.push(...mainFormTemplate, milestoneFormObject, ...msReplyFormList)
  }
  return defaultValueList
})
</script>

<style scoped lang="scss">
.dr-default-value {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
