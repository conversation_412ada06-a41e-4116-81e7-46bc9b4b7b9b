<template>
  <div class="dr-images-upload">
    <el-upload
      v-model:file-list="images"
      action="https://run.mocky.io/v3/9d059bf9-4660-45f2-925d-ce80ad6c4d15"
      list-type="picture-card"
      multiple
      accept="image/*"
      :limit="limit"
      :on-preview="handlePictureCardPreview"
      :on-remove="handleRemove"
    >
      <el-icon><Plus /></el-icon>
    </el-upload>

    <el-dialog v-model="dialogVisible">
      <img w-full :src="dialogImageUrl" alt="Preview Image" />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Plus } from '@element-plus/icons-vue'
import { UploadProps } from 'element-plus'

type PropsType = {
  limit?: number
  maxFileSize?: number
}

withDefaults(defineProps<PropsType>(), {
  limit: 9,
  maxFileSize: 0
})

const images = defineModel<{ name: string; url: string }[]>({ required: false, default: [] })

const dialogImageUrl = ref('')
const dialogVisible = ref(false)

const handleRemove: UploadProps['onRemove'] = (uploadFile, uploadFiles) => {
  console.log(uploadFile, uploadFiles)
}

const handlePictureCardPreview: UploadProps['onPreview'] = (uploadFile) => {
  dialogImageUrl.value = uploadFile.url!
  dialogVisible.value = true
}
</script>

<style scoped lang="scss"></style>
