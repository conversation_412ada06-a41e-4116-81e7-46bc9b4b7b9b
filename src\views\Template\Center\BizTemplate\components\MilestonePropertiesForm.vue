<template>
  <div class="milestone-properties-form">
    <div class="header">
      <h4>{{ t('admin_template_milestone_properties') }}</h4>
      <el-button
        v-if="!readonly && hasUpdateTemplateInject?.hasUpdate.value"
        type="primary"
        size="small"
        @click="saveFormData"
        >保存</el-button
      >
    </div>

    <div class="form-type-radio">
      <el-radio-group v-model="propertiesFormType">
        <el-radio-button :label="t('admin_template_base_setting')" value="normal" />
        <el-radio-button :label="t('admin_template_internalization')" value="i18n" />
      </el-radio-group>
    </div>
    <div class="form-container">
      <el-form
        ref="formRef"
        label-position="top"
        :model="formData"
        :rules="RULES"
        :disabled="readonly"
      >
        <el-form-item
          v-show="propertiesFormType === 'normal'"
          :label="t('admin_template_milestoneName')"
          prop="name"
        >
          <el-input
            v-model="formData.name"
            :maxlength="100"
            :placeholder="t('admin_common_inputText')"
          />
        </el-form-item>
        <el-form-item
          v-show="propertiesFormType === 'normal'"
          :label="t('admin_template_need_approval')"
          prop="needReply"
        >
          <el-radio-group v-model="formData.needReply">
            <el-radio :value="false">{{ t('admin_template_no_need') }}</el-radio>
            <el-radio :value="true">{{ t('admin_template_need') }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <template v-if="formData.needReply">
          <el-form-item
            v-show="propertiesFormType === 'normal'"
            :label="t('admin_template_reply_type')"
            prop="replyType"
          >
            <el-select v-model="formData.replyType" :placeholder="t('admin_common_selectText')">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.ADMIN_TEMPLATE_REPLY_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-show="propertiesFormType === 'normal'"
            v-if="formData.replyType && formData.replyType !== 'REPLY_TO_MILESTONE'"
            :label="t('admin_template_normal_task_replier')"
            prop="workOrderReplyUserType"
          >
            <el-select v-model="formData.workOrderReplyUserType" placeholder="无">
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.ADMIN_TEMPLATE_REPLY_USER_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item
            v-show="propertiesFormType === 'normal'"
            v-if="formData.replyType && formData.replyType !== 'REPLY_TO_WORK_ORDER'"
            :label="t('admin_template_normal_milestone_replier')"
            prop="milestoneReplyUserType"
          >
            <el-select
              v-model="formData.milestoneReplyUserType"
              :placeholder="t('admin_common_none')"
            >
              <el-option
                v-for="dict in getDictOptions(DICT_TYPE.ADMIN_TEMPLATE_MILESTONE_REPLY_USER_TYPE)"
                :key="dict.value"
                :label="dict.label"
                :value="dict.value"
              />
            </el-select>
          </el-form-item>
        </template>
        <el-form-item
          v-show="propertiesFormType === 'normal'"
          :label="t('admin_template_create_task_button_text')"
          prop="customCreateButton"
        >
          <el-input
            v-model="formData.customCreateButton"
            :maxlength="100"
            :placeholder="t('admin_template_create_task_button_text')"
          />
        </el-form-item>
        <el-form-item
          v-show="propertiesFormType === 'normal'"
          :label="t('admin_template_task_submit_button_text')"
          prop="customCollectButton"
        >
          <el-input
            v-model="formData.customCollectButton"
            :maxlength="100"
            :placeholder="t('admin_template_task_submit_button_text_placeholder')"
          />
        </el-form-item>

        <el-tabs v-show="propertiesFormType === 'i18n'" model-value="english">
          <el-tab-pane :label="t('admin_common_enUS')" name="english">
            <el-form-item :label="`${t('admin_template_milestoneName')}(${t('admin_common_enUS')})`" prop="nameEn">
              <el-input v-model="formData.nameEn" :maxlength="100" placeholder="请输入" />
            </el-form-item>
            <el-form-item :label="`${t('admin_template_create_task_button_text')}(${t('admin_common_enUS')})`" prop="customCreateButtonEn">
              <el-input
                v-model="formData.customCreateButtonEn"
                :maxlength="100"
                :placeholder="t('admin_common_inputText')"
              />
            </el-form-item>
            <el-form-item :label="`${t('admin_template_task_submit_button_text')}(${t('admin_common_enUS')})`" prop="customCollectButtonEn">
              <el-input
                v-model="formData.customCollectButtonEn"
                :maxlength="100"
                :placeholder="t('admin_common_inputText')"
              />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
    </div>
  </div>
</template>
<script setup lang="ts">
import { DICT_TYPE, getDictOptions } from '@/utils/dict'
import { FormRules } from 'element-plus'
import { ref } from 'vue'
import { BizTemplateApi } from '@/api/template/bizTemplate'
import { FormInstance } from 'element-plus'
import { ElMessage } from 'element-plus'
import { cloneDeep, isEqual } from 'lodash-es'
import { hasUpdateTemplate } from './injectionKey'

const { t } = useI18n()

const hasUpdateTemplateInject = inject(hasUpdateTemplate)

interface IFormData {
  sccsId: string | null
  id: string | null
  name: string
  nameEn: string
  needReply: boolean
  replyType?: string
  workOrderReplyUserType?: string
  milestoneReplyUserType?: string
  customCreateButton: string | null
  customCreateButtonEn: string | null
  customCollectButton: string | null
  customCollectButtonEn: string | null
}

interface IProps {
  readonly: boolean
  data: IFormData
}

const props = defineProps<IProps>()

const emit = defineEmits<{
  (e: 'save', data: { id: string }): void
}>()

const propertiesFormType = ref<'normal' | 'i18n'>('normal')

const formRef = ref<FormInstance>()

const INIT_DATA = {
  sccsId: null,
  id: null,
  name: '',
  nameEn: '',
  needReply: false,
  replyType: undefined,
  workOrderReplyUserType: undefined,
  milestoneReplyUserType: undefined,
  customCreateButton: null,
  customCreateButtonEn: null,
  customCollectButton: null,
  customCollectButtonEn: null
}

const formData = ref<IFormData>(cloneDeep(INIT_DATA))
const cacheFormData = ref<IFormData>(cloneDeep(INIT_DATA))

const RULES = reactive<FormRules<IFormData>>({
  name: [{ required: true, message: t('admin_common_inputText') }],
  replyType: [{ required: true, message: t('admin_common_selectText') }]
})

watch(
  () => props.data,
  (newPropsData) => {
    console.log('newPropsData', newPropsData)
    Object.keys(INIT_DATA).forEach((key) => {
      formData.value[key] = newPropsData[key]
      cacheFormData.value[key] = newPropsData[key]
    })

    hasUpdateTemplateInject?.setHasUpdate(false)
  },
  { immediate: true }
)

watch(
  [() => formData.value, () => cacheFormData.value],
  ([data, cacheData]) => {
    if (isEqual(data, cacheData)) {
      hasUpdateTemplateInject?.setHasUpdate(false)
    } else {
      hasUpdateTemplateInject?.setHasUpdate(true)
    }
  },
  { deep: true }
)

watch(
  () => props.readonly,
  (val) => {
    if (val === true && hasUpdateTemplateInject?.hasUpdate) {
      resetForm()
    }
  }
)

const saveFormData = async () => {
  try {
    const validateResult = await formRef.value?.validate()
    if (validateResult !== true) return
    const result = await BizTemplateApi.updateMilestoneDetail(formData.value)

    ElMessage.success('Success')
    emit('save', { id: result.data })
  } catch (error) {
    console.error(error)
    if (propertiesFormType.value !== 'normal') {
      propertiesFormType.value = 'normal'
    }
  }
}

const resetForm = () => {
  const cloneCacheFormData = cloneDeep(cacheFormData.value)
  formData.value = cloneCacheFormData
}

onUnmounted(() => {
  hasUpdateTemplateInject?.setHasUpdate(false)
})

defineExpose({ resetForm })
</script>
<style scoped lang="scss">
.milestone-properties-form {
  .header {
    background-color: #f2f2f2;
    height: 40px;
    padding: 0 10px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    & > h4 {
      margin: 0;
      font-weight: 400;
    }
  }

  .form-type-radio {
    display: flex;
    justify-content: center;
    padding: 10px 0;
  }

  .form-container {
    padding: 10px;
  }
}
</style>
