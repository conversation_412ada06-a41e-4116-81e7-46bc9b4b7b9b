<!-- 查询区域组件-->
<script lang="jsx">
import { ref } from 'vue'
import renderComponent from '../render/renderComponent'
import { defineComponent } from 'vue'
import { Search, Refresh } from '@element-plus/icons-vue'

export default defineComponent({
  name: 'lk-query',
  model: {
    prop: 'data'
  },
  props: {
    //初始化组件配置
    columns: { type: Array, default: () => [] },
    //初始化数据
    data: { type: Object, default: () => ({}) },
    //自定义操作按钮
    btnList: { type: Array, default: () => [] }
  },
  emits: ['click'],
  setup(props, { emit }) {
    const loadingBtn = ref('')
    const handleClick = (type) => {
      emit('click', type)
      loadingBtn.value = type
      setTimeout(() => {
        loadingBtn.value = ''
      }, 800)
    }

    return () => (
      <div class='lk-list-query'>
        {props.columns.map(c => {
          if (!c.type || c.type === 'input') {
            c = {
              ...c, 
              class: 'lk-query-item',
              onKeyup(e) {
                if (e.code == 'Enter') {
                  handleClick('search')
                }
              },
            }
          } else {
            c = { ...c, class: 'lk-query-item' }
          }

          return <div class="flex-row ai-center lk-query-box mb-2 mt-2">{c.label} {renderComponent(c, props.data)}</div>
        })}
        <div class="mb-2 mt-2">
          <el-button class="search" onClick={() => handleClick('search')} disabled={loadingBtn.value === 'search'} icon={Search}>
            {useI18n().t('admin_common_search')}
          </el-button>
          <el-button class="reset" onClick={() => handleClick('reset')} disabled={loadingBtn.value === 'reset'} icon={Refresh}>
            {useI18n().t('admin_common_reset')}
          </el-button>
        </div>
        {
          props.btnList.length > 0 && props.btnList.map(btn => {
            return <el-button class="mb-2 mt-2 ml-3" key={btn.key} plain disabled={loadingBtn.value === btn.key || btn.disabled} {...btn.elProps} onClick={() => handleClick(btn.key)}>
              {btn.icon && <Icon class="mr-1" icon={btn.icon} />}
              {btn.label}
            </el-button>
          })
        }
      </div>
    )
  }
})
</script>

<style lang="scss">
.lk-list-query {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  padding-left: 10px;
  .lk-query-box {
    font-size: 14px;
    margin: 4px 20px 4px 6px;
      box-sizing: border-box;
    }
    
    .lk-query-item {
      display: inline-block;
      width: 220px;
      margin-left: 12px;
    
      .el-input__wrapper {
        width: 100% !important;
        box-sizing: border-box;
      }
  }

  >* {
    vertical-align: middle;
  }
}
</style>
