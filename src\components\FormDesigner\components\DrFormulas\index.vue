<template>
  <div class="dr-formulas">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <div class="model-value">{{ formatedValue || '-' }}</div>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <span v-if="!formulaObject.formulas" class="placeholder"></span>
      <div v-else class="model-value">
        <el-tooltip
          effect="dark"
          :content="formatedValue"
          placement="top"
        >
          <div class="formulas-title">{{ formatedValue || '-' }}</div>
        </el-tooltip>
      </div>
      <!-- <div>{{ formatConfig }}</div> -->
    </template>
  </div>
</template>

<script setup lang="ts">
import { FormatConfigType } from './rule'

type PropsType = {
  modelValue?: any
  formCreateInject: any
  formulaObject: any
  imminent: boolean
  field: string
  formatConfig: FormatConfigType
}

const props = withDefaults(defineProps<PropsType>(), {
  formulaObject: {}
})

const formatedValue = computed(() => {
  if (!props.formulaObject.formulasText) return ''
  return props.formulaObject.formulasText
})
</script>

<style scoped lang="scss">
.dr-formulas {
  width: 100%;

  .placeholder {
    color: #888;
  }
}

.formulas-title {
  display: -webkit-box;
  display: -moz-box;
  display: box;
  -webkit-box-orient: vertical;
  -moz-box-orient: vertical;
  box-orient: vertical;
  line-clamp: 2;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
