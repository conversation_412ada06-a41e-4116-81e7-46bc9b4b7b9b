//组件
import * as components from './components'

//render
import lkRenderComponent from './render/renderComponent'
//表格配置界面
import lkTablePage from './hooks/use-table-page'
import lkPageClass from './hooks/use-table-page/PageClass'
import lkTableClass from './hooks/use-table/TableClass'

const install = (Vue: any) => {
  Object.values(components).forEach(component => {
    Vue.component(component.name, component)
  })
}

export default {
  install
}

export { lkTablePage, lkRenderComponent, lkPageClass, lkTableClass }
