import { shallowRef, shallowReactive, watch, onMounted, nextTick } from 'vue'
import type { Ref } from 'vue'
import type { ResType } from '../types'

type PaginationType = {
  currentPage: number
  pageSize: number
  total: number
  'page-sizes': number[]
  layout: string
  [key: string]: any
}

type FetchType<D> = (val: object) => Promise<ResType<D>>

class TableClass<D = any> {
  fetch: FetchType<D>
  selection: Ref<object[]>
  sortState: Ref<object>
  tableState: {
    loading: boolean
    data: D[]
    [key: string]: any
  }
  pagination: PaginationType
  indexMethod: (idnex: number) => number
  serialColumn: JSX.Element
  getMaxPage: () => void

  constructor(fetch: FetchType<D>, props: object, immediate: boolean = true) {
    this.fetch = fetch
    this.selection = shallowRef([])

    this.sortState = shallowRef({})
    //表格配置
    this.tableState = shallowReactive({
      loading: false,
      data: [],
      ...props,
      onSortChange: ({ prop, order }: { prop: string; order: string }) => {
        this.sortState.value = prop ? { [`${prop}_sort`]: order === 'ascending' } : {}
        this.getTableData()
      },
      onSelectionChange: (val: []) => {
        this.selection.value = val
      }
    })
    //分页配置
    this.pagination = shallowReactive({
      currentPage: 1,
      pageSize: 20,
      total: 0,
      background: true,
      'page-sizes': [10, 20, 30, 40, 50],
      layout: 'sizes, total, prev, pager, next, jumper',
      'onUpdate:page-size': (v: number) => {
        this.pagination.pageSize = v
        this.pagination.currentPage = 1
      },
      'onUpdate:current-page': (v: number) => (this.pagination.currentPage = v)
    })

    this.indexMethod = (index: number) => {
      return (this.pagination.currentPage - 1) * this.pagination.pageSize + index + 1
    }

    this.serialColumn = <el-table-column label='序号' type='index' align='center' index={this.indexMethod} width={80} />

    this.getMaxPage = () => {
      // total变换后page也会变化，但此时仍在loading
      const { currentPage: page, pageSize: size, total } = this.pagination
      if (this.tableState.data.length === 0 && page > 1)
        nextTick().then(() => {
          this.pagination.currentPage = Math.max(1, Math.min(Math.ceil(total / size), page - 1))
        })
    }

    watch([() => this.pagination.currentPage, () => this.pagination.pageSize], () => this.getTableData())
    immediate && onMounted(() => this.getTableData())
  }

  //表格数据请求
  getTableData() {
    if (this.tableState.loading) return
    this.tableState.loading = true
    this.selection.value = []
    const { currentPage: page, pageSize: size } = this.pagination
    return this.fetch?.({ ...this.sortState.value, page, size })
      .then((res: ResType<D>) => {
        this.tableState.data = res?.data ?? res?.items ?? res?.list ?? []
        this.pagination.total = res?.total ?? this.tableState.data.length
        this.getMaxPage()
      })
      .catch(() => {
        this.tableState.data = []
        this.pagination.total = 0
      })
      .finally(() => {
        this.tableState.loading = false
      })
  }
}

export default TableClass
