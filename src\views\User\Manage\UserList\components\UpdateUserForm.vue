<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px" v-loading="formLoading">
      <el-form-item :label="t('admin_user_userEmail')" prop="email">
        <el-input v-model="formData.email" :placeholder="t('admin_common_inputText')" maxlength="100" show-word-limit />
      </el-form-item>
      <el-form-item :label="t('admin_user_name')" prop="username">
        <el-input v-model="formData.username" :placeholder="t('admin_common_inputText')" maxlength="100"
          show-word-limit />
      </el-form-item>
      <el-form-item :label="t('admin_profile_sex')" prop="sex">
        <el-select v-model="formData.sex" :placeholder="t('admin_common_selectText')" clearable>
          <el-option :label="t('admin_common_man')" :value="1" />
          <el-option :label="t('admin_common_woman')" :value="2" />
        </el-select>
      </el-form-item>
      <el-form-item :label="t('admin_login_password')" prop="password">
        <el-input v-model="formData.password" type="password" :show-password="true" :rows="2" autosize show-word-limit
          maxlength="200" :placeholder="t('admin_common_inputText')" />
        <el-text v-if="isUpdateUser" type="info" size="small">{{ t('admin_update_trade_user_password_tip') }}</el-text>
      </el-form-item>
      <el-form-item :label="t('admin_profile_confirmPassword')" prop="confirmPassword">
        <el-input v-model="formData.confirmPassword" type="password" :show-password="true" :rows="2" autosize
          show-word-limit maxlength="200" :placeholder="t('admin_common_inputText')" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">{{ t('admin_common_sure') }}</el-button>
      <el-button @click="dialogVisible = false">{{ t('admin_common_cancel') }}</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { FormInstance, FormRules } from 'element-plus';
import { UserApi, TradeUserVO } from '@/api/user/userMsg';

/** 运营端业务模板库 表单 */
defineOptions({ name: 'UpdateUserForm' })
const props = defineProps({
})

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData: Ref<TradeUserVO> = ref({} as TradeUserVO)
// const validatePass = (rule: any, value: any, callback: any) => {
//   const regex =
//     /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
//   if (regex.test(value)) {
//     callback();
//   } else {
//     callback(new Error(t("admin_common_setPasswordTip")));
//   }
// };


const validateUpdatePass = (rule: any, value: any, callback: any) => {
  if (!value) {
    callback();
  }
  const regex =
    /^(?=.*([a-zA-Z].*))(?=.*[0-9].*)[a-zA-Z0-9,./<>?;\'\:\"\[\]\\\{\}\|\`\-\=\~\!\@#\$%\^&*()_+]{6,16}$/;
  if (regex.test(value)) {
    callback();
  } else {
    callback(new Error(t("admin_common_setPasswordTip")));
  }
};

const validatePwd = (rule: any, value: any, callback: any) => {
  if (value === formData.value.password) {
    callback();
  } else {
    callback(new Error(t("admin_login_passwordInconsistency")));
  }
};
const isUpdateUser = ref(false)
const formRules = computed(() => {
  const baseRules = {
    email: [
      {
        required: true,
        message: t("admin_profile_mailText"),
        trigger: "blur"
      }
    ],
    username: [
      {
        required: true,
        message: t("admin_registerUserNameTip"),
        trigger: "blur"
      }
    ],
  };
  if (!isUpdateUser.value) {
    return {
      ...baseRules,
      password: [
        {
          required: true,
          message: t("admin_login_passwordPlaceholder"),
          trigger: "blur"
        },
        { validator: validateUpdatePass, trigger: "blur" }
      ],
      confirmPassword: [
        {
          required: true,
          message: t("admin_common_confirmPasswordTip"),
          trigger: "blur"
        },
        { validator: validateUpdatePass, trigger: "blur" },
        { validator: validatePwd, trigger: "blur" }
      ]
    };
  } else {
    return {
      ...baseRules,
      password: [
        { validator: validateUpdatePass, trigger: "blur" }
      ],
      confirmPassword: [
        { validator: validatePwd, trigger: "blur" }
      ]
    };
  }
});


const formRef: Ref<FormInstance | undefined> = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    isUpdateUser.value = true
    formData.value = await UserApi.getTradeUser({ id: id })
  } else {
    isUpdateUser.value = false
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TradeUserVO
    if (formType.value === 'create') {
      data['id'] = ''
      await UserApi.addTradeUser(data)
      message.success(t('admin_common_createSuccess'))
    }
    else {
      await UserApi.updateTradeUser(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {} as TradeUserVO
  formRef.value?.resetFields()
}
</script>
