import request from '@/config/axios'

// 运营端业务模板库 VO
export interface BizTemplateVO {
  id: string // 主键
  bizTemplateName?: string // 模版名称
  icon?: string // 模版图标
  iconColor?: string // 模版图标颜色
  typeCode?: string // 创建类型
  industryId?: string // 所属行业
  status?: number // 是否启用;0否1是
  description?: string // 描述
  remark?: string // 备注
  milestoneNumber?: number // 里程碑数量
  formNumber?: number // 表单数量
}

// 运营端业务模板库 API
export const BizTemplateApi = {
  // 查询运营端业务模板库分页
  getBizTemplatePage: async (params: any) => {
    return await request.get({ url: `/system/biz-template/page`, params })
  },

  // 查询运营端业务模板库详情
  getBizTemplate: async (id: number) => {
    return await request.get({ url: `/system/biz-template/get?id=` + id })
  },

  // 新增运营端业务模板库
  createBizTemplate: async (data: BizTemplateVO) => {
    return await request.post({ url: `/system/biz-template/create`, data })
  },

  // 修改运营端业务模板库
  updateBizTemplate: async (data: BizTemplateVO) => {
    return await request.put({ url: `/system/biz-template/update`, data })
  },
  // 复制运营端业务模板库
  copyBizTemplate: async (data: BizTemplateVO) => {
    return await request.post({ url: `/system/biz-template/copy-new-biz-template`, data })
  },


  // 删除运营端业务模板库
  deleteBizTemplate: async (id: number) => {
    return await request.delete({ url: `/system/biz-template/delete?id=` + id })
  },

  // 导出运营端业务模板库 Excel
  exportBizTemplate: async (params) => {
    return await request.download({ url: `/system/biz-template/export-excel`, params })
  },

  // 行业列表
  getIndustryOptions: async () => {
    return await request.get({ url: `/system/industry-manage/list` })
  },

  // 模板设计详情
  getBizTemplateDesign: async (id) => {
    return await request.get({ url: `/system/biz-template-design/get?bizTemplateId=${id}` })
  },
  // 修改模板设计
  updateFormWithWidget: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-form-with-widget`, data })
  },
  // 设置字段显隐配置
  setFieldVisibility: async (data) => {
    return await request.post({ url: `/system/biz-template/set-field-visibility`, data })
  },
  // 设置字段显隐配置
  getFieldVisibility: async (formId) => {
    return await request.get({ url: `/system/biz-template/get-field-visibility-conditions?formId=${formId}` })
  },
  // 删除字段显隐配置
  deleteFieldVisibility: async (id) => {
    return await request.get({ url: `/system/biz-template/delete-field-visibility-conditions?relationId=${id}` })
  },
  //发布模板
  publicTemplate: async (data) => {
    return await request.post({ url: `/system/biz-template-design/public-template`, data })
  },
  //修改表单(名字描述)
  updateFormName: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-form`, data })
  },
  //新增里程碑
  addMilestone: async (data) => {
    return await request.post({ url: `/system/biz-template-design/add-milestone`, data })
  },
  //新增表单
  addForm: async (data) => {
    return await request.post({ url: `/system/biz-template-design/add-form`, data })
  },
  // 修改里程碑
  updateMilestone: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-milestone`, data })
  },
  // 修改里程碑详情
  updateMilestoneDetail: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-milestone-detail`, data })
  },
  // 获取里程碑详情
  getMilestoneDetail: async (id: string) => {
    return await request.get({ url: `/system/biz-template-design/get-milestone-detail?id=${id}` })
  },
  // 修改表单
  updateForm: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-form`, data })
  },
  // 获取表单详情
  getFormDetail: async (id: string) => {
    return await request.get({ url: `/system/biz-template-design/get-form-detail?id=${id}` })
  },
  // 删除里程碑
  deleteMilestone: async (id: number) => {
    return await request.delete({ url: `/system/biz-template-design/delete-milestone?id=` + id })
  },
  // 删除表单
  deleteForm: async (id: number) => {
    return await request.delete({ url: `/system/biz-template-design/delete-form?id=` + id })
  },
  // 取pdf导入模板类型
  getSpecialPdfList: async () => {
    return await request.get({ url: `/system/biz-template-design/get-special-pdf-type-list` })
  },
  // 取模板字段列表树
  getPdfTemplateTree: async () => {
    return await request.get({ url: `/system/biz-template-design/get-pdf-template-field-tree` })
  },
  // pdf导入取表单可选的控件列表
  getWidgetPdfSelectData: async (formId: string) => {
    return await request.get({ url: `/system/biz-template-design/get-widget-for-pdf-select?formId=` + formId })
  },
  // 修改模板pdf导入配置
  updatePdfSetting: async (data) => {
    return await request.post({ url: `/system/biz-template-design/update-pdf-setting`, data })
  },
  // 取子表内的子控件列表
  getTableFormWidgetList: async (templateId: string, tableFormWidgetId: string) => {
    return await request.get({ url: `/system/biz-template-design/get-table-form-son-widget-list?templateId=` + templateId + `&tableFormWidgetId=` + tableFormWidgetId })
  },
  // 修改模板里程碑或表单顺序
  updateMsOrFormSort: async (data) => {
    return await request.post({ url: `/system/biz-template-design/change-ms-or-form-sort`, data })
  },
}
