import { localeProps } from '@/components/FormDesigner/utils'
import { formCreateInjectionData } from '../../inject'
import unique from '@form-create/utils/lib/unique'

const injectionData = inject(formCreateInjectionData)
const injectDesigner = inject('designer')

export default {
  menu: 'layout',
  icon: 'icon-col',
  label: '格子',
  name: 'DrTableFormColumn',
  //可以向内部拖入组件
  drag: true,
  mask: false,
  inside: false,
  rule() {
    return {
      type: 'DrTableFormColumn',
      field: unique(),
      props: {
        label: "自定义名称"
      },
      readMode: 'custom',
      children: [],
    };
  },
  props(_, {t}) {
    return []
  }
}
