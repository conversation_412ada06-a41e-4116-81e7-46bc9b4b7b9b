<template>
  <div class="dr-sccs-menber-select">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <template v-if="Array.isArray(currentOption)">
        <span v-if="currentOption.length === 0">--</span>
        <template v-else>
          <el-space wrap>
            <el-tag v-for="item in currentOption" :key="item.value" type="primary">{{
              item.label
            }}</el-tag>
          </el-space>
        </template>
      </template>
      <template v-else>
        <el-tag v-if="currentOption" type="primary">{{ currentOption.label }}</el-tag>
        <span v-else>--</span>
      </template>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <el-select v-model="modelValue" :multiple="multiple" clearable :placeholder="placeholder">
        <el-option
          v-for="item in options"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
  </div>
</template>

<script setup lang="ts">
type PropsType = {
  formCreateInject?: any
  multiple?: boolean
  placeholder?: string
  disabled?: boolean
}

type OptionType = {
  label: string
  value: string | number
  disabled?: boolean
}

const modelValue = defineModel<string | number | string[] | number[]>({
  required: false,
  default: undefined
})

withDefaults(defineProps<PropsType>(), {
  multiple: false,
  placeholder: '请选择',
  disabled: false
})

const options = ref<OptionType[]>([])

const currentOption = computed(() => {
  if (Array.isArray(modelValue.value)) {
    // @ts-ignore
    const option = options.value.filter((item) => modelValue.value.includes(item.value))
    return option
  } else {
    const option = options.value.find((item) => item.value === modelValue.value)
    return option
  }
})

const requestSccsMenberList = async () => {
  // const URL = import.meta.env.VITE_BASE_URL + import.meta.env.VITE_API_URL + `/system/area/children`

  try {
    // const response = await fetch(URL)
    // if (!response.ok) {
    //   throw new Error(`Response status: ${response.status}`)
    // }

    // const json = await response.json()
    // console.log(json)
    // return json
    options.value = [
      {
        label: '小兰/<EMAIL>',
        value: 1
      },
      {
        label: '小黄/<EMAIL>',
        value: 2
      },
      {
        label: '小王/<EMAIL>',
        value: 3
      },
      {
        label: '小李/<EMAIL>',
        value: 4
      }
    ]
  } catch (error: any) {
    console.error(error.message)
  }
}

requestSccsMenberList()
</script>

<style scoped lang="scss">
.dr-sccs-menber-select {
  width: 100%;
}
</style>
