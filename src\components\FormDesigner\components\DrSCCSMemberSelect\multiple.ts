import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrSCCSMemberMultipleSelect'
const label = 'SCCS成员多选'

const Rule = {
  //插入菜单位置
  menu: 'extendAdvanced',
  //图标
  icon: 'icon-select',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: 'DrSCCSMemberSelect',
      field: uniqueId(),
      title: label,
      $required: false,
      readMode: 'custom',
      props: {
        multiple: true,
        placeholder: '请选择成员'
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("placeholderEn", langValue);
            api.activeRule.props["placeholderEn"] = langValue;
          },
        },
      }
    ]
  }
}

export default Rule
