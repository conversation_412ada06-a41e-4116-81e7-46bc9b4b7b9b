<template>
  <div class="dr-rate">
    <div v-for="i in modelValue" :key="i" class="star" @click="selectScore(i)">
      <img class="star-img" src="./dr-rate-star-fill.png" alt="star" />
    </div>
    <div v-for="j in starCount - modelValue" :key="modelValue + j" class="star" @click="addScore(j)">
      <img class="star-img" src="./dr-rate-star.png" alt="star" />
    </div>
  </div>
</template>

<script setup lang="ts">
type PropsType = {
  starCount?: number
}

withDefaults(defineProps<PropsType>(), {
  starCount: 5
})

const modelValue = defineModel<number>({ required: false, default: 0 })

const selectScore = (score: number) => {
  modelValue.value = score
}

const addScore = (score: number) => {
  modelValue.value += score
}
</script>

<style scoped lang="scss">
.dr-rate {
  display: flex;
  align-items: center;
  gap: 5px;

  .star {
    width: 14px;
    height: 14px;
    cursor: pointer;
    transition: all 0.2s;

    &:hover {
      transform: scale(1.2);
    }

    .star-img {
      width: 100%;
      height: 100%;
      display: block;
    }
  }
}
</style>
