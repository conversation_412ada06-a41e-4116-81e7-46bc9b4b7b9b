import request from '@/config/axios'

// 运营端资源模板库 VO
export interface ResTemplateVO {
  id: string // 主键
  resTemplateName?: string // 模版名称
  icon?: string // 模版图标
  iconColor?: string // 模版图标颜色
  typeCode?: string // 创建类型
  industryId?: string // 所属行业
  status?: number // 是否启用;0否1是
  description?: string // 描述
  remark?: string // 备注
}

// 运营端资源模板库 API
export const ResTemplateApi = {
  // 查询运营端资源模板库分页
  getResTemplatePage: async (params: any) => {
    return await request.get({ url: `/system/res-template/page`, params })
  },

  // 查询运营端资源模板库详情
  getResTemplate: async (id: number) => {
    return await request.get({ url: `/system/res-template/get?id=` + id })
  },

  // 新增运营端资源模板库
  createResTemplate: async (data: ResTemplateVO) => {
    return await request.post({ url: `/system/res-template/create`, data })
  },

  // 修改运营端资源模板库
  updateResTemplate: async (data: ResTemplateVO) => {
    return await request.put({ url: `/system/res-template/update`, data })
  },

  // 删除运营端资源模板库
  deleteResTemplate: async (id: number) => {
    return await request.delete({ url: `/system/res-template/delete?id=` + id })
  }
}