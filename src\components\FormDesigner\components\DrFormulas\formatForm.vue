<template>
  <div class="format-form">
    <p class="title">显示类型</p>
    <!-- onValueChange($event, 'type') -->
    <el-select
      :model-value="formatValue.type"
      size="small"
      style="width: 100%"
      @change="handleValueChange($event, 'type')"
    >
      <el-option
        v-for="item in typeOptions"
        :key="item.value"
        :label="item.label"
        :value="item.value"
      />
    </el-select>
    <template v-if="formatValue.type === 'number' || formatValue.type === 'percentage'">
      <el-select
        :model-value="formatValue.precision"
        size="small"
        style="width: 100%; margin-top: 10px"
        @change="onValueChange($event, 'precision')"
      >
        <el-option
          v-for="item in formatNumberPrecisionOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
    <template v-if="formatValue.type === 'number'">
      <p class="title" style="margin-top: 10px">显示方式</p>
      <el-select
        :model-value="formatValue.formatNumberType"
        size="small"
        style="width: 100%"
        @change="onValueChange($event, 'formatNumberType')"
      >
        <el-option
          v-for="item in formatNumberTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
    <template v-if="formatValue.type === 'date'">
      <p class="title" style="margin-top: 10px">显示格式</p>
      <el-select
        :model-value="formatValue.formatDateType"
        size="small"
        style="width: 100%"
        @change="onValueChange($event, 'formatDateType')"
      >
        <el-option
          v-for="item in formatDateTypeOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </template>
  </div>
</template>

<script setup lang="ts">
const formatValue = ref<any>({});
const typeOptions = [
  { value: 'number', label: '数字' },
  { value: 'percentage', label: '百分比' },
  { value: 'text', label: '文本' },
  { value: 'date', label: '日期' }
]

const formatNumberPrecisionOptions = [
  { value: 0, label: '保留0位小数(1)' },
  { value: 1, label: '保留1位小数(1.1)' },
  { value: 2, label: '保留2位小数(1.11)' },
  { value: 3, label: '保留3位小数(1.111)' },
  { value: 4, label: '保留4位小数(1.1111)' },
  { value: 5, label: '保留5位小数(1.11111)' },
  { value: 6, label: '保留6位小数(1.111111)' }
]

const formatNumberTypeOptions = [
  { value: 'none', label: '无' },
  { value: 'useThousandSeparator', label: '千分位' }
]

const formatDateTypeOptions = [
  { value: 'year', label: '年' },
  { value: 'month', label: '年-月' },
  { value: 'date', label: '年-月-日' },
  { value: 'datetime', label: '年-月-日-时-分' },
  { value: 'datetimesecond', label: '年-月-日-时-分-秒' }
]

type PropsType = {
  modelValue: {
    type: 'number' | 'percentage' | 'text' | 'date'
    precision: number
    formatNumberType?: 'none' | 'useThousandSeparator'
    formatDateType: 'year' | 'month' | 'date' | 'datetime' | 'datetimesecond'
  }
}

const props = defineProps<PropsType>()

watch(() => props.modelValue, () => {
  formatValue.value = props.modelValue
}, {
  deep: true,
  immediate: true
})

const emit = defineEmits(['update:modelValue', 'change'])

const handleValueChange = (val, field) => {
  formatValue.value.precision = 0;
  onValueChange(val, field);
}

const onValueChange = (val, field) => {
  emit('update:modelValue', {
    ...formatValue.value,
    [field]: val
  })
  emit('change', {
    ...formatValue.value,
    [field]: val
  })
}
</script>

<style scoped lang="scss">
.format-form {
  width: 100%;

  .title {
    margin: 0;
  }
}
</style>
