export type ColumnType =
  | 'DrInput'
  | 'DrInputNumber'
  | 'DrRadio'
  | 'DrImagesUpload'
  | 'DrDatePicker'
  | 'DrAddress'
  | 'DrRate'
  | 'DrTextarea'
  | 'DrPercentage'
  | 'DrFormulas'
  | 'DrSCCSMemberSingleSelect'
  | 'DrSCCSMemberMultipleSelect'
  | 'DrSCCSGroupMemberSingleSelect'
  | 'DrSCCSGroupMemberMultipleSelect'
  | 'DrSignature'
  | 'DrExchangeRates'
  | 'DrTableFormColumn'

export interface ColumnItemType {
  type: ColumnType
  fieldId: string
  label: string
  dataSourceId?: string
  dataSourceWidgetId?: string
  relateButtonText?: string
}
