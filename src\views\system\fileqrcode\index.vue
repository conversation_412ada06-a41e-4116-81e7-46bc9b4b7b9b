<template>
  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="100px">
      <el-form-item label="文件编号" prop="fileCode">
        <el-input v-model="queryParams.fileCode" placeholder="请输入文件编号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="文件名称" prop="fileName">
        <el-input v-model="queryParams.fileName" placeholder="请输入文件名称" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="版本号" prop="fileVersion">
        <el-input v-model="queryParams.fileVersion" placeholder="请输入版本号" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="文件状态" prop="fileStatus">
        <el-select v-model="queryParams.fileStatus" placeholder="请选择文件状态" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="控制类型(内)" prop="fileControlType">
        <el-select v-model="queryParams.fileControlType" placeholder="请选择控制类型(内)" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="控制类型(外)" prop="fileControlTypeExternal">
        <el-select v-model="queryParams.fileControlTypeExternal" placeholder="请选择控制类型(外)" clearable class="!w-240px">
          <el-option v-for="dict in getStrDictOptions(DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE)" :key="dict.value"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="创建时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
        <el-button type="primary" plain @click="openForm('create')" v-hasPermi="['system:file-qrcode:create']">
          <Icon icon="ep:plus" class="mr-5px" /> 新增
        </el-button>
        <el-button type="success" plain @click="handleDownLoad" :loading="exportLoading"
          :disabled="multipleSelection.length === 0">
          <Icon icon="ep:download" class="mr-5px" /> 二维码下载
        </el-button>
        <el-button type="warning" plain @click="handleUpdateAllCode" :loading="exportLoading">
          <Icon icon="ep:upload" class="mr-5px" /> 初始化二维码
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" :stripe="true" :show-overflow-tooltip="true" border
      @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" />
      <el-table-column label="文件编号" align="center" prop="fileCode" />
      <el-table-column label="文件名称" align="center" prop="fileName" />
      <el-table-column label="版本号" align="center" prop="fileVersion" />
      <el-table-column label="文件状态" align="center" prop="fileStatus">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_FILE_QRCODE_TYPE" :value="scope.row.fileStatus" />
        </template>
      </el-table-column>
      <el-table-column label="控制类型(内)" align="center" prop="fileControlType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE" :value="scope.row.fileControlType" />
        </template>
      </el-table-column>
      <el-table-column label="二维码(内)" align="center" prop="fileQrcode">
        <template #default="scope">
          <el-image class="h-80px w-80px" lazy :src="scope.row.fileQrcode" :preview-src-list="[scope.row.fileQrcode]"
            preview-teleported fit="cover" />
        </template>
      </el-table-column>
      <el-table-column label="控制类型(外)" align="center" prop="fileControlTypeExternal">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_FILE_QRCODE_CONTROL_TYPE" :value="scope.row.fileControlTypeExternal" />
        </template>
      </el-table-column>
      <el-table-column label="二维码(外)" align="center" prop="fileQrcodeExternal">
        <template #default="scope">
          <el-image class="h-80px w-80px" lazy :src="scope.row.fileQrcodeExternal"
            :preview-src-list="[scope.row.fileQrcodeExternal]" preview-teleported fit="cover" />
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" :formatter="dateFormatter" width="180px" />
      <el-table-column label="操作" align="center">
        <template #default="scope">
          <el-button link type="primary" @click="openForm('update', scope.row.id)"
            v-hasPermi="['system:file-qrcode:update']">
            编辑
          </el-button>
          <el-button link type="danger" @click="handleDelete(scope.row.id)" v-hasPermi="['system:file-qrcode:delete']">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <FileQrcodeForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import JSZip from 'jszip'
import { saveAs } from 'file-saver';
import { getStrDictOptions, DICT_TYPE } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import { FileQrcodeApi, FileQrcodeVO } from '@/api/system/fileqrcode'
import FileQrcodeForm from './FileQrcodeForm.vue'
import { ElMessage } from 'element-plus'
import { forEach } from 'jszip';

/** 运营端文件二维码管理 列表 */
defineOptions({ name: 'FileQrcode' })

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const loading = ref(true) // 列表的加载中
const list = ref<FileQrcodeVO[]>([]) // 列表的数据
const total = ref(0) // 列表的总页数
const multipleSelection = ref([])
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  fileCode: undefined,
  fileName: undefined,
  fileVersion: undefined,
  fileStatus: undefined,
  fileControlType: undefined,
  fileControlTypeExternal: undefined,
  createTime: [],
})
const queryFormRef = ref() // 搜索的表单
const exportLoading = ref(false) // 导出的加载中

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await FileQrcodeApi.getFileQrcodePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 列表选中事件 */
const handleSelectionChange = (val) => {
  multipleSelection.value = val;
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}
/** 更新所有二维码 */
const handleUpdateAllCode = async () => {
  await FileQrcodeApi.updateAllCode()
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await FileQrcodeApi.deleteFileQrcode(id)
    message.success(t('admin_common_delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

/** 下载按钮操作 */
const handleDownLoad = async () => {
  //单条数据 下载不压缩打包
  if (multipleSelection.value.length == 1) {
    const qcodeFile: any = multipleSelection.value[0]
    const imageUrl = qcodeFile.fileQrcode;
    const imageUrlExternal = qcodeFile.fileQrcodeExternal;
    const imageCode = qcodeFile.fileCode;
    const imageVersion = qcodeFile.fileVersion;
    const xhr = new XMLHttpRequest();
    // 解决一条数据多张图片的情况
    const imageList: any = [imageUrl, imageUrlExternal];
    const promises: any = []; // Promise数组
    for (let i = 0; i < imageList.length; i++) {
      const response = await fetch(imageList[i]);
      const blob = await response.blob(); // 转换为Blob对象
      const url = window.URL.createObjectURL(blob); // 创建URL
      xhr.open("GET", imageList[i], true);
      xhr.responseType = "blob";
      promises.push(fetch(url).then(res => res.blob())); // 添加Promise到数组中
    }
    Promise.all(promises).then(blobs => { // 所有Promise都完成后执行回调函数
      for (let i = 0; i < blobs.length; i++) {
        const link = document.createElement('a');
        link.href = window.URL.createObjectURL(blobs[i]);
        link.download = imageList[i];
        link.setAttribute("download", `${imageCode}_${imageVersion}(${i === 0 ? '内' : '外'}).png`);
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

      }
    })
  } else {
    const imageUrls: any = []; // 替换为实际的图片URL数组
    const filenames: any = []; // 对应的文件名数组
    // multipleSelection.value.forEach((ii: any) => {
    //   imageUrls.push(ii.fileQrcode)
    //   filenames.push((ii.fileCode + '_' + ii.fileVersion + '(内)'))
    //   imageUrls.push(ii.fileQrcodeExternal)
    //   filenames.push((ii.fileCode + '_' + ii.fileVersion + '(外)'))
    // })
    for (let i = 0; i < multipleSelection.value.length; i++) {
      const img: any = multipleSelection.value[i]
      imageUrls.push(img.fileQrcode)
      filenames.push((img.fileCode + '_' + img.fileVersion + '(内)'))
      imageUrls.push(img.fileQrcodeExternal)
      filenames.push((img.fileCode + '_' + img.fileVersion + '(外)'))
    }
    createAndDownloadImageZip(imageUrls, filenames);
  }
}
/** zip压缩 */
async function createAndDownloadImageZip(images, filenames) {
  const zip = new JSZip();
  for (let i = 0; i < images.length; i++) {
    // 获取图片资源
    await fetch(images[i])
      .then(response => response.blob())
      .then(blob => {
        // 将Blob添加到ZIP包中
        zip.file(`${filenames[i]}.png`, blob);
        // 在所有图片都处理完之后生成ZIP
        if (i === images.length - 1) {
          zip.generateAsync({ type: 'blob' }).then(content => {
            // 下载ZIP文件
            saveAs(content, 'downloaded_images.zip');
          });
        }
      });
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})
</script>
