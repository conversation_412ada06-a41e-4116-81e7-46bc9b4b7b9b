<script lang="jsx">
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'ChooseIcon',
  props: {
    iconList: { type: Array, default: () => ['link-clothing'] }, //可选择icon
    colorList: { type: Array, default: () => ['#409eff'] }, //可配置颜色
    icon: { type: String, default: '' }, //icon
    color: { type: String, default: '' }, //颜色
    fontSize: { type: String, default: '30px' }, //icon大小
  },
  emits: ['update:icon', 'update:color'],
  setup(props, { attrs, slots, emit }) {
    if (!props.icon || props.icon == null) {
      emit('update:icon', props.iconList[0])
    }
    if (!props.color || props.icon == null) {
      emit('update:color', props.colorList[0])
    }
    const handleChooseIcon = (icon) => {
      emit('update:icon', icon)
    }
    const handleChooseColor = (color) => {
      emit('update:color', color)
    }
    const slot = {
      reference: () => {
        return (
          <i style={{ color: props.color, display: 'inline-block', 'font-size': props.fontSize, margin: '2px 0px ' }} class={['iconfont', props.icon]}></i>
        )
      }
    }
    return () => (
      <el-popover placement="top" width={450} trigger="click" v-slots={slot}>
        <div>
          {props.iconList.map((item, index) => {
            return <i key={index} class={['iconfont', item]} style={{ color: props.color, 'margin-right': '4px', 'font-size': props.fontSize }} onClick={() => handleChooseIcon(item)} />
          })}
        </div>
        <div class="mt-1">
          {
            props.colorList.map((item, index) => (
              <div key={index} style={{ display: 'inline-block', width: '30px', height: '30px', 'margin-right': '4px', 'border-radius': '50%', 'background-color': item }} onClick={() => handleChooseColor(item)}>
              </div>
            ))
          }
        </div>
      </el-popover>
    )
  }
})
</script>
<style lang="scss" scoped></style>
