<template>
  <doc-alert title="站内信配置" url="https://doc.iocoder.cn/notify/" />

  <ContentWrap>
    <!-- 搜索工作栏 -->
    <el-form class="-mb-15px" :model="queryParams" ref="queryFormRef" :inline="true" label-width="68px">
      <el-form-item label="输入搜索" prop="searchContent">
        <el-input v-model="queryParams.searchContent" placeholder="编码、名称、发送对象" clearable @keyup.enter="handleQuery"
          class="!w-240px" />
      </el-form-item>
      <el-form-item label="产品端" prop="templateNotifyType">
        <el-select v-model="queryParams.templateNotifyType" placeholder="请选择产品端" clearable class="!w-240px"
          @change="handleTypeChange">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE)" :key="index"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息类型" prop="type">
        <el-select v-model="queryParams.messageType" placeholder="请选择消息类型" clearable class="!w-240px">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE[queryParams.templateNotifyType.toUpperCase()])"
            :key="index" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="模板语言" prop="templateLan">
        <el-select v-model="queryParams.templateLan" placeholder="请选择语言" clearable class="!w-240px">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE.COMMON_LANGUAGE)" :key="index"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="是否已读" prop="readStatus">
        <el-select v-model="queryParams.readStatus" placeholder="请选择是否已读" clearable class="!w-240px">
          <el-option v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_ENABLED)" :key="dict.value" :label="dict.label"
            :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="发送时间" prop="createTime">
        <el-date-picker v-model="queryParams.createTime" value-format="YYYY-MM-DD HH:mm:ss" type="daterange"
          start-placeholder="开始日期" end-placeholder="结束日期"
          :default-time="[new Date('1 00:00:00'), new Date('1 23:59:59')]" class="!w-240px" />
      </el-form-item>
      <el-form-item>
        <el-button @click="handleQuery">
          <Icon icon="ep:search" class="mr-5px" /> 搜索
        </el-button>
        <el-button @click="resetQuery">
          <Icon icon="ep:refresh" class="mr-5px" /> 重置
        </el-button>
      </el-form-item>
    </el-form>
  </ContentWrap>

  <!-- 列表 -->
  <ContentWrap>
    <el-table v-loading="loading" :data="list" border>
      <el-table-column label="模板编码" align="center" prop="templateCode" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="模板名称" align="center" prop="templateName" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="产品端" align="center" prop="templateNotifyType">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE" :value="scope.row.templateNotifyType" />
        </template>
      </el-table-column>
      <el-table-column label="消息类型" align="center" prop="messageType" min-width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE[scope.row.templateNotifyType.toUpperCase()]" :value="scope.row.messageType"
            v-if="scope.row.templateNotifyType" />
        </template>
      </el-table-column>
      <el-table-column label="模板语言" align="center" prop="templateLan" min-width="120">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.COMMON_LANGUAGE" :value="scope.row.templateLan" />
        </template>
      </el-table-column>
      <el-table-column label="模板标题" align="center" prop="templateTitle" width="120" :show-overflow-tooltip="true" />
      <el-table-column label="模板内容" align="center" prop="templateContent" width="200" :show-overflow-tooltip="true" />
      <el-table-column label="发送对象" align="center" prop="account" width="180" />
      <el-table-column label="所属团队" align="center" prop="teamName" width="180" />
      <el-table-column label="跳转链接" align="center" prop="templateUrl" width="180" />

      <el-table-column label="是否已读" align="center" prop="readStatus" width="80">
        <template #default="scope">
          <dict-tag :type="DICT_TYPE.INFRA_BOOLEAN_STRING" :value="scope.row.readStatus" />
        </template>
      </el-table-column>
      <el-table-column label="发送时间" align="center" prop="createTime" width="180" :formatter="dateFormatter" />
      <el-table-column label="操作" align="center" fixed="right">
        <template #default="scope">
          <el-button link type="primary" @click="openDetail(scope.row)" v-hasPermi="['system:notify-message:query']">
            详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <!-- 分页 -->
    <Pagination :total="total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：详情 -->
  <NotifyMessageDetail ref="detailRef" />
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import { dateFormatter } from '@/utils/formatTime'
import * as NotifyMessageApi from '@/api/system/notify/message'
import NotifyMessageDetail from './NotifyMessageDetail.vue'

defineOptions({ name: 'SystemNotifyMessage' })

const loading = ref(true) // 列表的加载中
const total = ref(0) // 列表的总页数
const list = ref([]) // 列表的数据
const queryParams = reactive({
  pageNo: 1,
  pageSize: 10,
  searchContent: '',
  userType: undefined,
  userId: undefined,
  templateCode: undefined,
  messageType: undefined,
  createTime: [],
  templateNotifyType: '',
  readStatus: undefined,
  templateLan: undefined
})
const queryFormRef = ref() // 搜索的表单

/** 查询列表 */
const getList = async () => {
  loading.value = true
  try {
    const data = await NotifyMessageApi.getNotifyMessagePage(queryParams)
    list.value = data.list
    total.value = data.total
  } finally {
    loading.value = false
  }
}

/** 搜索按钮操作 */
const handleQuery = () => {
  queryParams.pageNo = 1
  getList()
}

/** 重置按钮操作 */
const resetQuery = () => {
  queryFormRef.value.resetFields()
  handleQuery()
}

/** 详情操作 */
const detailRef = ref()
const openDetail = (data: NotifyMessageApi.NotifyMessageVO) => {
  detailRef.value.open(data)
}

const handleTypeChange = () => {
  queryParams.type = undefined
}


/** 初始化 **/
onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})
</script>
