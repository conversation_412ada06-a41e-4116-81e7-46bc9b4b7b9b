<template>
  <div class="dr-relate-card-view">
    <div class="dr-relate-card-header">
      <!-- <h4 class="title">关联引用</h4> -->
      <!-- <el-button>引用数据</el-button> -->
    </div>
    <template v-if="slots.default">
      <el-row :gutter="10" style="width: 100%">
        <slot></slot>
      </el-row>
    </template>
    <template v-else>
      <div class="_fd-slot-empty">从右侧配置中编辑关联字段</div>
    </template>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'

defineOptions({
  inheritAttrs: false
})

const slots = defineSlots<{
  default?(props: any): any
}>()

type PropsType = {
  relatedValue: {
    formId: string
    rules: any[]
  }
  colSpan?: 24
  formCreateInject: any
}

const props = withDefaults(defineProps<PropsType>(), {
  colSpan: 24
})

const loadRule = () => {
  const rules =
    cloneDeep(props.relatedValue.rules).map((rule) => {
      console.log(rule)
      return rule
        ? {
          type: 'col',
          props: {
            span: rule.type === 'DrTableForm' ? 24 : props.colSpan
          },
          display: true,
          hidden: false,
          _fc_drag_tag: 'col',
          children: [rule]
        }
        : {}
    }) || []

  // eslint-disable-next-line vue/no-mutating-props
  props.formCreateInject.rule.children = [
    {
      type: 'template',
      _fc_drag_skip: true,
      children: rules
    }
  ]
}

if (props.relatedValue?.rules) {
  loadRule()
}

watch(
  () => [props.relatedValue, props.colSpan],
  () => {
    loadRule()
  },
  {
    deep: true
  }
)
</script>

<style lang="scss" scoped>
.dr-relate-card-view {
  width: 100%;

  .dr-relate-card-header {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;

    .title {
      margin: 0;
    }
  }

  ::v-deep(.dr-table-form) {
    width: 100%;
    overflow-x: auto;

    .dr-table-row {
      display: table;
      width: 100%;
      overflow-x: auto;

      .fc-form-col {
        display: table-cell;
        width: auto;

        &:first-child {
          display: none;
        }

        .fc-form-col {
          display: table-cell !important;
          width: 184px;
          height: 200px;
        }
      }
      
      // FIXME: 不知道什么原因，部署后的版本，丢失了 fc-form-col 样式
      & > .el-col.el-col-24 {
        display: table-cell;
        width: auto;

        &:first-child {
          display: none;
        }

        .el-col.el-col-24 {
          display: table-cell !important;
          width: 184px;
          height: 200px;
        }
      }

    }
  }
}
</style>
