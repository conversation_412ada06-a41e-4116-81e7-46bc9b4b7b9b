const Rule = {
  //插入菜单位置
  menu: 'extendLayout',
  //图标
  icon: 'icon-grid',
  //名称
  label: '模块分组',
  //id,唯一!
  name: 'Dr<PERSON><PERSON>',
  //是否可以操作, 除了容器类组件建议为true !
  mask: false,
  //定义组件的事件
  event: [],
  children: 'col',
  childrenLen: 2,
  subRender({ t, h, resolveComponent, subRule }) {
    return [
      {
        label: t('style.width'),
        vnode: h(resolveComponent('el-slider'), {
          size: 'small',
          min: 0,
          max: 24,
          modelValue: subRule.props.span,
          'onUpdate:modelValue': (v) => {
            subRule.props.span = v
          }
        })
      }
    ]
  },
  sfc(rule) {
    rule.type = 'elRow'
  },
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: 'DrCard',
      props: {
        header: '标题',
        shadow: 'never',
        gutter: 20
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'inputNumber',
        title: '间隔',
        field: 'gutter',
        props: { min: 20 }
      },
      // {
      //   type: 'select',
      //   field: 'justify',
      //   options: [
      //     { label: 'start', value: 'start' },
      //     { label: 'end', value: 'end' },
      //     {
      //       label: 'center',
      //       value: 'center'
      //     },
      //     { label: 'space-around', value: 'space-around' },
      //     { label: 'space-between', value: 'space-between' }
      //   ]
      // },
      // {
      //   type: 'select',
      //   field: 'align',
      //   options: [
      //     { label: 'top', value: 'top' },
      //     { label: 'middle', value: 'middle' },
      //     {
      //       label: 'bottom',
      //       value: 'bottom'
      //     }
      //   ]
      // },
      {
        type: 'LanguageInput',
        title: '标题',
        field: 'header',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("headerEn", langValue);
            api.activeRule.props["headerEn"] = langValue;
          },
        },
      }
      // {
      //   type: 'radio',
      //   title: '是否显示阴影',
      //   field: 'shadow',
      //   options: [
      //     { label: '否', value: 'never' },
      //     { label: '是', value: 'always' }
      //   ]
      // }
    ]
  }
}

export default Rule
