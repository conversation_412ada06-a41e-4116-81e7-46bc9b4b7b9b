import { defineStore } from 'pinia'
import { store } from '../index'
import axios from 'axios';
import { CACHE_KEY, useCache } from '@/hooks/web/useCache'
const { wsCache } = useCache('sessionStorage')
import { config } from '@/config/axios/config'
const { base_url } = config

interface langDataState {
  langDataList: any
}
export const useLangDataStore = defineStore('langData', {
  state: (): langDataState => {
    return {
      langDataList: {} // 语言数据
    }
  },
  getters: {
    getLangDataList(): any {
      return this.langDataList
    }
  },
  actions: {
    async setLangAll() {
      const langDataList = wsCache.get(CACHE_KEY.LANG_DATA_LIST)
      if (langDataList) {
        this.langDataList = langDataList
      } else {
        const res = await axios.get(`${base_url}/infra/internalization/list-by-type?type=admin`);
        if(res.data.code == 0) {
          wsCache.set(CACHE_KEY.LANG_DATA_LIST, res.data.data)
          this.langDataList = res.data.data
        }
      }
    },
    async getLangList(lang) {
      if (!this.langDataList[lang]?.length) {
        await this.setLangAll()
      }
      return this.langDataList[lang] || {}
    },
    resetLang() {
      wsCache.delete(CACHE_KEY.LANG_DATA_LIST)
      this.langDataList = {}
      this.setLangAll()
    }
  }
})

export const useLangDataStoreWithOut = () => {
  return useLangDataStore(store)
}
