<template>
  <div class="dr-table-col">
    <div class="dr-table-col-mask" v-if="drTableSourceClass"></div>
    <slot name="default"></slot>
  </div>
</template>

<script setup lang="ts">
import { markRaw } from 'vue';

const props = defineProps({})
const emit = defineEmits(["handleUpdateColumn"]);

const drTableSourceClass = computed(() => {
  //@ts-ignore
  if (props.formCreateInject.rule.children && props.formCreateInject.rule.children[0] && props.formCreateInject.rule.children[0].children.length > 0) {
    //@ts-ignore
    return !!props.formCreateInject.rule.children[0].children[0].children[0]?.props.dataSourceId
  }
  return false;
})

watch(() => props, () => {
  const child =
    //@ts-ignore
    props.formCreateInject?.rule?.children?.[0]?.children?.[0]?.children?.[0] || {};
  const { title, field } = child;
  emit("handleUpdateColumn", { field, title });
}, {
  deep: true
})

//@ts-ignore
const FormInstance = markRaw(props.formCreateInject.form.$form())
</script>

<style scoped lang="scss"></style>
