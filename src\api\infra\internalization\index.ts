import request from '@/config/axios'

// 国际化翻译管理 VO
export interface InternalizationVO {
  id: string // 主键
  type: string // 对应模块
  zhCn: string // 简体中文
  enUs: string // 英语
  zhTw: string // 繁体中文
  node: string // 节点
}

// 国际化翻译管理 API
export const InternalizationApi = {
  // 查询国际化翻译管理分页
  getInternalizationPage: async (params: any) => {
    return await request.get({ url: `/infra/internalization/page`, params })
  },

  // 查询国际化翻译管理详情
  getInternalization: async (node: string) => {
    return await request.get({ url: `/infra/internalization/get?node=` + node })
  },

  // 新增国际化翻译管理
  createInternalization: async (data: InternalizationVO) => {
    return await request.post({ url: `/infra/internalization/create`, data })
  },

  // 修改国际化翻译管理
  updateInternalization: async (data: InternalizationVO) => {
    return await request.put({ url: `/infra/internalization/update`, data })
  },

  // 删除国际化翻译管理
  deleteInternalization: async (node: string) => {
    return await request.delete({ url: `/infra/internalization/delete?node=` + node })
  },

  // 导出国际化翻译管理 Excel
  exportInternalization: async (params) => {
    return await request.download({ url: `/infra/internalization/export-excel`, params })
  },
  // 过滤出为空的语言
  languageIsNull: async (params: any) => {
    return await request.get({ url: `/infra/internalization/page-language-isnull`, params })
  },
  // 初始化
  Initialization: async () => {
    return await request.get({ url: `/infra/internalization/init` })
  },
  translateToEn: async (content: string) => {  
    return await request.get({ url: '/infra/internalization/translateToEn', params: { content } })
  }
}
