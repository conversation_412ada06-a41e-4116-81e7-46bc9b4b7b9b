import uniqueId from '@form-create/utils/lib/unique'

export type FormatConfigType = {
  type: 'number' | 'text' | 'date'
  precision: number
  formatNumberType?: 'percentage' | 'useThousandSeparator'
  formatDateType: 'year' | 'month' | 'date' | 'datetime' | 'datetimesecond'
}

const name = 'DrFormulas'
const label = '公式型组件'

const Rule = {
  //插入菜单位置
  menu: 'extendAdvanced',
  //图标
  icon: 'icon-value',
  //名称
  label,
  //id,唯一!
  name,
  validate: false,
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  hiddenBaseField: ['labelConfig'],
  rule({ t }) {
    //自定义组件的生成规则
    const field = uniqueId()
    return {
      type: name,
      title: label,
      field: field,
      $required: false,
      readMode: 'custom',
      props: {
        field,
        formulas: '',
        imminent: false,
        formatConfig: {
          type: 'number',
          precision: 0,
          formatNumberType: 'none',
          formatDateType: 'date'
        }
      },
      children: [],
      computed: { value: '' }
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'FormulasFormatForm',
        field: 'formatConfig',
        title: '格式化配置'
      },
      {
        type: 'DrFormulasEdit',
        title: '计算公式',
        field: 'formulaObject'
      },
      {
        type: 'switch',
        title: '实时计算',
        field: 'imminent'
      }
    ]
  }
}

export default Rule
