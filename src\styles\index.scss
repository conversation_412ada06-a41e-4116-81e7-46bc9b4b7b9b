@import './var.css';
@import 'element-plus/theme-chalk/dark/css-vars.css';
@import './reset.scss';
@import './linkin-plus.scss';

.reset-margin [class*='el-icon'] + span {
  margin-left: 2px !important;
}

// 解决抽屉弹出时，body宽度变化的问题
.el-popup-parent--hidden {
  width: 100% !important;
}

// 解决表格内容超过表格总宽度后，横向滚动条前端顶不到表格边缘的问题
.el-scrollbar__bar {
  display: flex;
  justify-content: flex-start;
}
//select下拉框最大宽度
.el-select-dropdown__wrap {
  max-width: 300px !important; 
}

.el-table {
  .el-popper {
    max-width: 50% !important; 
  }
}

// 单行省略
.single-ellipsis {
	text-overflow: ellipsis;
	white-space: nowrap;
	overflow: hidden;
}

// 鼠标触手
.cursor-p {
	cursor: pointer;
}

/* nprogress 适配 element-plus 的主题色 */
#nprogress {
  & .bar {
    background-color: var(--el-color-primary) !important;
  }

  & .peg {
    box-shadow:
      0 0 10px var(--el-color-primary),
      0 0 5px var(--el-color-primary) !important;
  }

  & .spinner-icon {
    border-top-color: var(--el-color-primary);
    border-left-color: var(--el-color-primary);
  }
}
