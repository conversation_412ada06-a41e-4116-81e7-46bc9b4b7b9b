<template>
  <div class="dr-form-visible-setting-body">
    <div class="dr-form-visible-text">当前表单字段，当满足以下条件时，才显示指定的字段</div>
    <el-button class="dr-form-visible-btn" v-if="injectionData.designerReadonly" type="primary" round size="small" @click="handleAddVisibleCondition">
      <el-icon><Plus /></el-icon>添加
    </el-button>
    <div class="dr-form-visible-region-body" v-for="visibleFormItem in cloneDeep(visibleConditionsList)" :key="visibleFormItem">
      <div class="dr-form-visible-region-top">
        <div class="dr-form-visible-region-row">
          <el-tag type="primary" effect="light">条件</el-tag>
          <span class="dr-form-visible-region-title">{{ transformConditionText(visibleFormItem.conditions) }}</span>
        </div>
        <div class="dr-form-visible-region-row">
          <el-tag type="success" effect="light">显示</el-tag>
          <span class="dr-form-visible-region-title" v-if="transformWidgetText(visibleFormItem.widgetIdList).length > 0">
            <div class="dr-form-visible-region-field" v-for="widgetItem in transformWidgetText(visibleFormItem.widgetIdList)" :key="widgetItem?._fc_id">{{ widgetItem?.title }}</div>
          </span>
        </div>
      </div>
      <div class="dr-form-visible-region-bottom" v-if="injectionData.designerReadonly">
        <el-icon size="14" @click="handleDeleteFormVisible(visibleFormItem.relationId)"><Delete /></el-icon>
        <el-icon size="14" @click="handleEditFormVisible(visibleFormItem)"><Edit /></el-icon>
      </div>
    </div>

    <el-dialog class="dr-form-visible-dialog" v-model="dialogVisible" title="字段显隐设置" width="800" :close-on-click-modal="false">
      <div class="dr-form-visible-body">
        <div class="dr-form-visible-header">
          条件可设置的字段为当前表单字段，以及主表单字段
        </div>
        <div class="dr-form-visible-title">
          满足以下条件时
        </div>
        <div class="dr-form-visible-container">
          <div v-for="(conditionItem, index) in conditionsList" :key="index">
            <div class="dr-form-visible-row">
              <div class="dr-form-visible-col" v-for="(conditionRow, conditionIndex) in conditionItem" :key="conditionIndex">
                <SearchConditions :condition="conditionRow" :conditionList="searchConditionList" @handleChangeConditions="(conditionValue:any) => handleChangeConditions(conditionValue, conditionRow)"/>
                <el-icon @click="handleObtainAppointDelete(conditionIndex, conditionItem)"><Close /></el-icon>
              </div>
              <el-button plain @click="handleAddConditionCol(conditionItem)"><el-icon><Plus /></el-icon>且条件</el-button>
            </div>
            <div class="dr-form-visible-area">
              <el-button v-if="index + 1 === conditionsList.length" plain @click="handleAddConditionRow(conditionsList)"><el-icon><Plus /></el-icon>或条件</el-button>
              <div v-else>
                或
                <el-button plain @click="handleDeleteConditionRow(conditionsList, index)">
                  <el-icon><Delete /></el-icon>删除或条件
                </el-button>
              </div>
            </div>  
          </div>      
        </div>
        <div class="dr-form-visible-footer">
          <div class="dr-form-visible-footer-top">
            <div class="dr-form-visible-footer-left">
              <div class="dr-form-visible-footer-title">显示此表单指定字段</div>
              <div class="dr-form-visible-footer-tip">满足以上条件时，显示指定字段，未满足时隐藏指定字段</div>
            </div>
            <div class="dr-form-visible-footer-right">
              <el-button type="primary" round size="small" @click="handleAddVisibleFields">
                <el-icon><Plus /></el-icon>添加
              </el-button>
            </div>
          </div>
          <div class="dr-form-visible-field">
            <el-row :gutter="30" v-if="transferValue.length !== 0">
              <el-col :span="6" v-for="field in transferValue" :key="field">
                <div class="dr-form-visible-field-col">
                  <div class="dr-form-visible-field-text">{{ handleGetTransferValueName(field) }}</div>
                  <el-icon @click="handleDeleteField(field)"><Close /></el-icon>
                </div>
              </el-col>
            </el-row>
            <el-empty v-else :image-size="60" description="请设置需要显示的字段" />
          </div>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
    <el-dialog class="dr-form-field-visible-dialog" v-model="dialogFieldVisible" title="字段列表" width="580">
      <el-transfer
        v-model="transferValue"
        filterable
        :props="{
          key: 'value',
          label: 'title',
        }"
        :titles="['待选列表', '已选列表']"
        :data="transferData"
      >
        <template #left-empty>
          <el-empty :image-size="60" description="No data" />
        </template>
        <template #right-empty>
          <el-empty :image-size="60" description="No data" />
        </template>
      </el-transfer>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="dialogFieldVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSaveTransferValue">确定</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Plus, Close, Delete, Edit } from '@element-plus/icons-vue';
import { cloneDeep } from 'lodash-es';
import { getDictLabel } from '@/utils/dict'
import { formCreateInjectionData } from '../../../inject';
import { BizTemplateApi } from '@/api/template/bizTemplate';
import SearchConditions from "./SearchConditions.vue";
import { DateRangeOptions, DatePickerOptions } from "./enum";
import { ElMessage, ElMessageBox } from 'element-plus'

type PropsType = {
  modelValue: any
}

const dialogVisible = ref<boolean>(false);
const dialogFieldVisible = ref<boolean>(false);
const transferValue = ref<any[]>([]);
const transferData = ref<any[]>([]);
const selectedWidgetList = ref<any[]>([]);
const injectDesigner: any = inject('designer');
const injectionData: any = inject(formCreateInjectionData);
const conditionsList: any = ref<any[]>([[{}]])
const visibleConditionsList: any = ref<any[]>([]);
const relationId = ref<string>("");
const dialogState = ref<string>("add");

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => ([])
})

const emit = defineEmits(['update:modelValue'])

const transformWidgetText = (widgetIdList: any[]) => {
  const widgetDataList = widgetIdList.map(widgetId => injectDesigner.setupState.getFormDescription().find(widgetItem => widgetItem._fc_id === widgetId))
  return widgetDataList;
}

const handleObtainAppointDelete = (index: number, conditionData: any) => {
  conditionData.splice(index, 1);
}

const transformConditionText = (conditionsList: any) => {
  let perhapsTexts = "";
  conditionsList.forEach(perhapsConditions => {
    // 或者条件
    let perhapsText = "";
    perhapsConditions.forEach(alsoConditions => {
      // 并且条件
      let conditionText = "";
      let dictType = "";
      if (["DrInputNumber", "DrRate", "DrPercentage"].includes(alsoConditions.firstDataObject.fieldType)) {
        dictType = "operation_number"
      } else if (["DrDatePicker"].includes(alsoConditions.firstDataObject.fieldType)) {
        dictType = "operation_date"
      } else {
        dictType = "operation_input"
      }
      if (['IS_NULL', 'IS_NOT_NULL'].includes(alsoConditions.operator)) {
        conditionText += `${alsoConditions.firstDataObject.label}${getDictLabel(dictType, alsoConditions.operator.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()))}`;
      } else {
        conditionText += `${alsoConditions.firstDataObject.label}${getDictLabel(dictType, alsoConditions.operator.toLowerCase().replace(/_([a-z])/g, (_, letter) => letter.toUpperCase()))}`;
        if (alsoConditions.fieldTypeEnum === "CUSTOM") {
          if (alsoConditions.firstDataObject.fieldType === "DrDatePicker") {
            if (alsoConditions.rangeDateType) {
              const dateRangeObject:any = DateRangeOptions.find(rangeOption => rangeOption.value === alsoConditions.rangeDateType);
              conditionText += `${dateRangeObject.label}`;
            } else if (alsoConditions.customerDateType) {
              const dateRangeObject:any = DatePickerOptions.find(rangeOption => rangeOption.value === alsoConditions.customerDateType);
              conditionText += `${dateRangeObject.label}`;
            }
          }
          conditionText += `${alsoConditions.value}`;
          if (alsoConditions.firstDataObject.fieldType === "DrDatePicker") {
            const dateType = alsoConditions.dateType === 'day' ? '天' : alsoConditions.dateType === 'month' ? '月' : '年';
            conditionText += `${dateType}`;
          }
        } else {
          conditionText += `${alsoConditions.twoDataObject.label}`;
        }
      }
      perhapsText += ` 并且 ${conditionText}`
    })
    perhapsTexts += ` 或者 ${perhapsText.substr(4)}`
  })
  return perhapsTexts.substr(4);
}

const handleTransferData = () => {
  const transferDataList = dialogState.value === 'edit' ? injectDesigner.setupState.getFormDescription() : injectDesigner.setupState.getFormDescription().filter(widget => !selectedWidgetList.value.includes(widget._fc_id));
  const transferFieldList = transferDataList.map(transferField => {
    return {
      title: transferField.title,
      value: transferField._fc_id,
      disabled: false
    }
  });
  return transferFieldList;
}

const handleGetTransferValueName = (field: string) => {
  const widgetTransferItem = injectDesigner.setupState.getFormDescription().find(widget => widget._fc_id === field);
  return widgetTransferItem.title;
}

const handleChangeConditions = (conditionData: any, conditionValue: any) => {
  Object.assign(conditionValue, conditionData)
}

const handleAddConditionCol = (row: any) => {
  row.push({
    firstData: [],
    twoData: [],
    operator: "",
    fieldTypeEnum: "",
    workOrderRange: "",
    rangeDateType: "",
    customerDateType: "",
    value: "",
    validate: true
  })
}

const handleAddConditionRow = (conditionList: any) => {
  conditionList.push([{ "firstData": [], "twoData": [], "operator": "", "fieldTypeEnum": "", "workOrderRange": "", "rangeDateType": "", "customerDateType": "", "value": "", "validate": true }])
}

const handleDeleteConditionRow = (conditionList: any, index: number) => {
  conditionList.splice(index + 1, 1)
}

const handleConfirm = async () => {
  const { msFormType, replyFormId } = injectionData;
  const data = await BizTemplateApi.setFieldVisibility({
    sccsId: injectionData.msSccsId.value,
    relationId: relationId.value,
    formId: msFormType.value === 'REPLY_TO_WORK_ORDER' || msFormType.value === 'REPLY_TO_MILESTONE' ? replyFormId.value : injectionData.currentFormId.value,
    conditions: conditionsList.value,
    widgetIdList: transferValue.value
  })
  if (data) {
    const dataList = cloneDeep(props.modelValue);
    dataList.push({ 
      conditions: conditionsList.value,
      widgetIdList: transferValue.value
    })
    const formId = injectionData.msFormType.value === 'REPLY_TO_WORK_ORDER' || injectionData.msFormType.value === 'REPLY_TO_MILESTONE' ? injectionData.replyFormId.value : injectionData.currentFormId.value;
    const data = await BizTemplateApi.getFieldVisibility(formId);
    visibleConditionsList.value = data;
    let widgetIdList:any = [];
    data.forEach((widget: any) => widgetIdList = [...widgetIdList, ...widget.widgetIdList]);
    selectedWidgetList.value = widgetIdList
    emit('update:modelValue', dataList)
    dialogVisible.value = false;
  }
}

const handleAddVisibleCondition = () => {
  relationId.value = "";
  conditionsList.value = [[{
    firstData: [],
    twoData: [],
    operator: "",
    fieldTypeEnum: "",
    workOrderRange: "",
    rangeDateType: "",
    customerDateType: "",
    value: "",
    validate: true
  }]];
  dialogState.value = "add";
  transferValue.value = [];
  dialogVisible.value = true;
}

const handleAddVisibleFields = () => {
  const transferDataList = handleTransferData();
  let conditionWidgetList:any[] = [];
  for (let condition of conditionsList.value) {
    for (let conditionItem of condition) {
      conditionWidgetList.push(conditionItem.firstDataObject.value);
    }
  }
  transferData.value = transferDataList.filter(transferData => !conditionWidgetList.includes(transferData.value));
  dialogFieldVisible.value = true;
}

const allowedUseWidgetList = (widgetJsonList: any, widgetType: any, widgetId: string | null) => {
  const widgetTypeList = widgetType ? widgetType : ["DrInput", "DrTextarea", "DrInputNumber", "DrPercentage", "DrRadio", "DrCheckbox", "DrDatePicker", "DrLocation", "DrRate", "DrFormulas", "DrSCCSMemberSelect", "DrSCCSGroupMemberSelect"];      
  return widgetId ? widgetJsonList.filter(widget => widgetTypeList.includes(widget.type) && widget._fc_id !== widgetId && !transferValue.value.includes(widget._fc_id)) : widgetJsonList.filter(widget => widgetTypeList.includes(widget.type) && !transferValue.value.includes(widget._fc_id))
}

const LoopNestingWidgetList = (widgetJsonList: any, widgetType: any, widgetId: string | null) => {
  let allWidgetJsonList = []
  for (let widget of widgetJsonList) {
    if (widget.type === "DrCard") {
      const cardChildList = widget.children.filter(child => child.children && child.children[0]).map(childWidget => childWidget.children[0])
      allWidgetJsonList = allWidgetJsonList.concat(cardChildList);
    } else {
      //@ts-ignore
      allWidgetJsonList.push(widget)
    }
  }
  return allowedUseWidgetList(allWidgetJsonList, widgetType, widgetId);
}

const getMainFormCascaderData = (conditionList: any, widgetType: any, widgetId: any) => {
  const mainFormData = conditionList.filter(widgetForm => widgetForm.formType === "MAIN_FORM");
 
  const list = mainFormData.map(widgetForm => {
    const type = widgetForm.formType.toLowerCase().replace(/_([a-z])/g, function(match, letter) {
      return letter.toUpperCase();
    });

    let childrenList: any[] = []
    childrenList.push({
      deleted: false,
      field: null,
      fieldType: null,
      id: null,
      label: "表单字段",
      type: widgetForm.formType === 'MAIN_FORM' ? "form" : type,
      value: widgetForm.id,
      children: LoopNestingWidgetList(widgetForm.widgetJsonList, widgetType, widgetId).map(widgetChild => {
        return {
          deleted: false,
          field: widgetChild._fc_id,
          fieldType: widgetChild.type,
          fromId: widgetForm.id,
          id: null,
          label: widgetChild.title,
          type: "form",
          value: widgetChild._fc_id,
          props: widgetChild.props
        }
      })
    })

    return {
      deleted: false,
      field: null,
      fieldType: null,
      id: null,
      label: widgetForm.name,
      type: widgetForm.formType === 'MAIN_FORM' ? "main" : type,
      value: widgetForm.formType === 'MAIN_FORM' ? "mainForm" : widgetForm.id,
      children: childrenList
    }
  })
  return list;
}

const searchConditionList = computed(() => {
  return handleObtainAppointWidgetType();
})

console.log(injectionData.designerReadonly)

const handleObtainAppointWidgetType = (widgetType?: string, widgetId?: any) => {
  const getLatestWidgetJsonList = () => injectDesigner.setupState.getFormDescription();
  const updateCurrentFormWidgets = (formData: any) => {
    if (!formData) return;

    const latestWidgetJsonList = getLatestWidgetJsonList();

    switch (formData.formType) {
      case 'MAIN_FORM':
        Object.assign(formData, { widgetJsonList: latestWidgetJsonList });
        break;
      case 'MILESTONE':
        if (
          injectionData?.msFormType.value === 'REPLY_TO_MILESTONE' &&
          formData.replyForm?.id === injectionData.replyFormId.value
        ) {
          Object.assign(formData.replyForm, { widgetJsonList: latestWidgetJsonList });
        } else if (
          injectionData?.msFormType.value === 'REPLY_TO_WORK_ORDER' &&
          formData.workOrderReplyForm?.id === injectionData.replyFormId.value
        ) {
          Object.assign(formData.workOrderReplyForm, { widgetJsonList: latestWidgetJsonList });
        } else if (formData.formList) {
          const targetForm = formData.formList.find(
            (form: any) => form.id === injectionData.currentFormId.value
          );
          if (targetForm) {
            Object.assign(targetForm, { widgetJsonList: latestWidgetJsonList });
          }
        }
        break;
    }
  };

  const index = injectionData?.templateTreeData.value.findIndex(template => template.id === injectionData.currentFormId.value);
  const templateData = injectionData?.templateTreeData.value.find(template => template.id === injectionData.currentFormId.value);
  
  if (templateData && templateData.formType === 'MAIN_FORM') {
    injectionData?.templateTreeData.value.splice(index, 1, Object.assign(templateData, { widgetJsonList: injectDesigner.setupState.getFormDescription() }));
  } 
  // 如果是里程碑相关表单
  else {
    const milestoneData = injectionData?.templateTreeData.value.find(template => 
      template.formType === 'MILESTONE' && (
        template.id === injectionData.currentFormId.value ||
        template.formList?.some(form => form.id === injectionData.currentFormId.value)
      )
    );
    if (milestoneData) {
      updateCurrentFormWidgets(milestoneData);
    }
  }

  const conditionList = injectionData?.templateTreeData.value;
  let cascaderData:any[] = [];
  const mainFormData = getMainFormCascaderData(conditionList, widgetType, widgetId);
  cascaderData.push(...mainFormData);

  if (injectionData?.formType.value === 'MAIN_FORM') {
    // 主表单
    const milestoneDataList = conditionList.filter(widgetForm => widgetForm.formType === "MILESTONE");

    const mileStoneWidgetData = milestoneDataList.filter(milestone => milestone.replyForm).map(milestoneItem => {
      const type = milestoneItem.formType.toLowerCase().replace(/_([a-z])/g, function(match, letter) {
        return letter.toUpperCase();
      });
      let childrenList: any[] = [];
      if (milestoneItem.replyForm) {
        childrenList.push({
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: "里程碑批复",
          type: "msReplyForm",
          value: milestoneItem.replyForm.id,
          children: LoopNestingWidgetList(milestoneItem.replyForm.widgetJsonList, widgetType, widgetId).map(widgetChild => {
            return {
              deleted: false,
              field: widgetChild._fc_id,
              fieldType: widgetChild.type,
              fromId: milestoneItem.replyForm.id,
              id: null,
              label: widgetChild.title,
              type: "msReplyForm",
              value: widgetChild._fc_id,
              props: widgetChild.props
            }
          })
        })
      }

      return {
        deleted: false,
        field: null,
        fieldType: null,
        id: null,
        label: milestoneItem.name,
        type: type,
        value: milestoneItem.id,
        children: childrenList
      }
    })

    cascaderData.push(...mileStoneWidgetData);
  } else if (injectionData?.formType.value === 'FORM') {
    // 工单
    const milestoneDataList = conditionList.filter(widgetForm => widgetForm.formType === "MILESTONE").find(milestoneTemplateData => milestoneTemplateData.formList.filter(data => data.id === injectionData?.currentFormId.value).length > 0);
    const mileStoneWidgetData = [milestoneDataList].map(milestoneItem => {
      const type = milestoneItem.formType.toLowerCase().replace(/_([a-z])/g, function(match, letter) {
        return letter.toUpperCase();
      });
      let childrenList: any[] = [];
      if (milestoneDataList.formList.length > 0) {
        milestoneDataList.formList.forEach(formData => {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: formData.name,
            type: "formSysField",
            value: formData.id,
            children: LoopNestingWidgetList(formData.widgetJsonList, widgetType, widgetId).map(widgetChild => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: formData.id,
                id: null,
                label: widgetChild.title,
                type: "form",
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        })
      }
      
      return {
        deleted: false,
        field: null,
        fieldType: null,
        id: null,
        label: milestoneItem.name,
        type: type,
        value: milestoneItem.id,
        children: childrenList
      }
    })
    cascaderData.push(...mileStoneWidgetData);
  } else if (injectionData?.formType.value === 'MILESTONE') {
    if (injectionData?.msFormType.value === 'REPLY_TO_WORK_ORDER') {
      // 工单批复
      const milestoneDataList = conditionList.find(widgetForm => widgetForm.id === injectionData?.currentFormId.value);
      const mileStoneWidgetData = [milestoneDataList].map(milestoneItem => {
        const type = milestoneItem.formType.toLowerCase().replace(/_([a-z])/g, function(match, letter) {
          return letter.toUpperCase();
        });
        let childrenList: any[] = [];
        if (milestoneItem.replyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: "里程碑批复",
            type: "msReplyForm",
            value: milestoneItem.replyForm.id,
            children: LoopNestingWidgetList(milestoneItem.replyForm.widgetJsonList, widgetType, widgetId).map(widgetChild => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.replyForm.id,
                id: null,
                label: widgetChild.title,
                type: "msReplyForm",
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }
        if (milestoneItem.workOrderReplyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: "工单批复",
            type: "replyForm",
            value: milestoneItem.workOrderReplyForm.id,
            children: LoopNestingWidgetList(milestoneItem.workOrderReplyForm.widgetJsonList, widgetType, widgetId).map(widgetChild => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.workOrderReplyForm.id,
                id: null,
                label: widgetChild.title,
                type: "workOrderReply",
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }
        if (milestoneDataList.formList.length > 0) {
          milestoneDataList.formList.forEach(formData => {
            childrenList.push({
              deleted: false,
              field: null,
              fieldType: null,
              id: null,
              label: formData.name,
              type: "formSysField",
              value: formData.id,
              children: LoopNestingWidgetList(formData.widgetJsonList, widgetType, widgetId).map(widgetChild => {
                return {
                  deleted: false,
                  field: widgetChild._fc_id,
                  fieldType: widgetChild.type,
                  fromId: formData.id,
                  id: null,
                  label: widgetChild.title,
                  type: "form",
                  value: widgetChild._fc_id,
                  props: widgetChild.props
                }
              })
            })
          })
        }
        
        return {
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: milestoneItem.name,
          type: type,
          value: milestoneItem.id,
          children: childrenList
        }
      })
      cascaderData.push(...mileStoneWidgetData);
    } else {
      // 里程碑批复
      const milestoneDataList = conditionList.find(widgetForm => widgetForm.id === injectionData?.currentFormId.value);
      const mileStoneWidgetData = [milestoneDataList].map(milestoneItem => {
        const type = milestoneItem.formType.toLowerCase().replace(/_([a-z])/g, function(match, letter) {
          return letter.toUpperCase();
        });
        let childrenList: any[] = [];
        if (milestoneItem.replyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: "里程碑批复",
            type: "msReplyForm",
            value: milestoneItem.replyForm.id,
            children: LoopNestingWidgetList(milestoneItem.replyForm.widgetJsonList, widgetType, widgetId).map(widgetChild => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.replyForm.id,
                id: null,
                label: widgetChild.title,
                type: "msReplyForm",
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }
        
        return {
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: milestoneItem.name,
          type: type,
          value: milestoneItem.id,
          children: childrenList
        }
      })
      cascaderData.push(...mileStoneWidgetData);
    }
  }
  return cascaderData;
}

const handleSaveTransferValue = () => {
  dialogFieldVisible.value = false;
}

/**
 * 删除关联字段
 * @param field 需要删除的id
 */
const handleDeleteField = (field: string) => {
  const index = transferValue.value.findIndex(transferField => transferField === field);
  transferValue.value.splice(index, 1)
}

const handleEditFormVisible = (visibleFormItem: any) => {
  conditionsList.value = visibleFormItem.conditions;
  transferValue.value = visibleFormItem.widgetIdList;
  relationId.value = visibleFormItem.relationId;
  dialogState.value = "edit";
  dialogVisible.value = true;
}

const handleDeleteFormVisible = (visibleFormId: string) => {
  ElMessageBox.confirm(
    '是否确定删除设置？',
    '删除显隐设置',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'error',
    }
  ).then(async () => {
    const data = await BizTemplateApi.deleteFieldVisibility(visibleFormId);
    ElMessage.success("删除成功！");
    if (data) {
      const formId = injectionData.msFormType.value === 'REPLY_TO_WORK_ORDER' || injectionData.msFormType.value === 'REPLY_TO_MILESTONE' ? injectionData.replyFormId.value : injectionData.currentFormId.value;
      const data = await BizTemplateApi.getFieldVisibility(formId);
      visibleConditionsList.value = data;
      let widgetIdList:any = [];
      data.forEach((widget: any) => widgetIdList = [...widgetIdList, ...widget.widgetIdList]);
      selectedWidgetList.value = widgetIdList
    }
  })
  .catch(() => {})
}

watch(() => injectionData.currentFormId.value, async () => {
  const formId = injectionData.msFormType.value === 'REPLY_TO_WORK_ORDER' || injectionData.msFormType.value === 'REPLY_TO_MILESTONE' ? injectionData.replyFormId.value : injectionData.currentFormId.value;
  if (formId) {
    const data = await BizTemplateApi.getFieldVisibility(formId);
    visibleConditionsList.value = data;
    let widgetIdList:any = [];
    data.forEach((widget: any) => widgetIdList = [...widgetIdList, ...widget.widgetIdList]);
    selectedWidgetList.value = widgetIdList;
  }
}, {
  immediate: true
})
</script>

<style scoped lang="scss">
.dr-form-visible-setting-body {
  width: 100%;

  .dr-form-visible-text {
    font-size: 10px;
    color: #606266;
  }

  .dr-form-visible-btn {
    width: 100%;
    margin: 10px 0;

    span {
      font-size: 12px;
    }

  }

  .dr-form-visible-region-body {
    border-radius: 4px;
    border: 1px solid #ccc;
    margin-bottom: 20px;

    .dr-form-visible-region-top {
      padding: 5px 10px;
      .dr-form-visible-region-row {
        .dr-form-visible-region-title {
          margin-left: 6px;

          .dr-form-visible-region-field {
            display: inline-flex;
            margin-right: 4px;
          }
        }
      }
    }
    .dr-form-visible-region-bottom {
      display: flex;
      height: 30px;
      align-items: center;
      border-top: 1px solid #ccc;

      .el-icon {
        flex: 1;
        height: 100%;
        cursor: pointer;

        &:first-child{
          border-right: 1px solid #ccc;
        }

        &:hover {
          background: #d2d6db;
        }
      }
    }
  }
}

::v-deep(.dr-form-visible-dialog) {
  .dr-form-visible-body {
    .dr-form-visible-header {
      background: #d2d6db;
      padding: 10px 15px;
    }
    .dr-form-visible-title {
      font-weight: bolder;
      font-size: 14px;
      margin: 4px 0;
    }
  }

  .dr-form-visible-container {
    margin-bottom: 3px 0 10px;

    .dr-form-visible-row {
      border: 1px solid #ccc;
      padding: 10px;

      .dr-form-visible-col {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
    }

    .dr-form-visible-area {
      margin: 10px 0;
    }
  }

  .dr-form-visible-footer {
    border-top: 1px solid #ccc;
    .dr-form-visible-footer-top {
      display: flex;
      align-items: center;

      .dr-form-visible-footer-left {
        flex: 1;
        .dr-form-visible-footer-title {
          color: #333;
          font-size: 14px;
          font-weight: bolder;
        }
        .dr-form-visible-footer-tip {
          font-size: 12px;
          color: #666;
        }
      }
    }

    .dr-form-visible-field {
      margin: 10px;
      .dr-form-visible-field-col {
        display: flex;
        align-items: center;
        border: 1px solid #d2d6db;
        border-radius: 4px;
        padding: 0 10px;
        margin-bottom: 10px;

        .dr-form-visible-field-text {
          flex: 1;
          align-items: center;
        }

        .el-icon {
          cursor: pointer;
        }
      }
    }
  }
}

::v-deep(.dr-form-field-visible-dialog) {
  .el-transfer {
    .el-transfer-panel__header {
      .el-checkbox__label {
        font-size: 12px;
      }
    }
  }
}
</style>
