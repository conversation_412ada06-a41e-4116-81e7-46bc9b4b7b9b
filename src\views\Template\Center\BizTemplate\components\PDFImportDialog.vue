<template>
  <el-dialog v-model="dialogVisible" title="PDF导入设置" width="600" :before-close="handleClose"
    :close-on-click-modal="false" :close-on-press-escape="false">
    <div class="import-body">
      <div class="import-row-header">
        <div class="import-row-title">PDF导入</div>
        <div class="import-row-widget">
          <el-switch v-model="enablePdfImport" :disabled="editState" style="--el-switch-on-color: #13ce66;" />
        </div>
      </div>
      <div class="import-container" v-if="enablePdfImport">
        <div class="import-container-header">
          <div class="import-container-title">选择PDF模板</div>
          <div class="import-container-select">
            <el-select-v2 v-model="specialPdfType" :options="templateOptions" size="small" :disabled="editState"
              placeholder="请选择pdf模板" clearable />
          </div>
        </div>
        <el-tabs type="border-card" class="card-border-tabs">
          <el-tab-pane label="非子表配置">
            <div class="import-card-header-title">
              <div class="import-card-header-tr">设置PDF模板字段与订单主表单字段关联</div>
              <div class="import-card-header-tr">必填项：带*为必填项，必填字段若未关联，贸易端导入将失败</div>
            </div>
            <div class="import-card-row">
              <div class="import-card-title">PDF附件导入</div>
              <div class="import-card-select">
                <el-select-v2 v-model="pdfWidgetId" :options="fileUploadWidgetList" size="small"
                  :value-on-clear="() => null" :empty-values="[undefined, null]" :disabled="editState" :props="{
                    value: 'widgetId',
                    label: 'title'
                  }" placeholder="请选择pdf模板" clearable />
              </div>
            </div>
            <div class="import-card-match-list">
              <div class="import-card-match-li" v-for="(pdfWidgetItem, widgetIndex) in pdfKeyAndWidgetList"
                :key="pdfWidgetItem">
                <div class="import-card-match-left">
                  <div class="import-card-match-title">PDF字段</div>
                  <el-tree-select v-model="pdfWidgetItem.pdfKey" :data="pdfWidgetOptions" :render-after-expand="false"
                    size="small" :disabled="editState" :props="{
                      label: 'title',
                      value: 'key'
                    }" />
                </div>
                <el-icon>
                  <Connection />
                </el-icon>
                <div class="import-card-match-right">
                  <div class="import-card-match-title">主表单字段</div>
                  <el-select-v2 v-model="pdfWidgetItem.widgetId"
                    :options="computedNotTableFormWidgetList(pdfWidgetItem.widgetId)" size="small" :disabled="editState"
                    placeholder="请选择主表单字段" clearable :props="{
                      value: 'widgetId',
                      label: 'title'
                    }" />
                </div>
                <el-icon class="width30" v-if="widgetIndex !== 0" @click="handleDeleteMainFormFields(widgetIndex)">
                  <Close />
                </el-icon>
                <span class="width30" v-else></span>
              </div>
              <el-button plain size="small" :disabled="editState" @click="handleAddPdfWidget"><el-icon>
                  <Plus />
                </el-icon>添加</el-button>
            </div>
          </el-tab-pane>
          <el-tab-pane label="子表配置">
            <div class="import-card-row">
              <div class="import-card-title">选择主表单子表</div>
              <div class="import-card-select">
                <el-select-v2 v-model="pdfTableFormKeyAndWidget.tableFormWidgetId" :options="tableFormWidgetList"
                  size="small" :value-on-clear="() => null" :empty-values="[undefined, null]" :disabled="editState"
                  :props="{
                    value: 'widgetId',
                    label: 'title'
                  }" placeholder="请选择pdf模板" clearable @change="handlePdfTableFormWidget" />
              </div>
            </div>
            <div class="import-card-match-list" v-if="tableWidgetList.length > 0">
              <div class="import-card-match-li"
                v-for="(pdfWidgetItem, widgetIndex) in pdfTableFormKeyAndWidget.keyAndWidgetVoList"
                :key="pdfWidgetItem">
                <div class="import-card-match-left">
                  <div class="import-card-match-title">PDF - Product Details</div>
                  <el-select-v2 v-model="pdfWidgetItem.pdfKey" :options="tableFormPdfKeyList" size="small"
                    :disabled="editState" placeholder="请选择PDF - Product Details字段" clearable :props="{
                      value: 'key',
                      label: 'title'
                    }" />
                </div>
                <el-icon>
                  <Connection />
                </el-icon>
                <div class="import-card-match-right">
                  <div class="import-card-match-title">子表字段</div>
                  <el-select-v2 v-model="pdfWidgetItem.widgetId"
                    :options="computedTableFormWidgetList(pdfWidgetItem.widgetId)" size="small" placeholder="请选择子表字段"
                    clearable :disabled="editState" :props="{
                      value: 'widgetId',
                      label: 'title'
                    }" />
                </div>
                <el-icon class="width30" v-if="widgetIndex !== 0" @click="handleDeleteTableFormPdfWidget(widgetIndex)">
                  <Close />
                </el-icon>
                <span class="width30" v-else></span>
              </div>
              <el-button plain size="small" :disabled="editState" @click="handleAddTableFormPdfWidget"><el-icon>
                  <Plus />
                </el-icon>添加</el-button>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button :disabled="editState" type="primary" @click="handleConfirm"> 确定 </el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup lang="ts">
import { Connection, Close, Plus } from "@element-plus/icons-vue";
import { BizTemplateApi } from '@/api/template/bizTemplate';
import { ElMessage } from 'element-plus';

const dialogVisible = ref<boolean>(false);
const enablePdfImport = ref<boolean>(false);
const templateOptions = ref<any[]>([]);
const fileUploadWidgetList = ref<any[]>([]);
const specialPdfType = ref<string>("");
const pdfWidgetId = ref<any>("");
const pdfWidgetOptions = ref<any>([]);
const tableFormWidgetList = ref<any>([]);
const notTableFormWidgetList = ref<any>([]);
const bizTemplateId = ref<string>("");
const bizFormId = ref<string>("");
const tableFormPdfKeyList = ref<any>([]);
const editState = ref<boolean>(false);
const pdfTableFormKeyAndWidget = ref<any>({
  pdfParentKey: "productDetails",
  tableFormWidgetId: null,
  keyAndWidgetVoList: [{
    pdfKey: "",
    widgetId: ""
  }]
});
const pdfKeyAndWidgetList = ref<any>([{
  pdfKey: "",
  widgetId: ""
}])
const tableWidgetList = ref<any>([]);

const emit = defineEmits(["handleClose"])

const handlePdfTableFormWidget = async () => {
  if (pdfTableFormKeyAndWidget.value.tableFormWidgetId) {
    const data = await BizTemplateApi.getTableFormWidgetList(bizTemplateId.value, pdfTableFormKeyAndWidget.value.tableFormWidgetId);
    tableWidgetList.value = data;
  }
}

watch(() => pdfTableFormKeyAndWidget.value.tableFormWidgetId, async () => {
  if (pdfTableFormKeyAndWidget.value.tableFormWidgetId) {
    const data = await BizTemplateApi.getTableFormWidgetList(bizTemplateId.value, pdfTableFormKeyAndWidget.value.tableFormWidgetId);
    tableWidgetList.value = data;
  }
})

const handleConfirm = async () => {
  const data = await BizTemplateApi.updatePdfSetting({
    templateId: bizTemplateId.value,
    specialPdfType: specialPdfType.value,
    pdfWidgetId: pdfWidgetId.value,
    pdfKeyAndWidgetList: pdfKeyAndWidgetList.value.map(pdfData => {
      return {
        formId: bizFormId.value,
        pdfKey: pdfData.pdfKey,
        widgetId: pdfData.widgetId
      }
    }),
    pdfTableFormKeyAndWidget: pdfTableFormKeyAndWidget.value,
    enablePdfImport: enablePdfImport.value,
  });
  if (data) {
    ElMessage.success("创建成功！");
    dialogVisible.value = false;
    emit("handleClose");
  }
}

const handleDeleteMainFormFields = (index: number) => {
  if (editState.value) {
    return;
  }
  pdfKeyAndWidgetList.value.splice(index, 1);
}

const computedTableFormWidgetList = computed(() => {
  return (widgetId) => {
    const pdfKeyIdList = pdfTableFormKeyAndWidget.value.keyAndWidgetVoList.filter(widget => widget.widgetId !== widgetId).map(widget => widget.widgetId);
    return tableWidgetList.value.filter(child => !pdfKeyIdList.includes(child.widgetId));
  }
})

const computedNotTableFormWidgetList = computed(() => {
  return (widgetId: string) => {
    const pdfKeyIdList = pdfKeyAndWidgetList.value.filter(widget => widget.widgetId !== widgetId).map(widget => widget.widgetId);
    return notTableFormWidgetList.value.filter(child => !pdfKeyIdList.includes(child.widgetId));
  }
})

const handleAddPdfWidget = () => {
  pdfKeyAndWidgetList.value.push({
    pdfKey: "",
    widgetId: ""
  })
}

const handleAddTableFormPdfWidget = () => {
  pdfTableFormKeyAndWidget.value.keyAndWidgetVoList.push({
    pdfKey: "",
    widgetId: ""
  })
}

const handleDeleteTableFormPdfWidget = (index) => {
  if (editState.value) {
    return;
  }
  pdfTableFormKeyAndWidget.value.keyAndWidgetVoList.splice(index, 1);
}

const handleOpenDialog = (formId: string, templateId: string, pdfInfo: any, isEditState: boolean) => {
  Promise.all([BizTemplateApi.getSpecialPdfList(), BizTemplateApi.getPdfTemplateTree(), BizTemplateApi.getWidgetPdfSelectData(formId)]).then(data => {
    templateOptions.value = data[0];
    specialPdfType.value = data[0][0].value;
    fileUploadWidgetList.value = [{ title: "不导入", widgetId: "" }, ...data[2].fileUploadWidgetList];
    pdfWidgetOptions.value = data[1];
    notTableFormWidgetList.value = data[2].notTableFormWidgetList;
    tableFormWidgetList.value = data[2].tableFormWidgetList;
    bizTemplateId.value = templateId;
    bizFormId.value = formId;
    tableFormPdfKeyList.value = data[1].slice(-1)[0].children;
    editState.value = !isEditState;

    if (pdfInfo) {
      enablePdfImport.value = pdfInfo.enablePdfImport;
      pdfWidgetId.value = pdfInfo.pdfWidgetId;
      specialPdfType.value = pdfInfo.specialPdfType;
      pdfKeyAndWidgetList.value = pdfInfo.pdfKeyAndWidgetList;
      pdfTableFormKeyAndWidget.value = pdfInfo.pdfTableFormKeyAndWidget;
    }

    dialogVisible.value = true;
  })
}

const handleClose = (done) => {
  enablePdfImport.value = false;
  pdfWidgetId.value = "";
  specialPdfType.value = "";
  pdfKeyAndWidgetList.value = [{
    pdfKey: "",
    widgetId: ""
  }];
  pdfTableFormKeyAndWidget.value = {
    pdfParentKey: "productDetails",
    tableFormWidgetId: null,
    keyAndWidgetVoList: [{
      pdfKey: "",
      widgetId: ""
    }]
  };
  done()
}

defineExpose({
  handleOpenDialog
})
</script>
<style lang="scss" scoped>
.import-body {
  .import-row-header {
    display: flex;
    align-items: center;

    .import-row-title {
      flex: 1;
      font-weight: bolder;
    }
  }

  .import-container {
    margin: 20px 0;

    .import-container-header {
      display: flex;
      align-items: center;

      .import-container-title {
        flex: 1;
        font-weight: bolder;
        font-size: 12px;
      }

      .import-container-select {
        width: 300px;
      }
    }

    .card-border-tabs {
      margin: 10px 0;

      .import-card-header-title {
        font-size: 12px;
        color: #333;
        background: #d2d6db;
        padding: 5px 10px;
        border-radius: 4px;
        margin-bottom: 20px;

        .import-card-header-tr {
          line-height: 20px;
        }
      }

      .import-card-row {
        display: flex;
        align-items: center;
        margin: 6px 0;

        .import-card-title {
          flex: 1;
          font-weight: bolder;
          font-size: 12px;
        }

        .import-card-select {
          width: 280px;
        }
      }

      .import-card-match-list {
        margin-top: 30px;

        .import-card-match-li {
          display: flex;
          padding: 4px 0;

          .import-card-match-left {
            width: 200px;

            .import-card-match-title {
              font-weight: bolder;
              font-size: 12px;
              margin-bottom: 6px;
            }
          }

          .el-icon {
            flex: 1;
            font-size: 16px;
            margin-top: 26px;
          }

          .import-card-match-right {
            width: 200px;

            .import-card-match-title {
              font-weight: bolder;
              font-size: 12px;
              margin-bottom: 6px;
            }
          }

          .width30 {
            flex: none;
            width: 30px;
            cursor: pointer;
          }
        }

        .el-button {
          margin-top: 20px;
        }
      }
    }
  }
}
</style>
