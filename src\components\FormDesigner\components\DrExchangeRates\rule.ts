import uniqueId from '@form-create/utils/lib/unique'
import { currencyOptions } from '../../utils'

const name = 'DrExchangeRates'
const label = '汇率'

const Rule = {
  //插入菜单位置
  menu: 'extendAdvanced',
  //图标
  icon: 'icon-value',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      title: label,
      field: uniqueId(),
      $required: false,
      props: {
        exchangeType: 'fixed',
        targetCurrency: currencyOptions[0]
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'DrInputRadio',
        field: 'exchangeType',
        value: 'fixed',
        title: '换算节点',
        on: {
          handleExchangeRate(api, rateObject) {
            api.setData('rateObject', rateObject)
            api.activeRule.props['rateObject'] = rateObject
          }
        }
      },
      {
        type: 'select',
        field: 'targetCurrency',
        value: currencyOptions[0],
        title: '基准货币',
        options: currencyOptions.map((item) => ({ value: item, label: item }))
      }
    ]
  }
}

export default Rule
