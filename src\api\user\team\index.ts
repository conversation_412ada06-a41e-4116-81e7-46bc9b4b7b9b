import request from '@/config/axios'

// 贸易端团队 VO
export interface TeamVO {
  id: string // 主键
  teamName: string // 团队名称
  teamShortName: string // 团队简称
  teamAvatar: string // 团队头像
  teamCode: string // 团队码
  teamOwner: string // 团队拥有人;关联用户ID
  teamType: string // 团队类型;free=免费版，member=会员
  mobile: string // 联系方式
  address: string // 公司地址
  companyWeb: string // 公司网址
  companyName: string // 公司名称
}

// 贸易端团队 API
export const TeamApi = {
  // 查询贸易端团队分页
  getTeamPage: async (params: any) => {
    return await request.get({ url: `/system/trade-team/page`, params })
  },

  // 查询贸易端团队详情
  getTeam: async (id: string) => {
    return await request.get({ url: `/system/trade-team/get?id=` + id })
  },

  // 团队成员邀请记录
  getInviteMemberRecord: async (params: any) => {
    return await request.get({ url: `/system/trade-team/invite-member-record`, params })
  },

  // 团队协作邀请记录
  getInviteCoopRecord: async (params: any) => {
    return await request.get({ url: `/system/trade-team/invite-coop-record`, params })
  },
  deleteTeam: async (id: string) => {
    return await request.delete({ url: `/system/trade-team/delete?id=` + id })
  },

}
