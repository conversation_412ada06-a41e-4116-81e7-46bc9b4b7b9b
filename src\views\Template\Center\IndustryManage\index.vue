<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery">
    </Query>
  </ContentWrap>

  <ContentWrap>
    <Table ref="tableRef" :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @handleTable="handleTable" @SortChange="handleSortChange">
      <template #isShow>
        <el-table-column min-width="120" align="center" :label="t('admin_common_isShow') ">
          <template #default="{ row }">
            <el-switch :model-value="row.isShow" active-color="#409eff" :active-value="1" :inactive-value="0"
              :loading="showLoading" @change="handleChangeShow(row)" />
          </template>
        </el-table-column>
      </template>
    </Table>
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <IndustryManageForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { IndustryManageApi, IndustryManageVO } from '@/api/template/industryManage'
import IndustryManageForm from './components/IndustryManageForm.vue'
import businessConfig from './components/index.config'

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as IndustryManageVO[]
})

/** 运营端行业管理 列表 */
defineOptions({ name: 'IndustryManage' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  industryName = ''
  isShow = null
  createTime = []
  sortBy = ''
  descending: any = null
}
const queryParams = reactive(new DefaultQuery())
const showLoading = ref(false)
const tableRef = ref()

const handleQuery = (type: string) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      tableRef.value.clearSort()
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'add':
      openForm('create')
      break;
    default:
      break;
  }
}

const handleSortChange = (data: any) => {
  if (data.order == null) {
    queryParams.sortBy = ''
    queryParams.descending = null
  } else {
    queryParams.sortBy = data.prop
    queryParams.descending = data.order == 'descending'
  }
  getList()
}

const handleTable = (type: string, row: any) => {
  switch (type) {
    case 'edit':
      openForm('update', row.id)
      break;
    case 'delete':
      handleDelete(row.id)
      break;
    default:
      break;

  }
}
/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await IndustryManageApi.getIndustryManagePage(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await IndustryManageApi.deleteIndustryManage(id)
    message.success(t('admin_common_delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

const handleChangeShow = async (row) => {
  showLoading.value = true
  try {
    const data = { id: row.id }
    data['isShow'] = row.isShow == 1 ? 0 : 1
    await IndustryManageApi.updateIndustryManage(data)
    getList()
  } finally {
    showLoading.value = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})
</script>