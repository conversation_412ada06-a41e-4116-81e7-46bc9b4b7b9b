<template>
  <el-col :span="24">
    <div class="dr-card">
      <el-card :header="header" :shadow="shadow">
        <el-row v-bind="$attrs">
          <slot name="default"></slot>
        </el-row>
      </el-card>
    </div>
  </el-col>
</template>
<script setup lang="ts">
type PropsType = {
  header?: string
  shadow: 'always' | 'never'
}

withDefaults(defineProps<PropsType>(), {
  shadow: 'never'
})
</script>
<style scoped>
.dr-card {
  margin-bottom: 18px;

  :deep(.el-col) {
    min-height: 60px;
  }
}
</style>
