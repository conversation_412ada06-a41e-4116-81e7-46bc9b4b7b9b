<script lang="jsx">
import { defineComponent, ref, nextTick, watch } from 'vue'
import { ElInfiniteScroll, ElLoading } from 'element-plus'
import debounce from 'lodash-es'

export default defineComponent({
  name: 'LKRemoteSelect',
  props: {
    modelValue: { required: true, type: [String, Number, Boolean, Object, Array] },
    // tip: model一定要传，否则label显示不正确
    model: { required: true, type: Object, default: () => ({}) },
    method: {
      type: Function,
      default: () => Promise.resolve({})
    },
    multiple: { type: Boolean, default: false },
    valueKey: { type: String, default: 'value' },
    labelKey: { type: String, default: 'label' },
    //将选中的obj的一些值映射到model,{k:v},k为option的key,v为model的key
    valueMap: { type: Object, default: () => ({}) },
    queryKey: { type: String, default: '' }
  },
  emits: ['update:modelValue', 'select'],
  setup(props, { attrs, emit, expose }) {
    const options = ref([])
    const selectRef = ref(null)
    const selected = ref(null)
    let additionalData = []
    let is_form_component = false

    watch(
      () => props.modelValue,
      n => {
        if (is_form_component) return
        if (props.multiple && n) {
          selected.value = n.map(nn => nn[props.valueKey] ?? nn)
          additionalData = n
        } else {
          selected.value = n
          additionalData = n || n === 0 ? [props.model] : []
        }
      },
      { immediate: true }
    )

    const pagination = {
      page: 1,
      size: 20
    }

    let loadingInstance
    let total = Infinity
    const getOptionList = () => {
      if (loadingInstance) return
      if (options.value.length >= total) return
      if (pagination.page === 1) options.value = []

      //添加loading效果
      const selectDropdown = selectRef.value.$refs.scrollbar.$el.parentNode
      loadingInstance = ElLoading.service({
        target: selectDropdown
      })

      props
        .method({ ...pagination, [props.queryKey || props.labelKey || 'query']: query })
        .then(res => {
          let data = res?.data ?? []
          if (Object.prototype.toString.call(data[0]) !== '[object Object]')
            data = data.map(d => ({ label: d, value: d }))
          options.value.push(...data)
          pagination.page++
          total = res.total ?? data.length
        })
        .finally(() => {
          loadingInstance && loadingInstance.close()
          loadingInstance = null
        })
    }

    let dropDown
    const initScroll = async n => {
      if (!dropDown) {
        await nextTick()
        const dropNode = selectRef.value.$refs.scrollbar
        dropDown = dropNode.wrapRef
        dropDown.parentNode.style.display = 'block'
        // 设置距离底部多少px时触发load，防止页面缩放后scollTop与scrollHeight不匹配
        dropDown.setAttribute('infinite-scroll-distance', 2)
        ElInfiniteScroll.mounted(dropDown, {
          instance: dropNode,
          value: getOptionList
        })
      } else if (n && query) {
        query = ''
        resetPage()
        getOptionList()
      }
    }

    const resetPage = () => {
      total = Infinity
      pagination.page = 1
      options.value = []
    }

    expose({ resetPage, selectRef })

    let query = ''
    const filterMethod = debounce(key => {
      query = key
      resetPage()
      getOptionList()
    }, 200)

    const handleSelectChange = v => {
      is_form_component = true
      emit('update:modelValue', v)
      const { model, valueMap, valueKey, multiple } = props
      if (!multiple) {
        const selected = options.value.find(o => o[valueKey] === v)
        emit('select', selected)
        Object.entries(valueMap).forEach(([k, v]) => {
          if (Array.isArray(v)) v.forEach(vv => (model[vv] = selected?.[k]))
          else model[v] = selected?.[k]
        })
      } else {
        const selections = options.value.filter(o => v.includes(o[valueKey]))
        emit('select', selections)
      }
      nextTick(() => {
        is_form_component = false
      })
    }

    const renderOption = o => {
      const { labelKey, valueKey, valueMap } = props
      const l = o[labelKey] ?? o[valueMap[labelKey]]
      const v = o[valueKey] ?? o[valueMap[valueKey]]
      return <el-option label={l} value={v} key={v} />
    }

    return () => {
      const { valueKey, valueMap } = props
      const additionalData1 = []
      additionalData.forEach(element => {
        const v = element[valueKey] ?? element[valueMap[valueKey]]
        if (v != void 0 && options.value.findIndex(o => o[valueKey] == v) == -1) additionalData1.push(element)
      })
      return (
        <el-select
          ref={selectRef}
          v-model={selected.value}
          onVisible-change={initScroll}
          // remote为true时若无数据,则dropdown不会显示,用filterable方法代替remote,效果更好
          filterable
          filter-method={filterMethod}
          clearable
          multiple={props.multiple}
          onChange={handleSelectChange}
          {...attrs}
        >
          {additionalData1.length ? additionalData1.map(renderOption) : null}
          {options.value.length ? options.value.map(renderOption) : null}
        </el-select>
      )
    }
  }
})
</script>
