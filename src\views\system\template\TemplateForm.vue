<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="100px"
      v-loading="formLoading"
    >
      <el-form-item label="模版名称" prop="templateName">
        <el-input v-model="formData.templateName" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="图标" prop="icon">
        <el-input v-model="formData.icon" placeholder="请输入图标" />
      </el-form-item>
      <el-form-item label="图标颜色" prop="iconColor">
        <el-input v-model="formData.iconColor" placeholder="请输入图标颜色" />
      </el-form-item>
      <el-form-item label="行业" prop="industry">
        <el-select v-model="formData.industry" placeholder="请选择行业">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.INDUSTRY)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="模版类型" prop="type">
        <el-select v-model="formData.type" placeholder="请选择模版类型">
          <el-option
            v-for="dict in getStrDictOptions(DICT_TYPE.TEMPLATE_CREATE_TYPE)"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="描述" prop="description">
        <Editor v-model="formData.description" height="150px" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script setup lang="ts">
import { getStrDictOptions, getBoolDictOptions, DICT_TYPE } from '@/utils/dict'
import { TemplateApi, TemplateVO } from '@/api/system/template'

/** 运营端-模版 表单 */
defineOptions({ name: 'TemplateForm' })

const { t } = useI18n() // 国际化
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型：create - 新增；update - 修改
const formData = ref({
  id: undefined,
  templateName: undefined,
  icon: undefined,
  iconColor: undefined,
  industry: undefined,
  type: undefined,
  description: undefined,
  enable: undefined,
  editable: undefined,
  publishedDate: undefined,
  sccsCreate: undefined,
  fromId: undefined,
  baseInfoUpdateTime: undefined,
  enablePdfImport: undefined,
  specialPdfType: undefined,
  pdfWidgetId: undefined
})
const formRules = reactive({
})
const formRef = ref() // 表单Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = t('admin_common_' + type)
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await TemplateApi.getTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  await formRef.value.validate()
  // 提交请求
  formLoading.value = true
  try {
    const data = formData.value as unknown as TemplateVO
    if (formType.value === 'create') {
      await TemplateApi.createTemplate(data)
      message.success(t('admin_common_createSuccess'))
    } else {
      await TemplateApi.updateTemplate(data)
      message.success(t('admin_common_updateSuccess'))
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}

/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    templateName: undefined,
    icon: undefined,
    iconColor: undefined,
    industry: undefined,
    type: undefined,
    description: undefined,
    enable: undefined,
    editable: undefined,
    publishedDate: undefined,
    sccsCreate: undefined,
    fromId: undefined,
    baseInfoUpdateTime: undefined,
    enablePdfImport: undefined,
    specialPdfType: undefined,
    pdfWidgetId: undefined
  }
  formRef.value?.resetFields()
}
</script>