@mixin flex($direction: row, $align: stretch, $justify: flex-start) {
  display: flex;
  flex-direction: $direction;
  align-items: $align;
  justify-content: $justify;
}
.lk-content-page-header {
  margin-bottom: 10px;
  padding: 3px 10px;
  min-height: 50px;
  color: #a8acb7;
  // border-radius: 2px;
  background: #fff;
  .header-left {
    display: flex;
  }
  & {
    @include flex(row, center, space-between);
  }

  .el-button i,
  .el-button svg {
    margin-right: 12px;
    font-size: 12px;
  }
}
.lk-content-page {
  overflow: hidden;
  flex: 1;
  margin: 0 20px 20px 20px;

  @include flex(column);
  .content-table {
    overflow: hidden;
    flex: 1 1;
    padding: 0 10px;
    background-color: #fff;
    // .el-table .el-table__inner-wrapper::before {
    //   height: 0 !important;
    // }
  }
  &-horizontal {
    flex-direction: row;
    padding: 0;
    .content-page-siderbar {
      overflow: auto;
      padding: 10px;
      width: 200px;
      border-radius: 2px;
      background-color: #fff;
      box-shadow: 0 0 10px 0 rgba(84, 133, 255, 0.1);
    }
    .content-page-content {
      overflow: hidden;
      flex: 1;
      margin-left: 10px;
      @include flex(column);
    }
  }
  .lk-pagination {
    display: flex;
    justify-content: center;
    padding: 12px 0;
    // text-align: center;
    // color: var(--color-unimportant);
    // border-top: 1px solid #e5e9f3;
    background: #fff;
    // .el-pagination__total {
    //   color: var(--color-unimportant);
    // }
    // .el-pagination__sizes {
    //   .el-input__inner {
    //     color: var(--color-unimportant);
    //   }
    // }
    // .el-select .el-input {
    //   width: 100px;
    // }
    // button,
    // li {
    //   font-weight: 400;
    //   &:not(:disabled) {
    //     color: var(--text-color-normal);
    //     border: 1px solid var(--el-border-color-base);
    //     background-color: transparent;
    //   }
    //   &.active {
    //     color: #fff;
    //     background-color: var(--el-color-primary);
    //   }
    //   &:hover {
    //     color: var(--el-color-primary);
    //     border-color: var(--el-color-primary) !important;
    //   }
    // }
  }
}
