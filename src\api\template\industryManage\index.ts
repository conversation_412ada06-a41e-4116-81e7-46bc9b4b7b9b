import request from '@/config/axios'

// 运营端行业管理 VO
export interface IndustryManageVO {
  id: string // 主键
  industryName?: string // 行业名称
  industryImg?: string // 行业图标
  icon?: string // 行业图标
  iconColor?: string;// 行业图标颜色
  isShow?: number // 是否显示;0否1是
  sort?: number // 排序
}

// 运营端行业管理 API
export const IndustryManageApi = {
  // 查询运营端行业管理分页
  getIndustryManagePage: async (params: any) => {
    return await request.get({ url: `/system/industry-manage/page`, params })
  },

  // 查询运营端行业管理详情
  getIndustryManage: async (id: number) => {
    return await request.get({ url: `/system/industry-manage/get?id=` + id })
  },

  // 新增运营端行业管理
  createIndustryManage: async (data: IndustryManageVO) => {
    return await request.post({ url: `/system/industry-manage/create`, data })
  },     

  // 修改运营端行业管理
  updateIndustryManage: async (data: IndustryManageVO) => {
    return await request.put({ url: `/system/industry-manage/update`, data })
  },

  // 删除运营端行业管理
  deleteIndustryManage: async (id: number) => {
    return await request.delete({ url: `/system/industry-manage/delete?id=` + id })
  },

  // 导出运营端行业管理 Excel
  exportIndustryManage: async (params) => {
    return await request.download({ url: `/system/industry-manage/export-excel`, params })
  },
}
