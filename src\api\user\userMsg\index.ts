import request from '@/config/axios'
export interface TradeUserVO {
  id: string // 主键
  email?: string // 用户邮箱
  username?: string // 姓名
  sex?: number // 用户性别
  password?: string // 密码
  confirmPassword?: string // 确认密码
}
// 贸易端用户 API
export const UserApi = {
  // 查询贸易端用户分页
  getUserPage: async (params: any) => {
    return await request.get({ url: `/system/trade-user/page`, params })
  },

  // 查询贸易端用户详情
  getUser: async (id: string) => {
    return await request.get({ url: `/system/trade-user/get?id=` + id })
  },

  // 改变用户状态
  setStatus: async (data: any) => {
    return await request.post({ url: `/system/trade-user/update-status`, data })
  },
  // 新增贸易端用户
  addTradeUser: async (data: any) => {
    return await request.post({ url: `/system/trade-user/add-trade-user`, data })
  },
  // 获取贸易端用户
  getTradeUser: async (params: any) => {
    return await request.get({ url: `/system/trade-user/get-trade-user`, params })
  },
  // 修改贸易端用户
  updateTradeUser: async (data: any) => {
    return await request.post({ url: `/system/trade-user/update-trade-user`, data })
  },
}
