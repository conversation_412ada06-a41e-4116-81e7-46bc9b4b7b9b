//表格查询，分页，增加，删除，配置
import Query from '../../components/Query.vue'
import Table from '../../components/Table.vue'
import FormDialog from '../../components/FormDialog.vue'
import { reactive, nextTick } from 'vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import { cloneDeep } from 'lodash-es'
import useFormDialog from '../use-form-dialog'
import useTable from '../use-table'
import type { PageProps, RequestType } from './types'

//删除弹窗
const defaultDeleteProps = {
  message: '确定删除所选的数据吗？',
  title: '确认删除',
  confirmButtonText: '确定',
  cancelButtonText: '取消',
  customClass: 'delete-dialog',
  // cancelButtonClass: 'el-button--primary is-plain',
  // confirmButtonClass: 'el-button--danger is-plain',
  dangerouslyUseHTMLString: true,
  center: true
}

export default function useTablePage<D = any>({
  pageClass = [],
  API = {
    get: () => Promise.resolve({ data: [], total: 0 }),
    post: () => Promise.resolve(),
    put: () => Promise.resolve(),
    delete: () => Promise.resolve()
  },
  prefix,
  deleteProps = {},
  operateProps = {},
  tableColumns,
  queryColumns,
  formColumns,
  columns = [],
  commands = [
    { label: '编辑', key: 'put', type: 'primary' },
    { label: '删除', key: 'delete', type: 'danger' }
  ],
  rules = {},
  query = {},
  handleCommand
}: PageProps<D>) {
  const reactiveColumns = reactive(columns)

  const queryState = reactive({
    data: query,
    columns: queryColumns ?? reactiveColumns
  })

  const operateColumn = commands.length && (
    <el-table-column
      label='操作'
      align='center'
      width={commands.reduce((t, c) => (t += c.width ?? 85), 10)}
      v-slots={{
        default: ({ row, $index }: { row: D; $index: number }) =>
          commands.map(c =>
            c.render ? (
              c.render(row, $index)
            ) : (
              <el-button {...c} onClick={() => tableHandle(c.key, row, $index)}>
                {c.label}
              </el-button>
            )
          )
      }}
      {...operateProps}
    />
  )
  //表格
  const { selection, tableState, getTableData, pagination, serialColumn } = useTable<D>(
    p => API.get({ ...p, ...queryState.data }),
    {
      ref: 'tableRef',
      // align: 'center',
      columns: tableColumns ?? reactiveColumns,
      height: '100%'
    }
  )

  const formState = useFormDialog(
    data => {
      const api = API[formState.type as string] as RequestType
      return api
        ? api(data)?.then((msg?: string) => {
            getTableData()
            return msg
          })
        : Promise.reject()
    },
    { formProps: { columns: formColumns ?? columns, rules } }
  )

  //表格操作
  const tableHandle = (type: string, data?: D, index?: number) => {
    formState.type = type
    formState.data = cloneDeep(data ?? {})
    switch (type) {
      case 'delete':
        ElMessageBox.confirm('', { ...defaultDeleteProps, ...deleteProps })
          .then(() =>
            API.delete?.(data).then(() => {
              ElMessage.success('删除成功')
              pagination.total--
              getTableData()
            })
          )
          .catch(console.log)
        break
      case 'post':
      case 'put':
      case 'view':
      case 'copy':
        formState.visible = true
        nextTick(() => {
          formState.formProps.columns.forEach(c => {
            c.hide = c.hideTypes?.includes(type)
            c.disabled = c.disabledTypes?.includes(type)
          })
          formState.formProps.disabled = type === 'view'
          formState.title = {
            post: '新增',
            put: '编辑',
            view: '详情',
            copy: '复制'
          }[type]
        })
      // 此处不要加break，默认走一次handleCommand
      default:
        handleCommand?.(type, data, index)
        break
    }
  }

  //查询
  const handleQuery = () => {
    pagination.currentPage = 1
    getTableData()
  }

  //渲染查询区、头部、表格、分页、表格操作
  const renderPart = (part: string, slot?: JSX.Element) => {
    switch (part) {
      case 'query':
        return <Query {...queryState} onClick={handleQuery} />
      case 'header':
        return (
          <div class='lk-content-page-header'>
            <div class='header-left'>
              {slot ?? (
                <el-button type='primary' onClick={() => tableHandle('post')}>
                  {/* <svg-icon icon="plus" /> */}
                  新增
                </el-button>
              )}
            </div>
            {renderPart('query')}
          </div>
        )
      case 'table':
        return (
          <Table {...tableState} v-slots={{ prefix: () => [prefix?.(), serialColumn], ...slot }}>
            {operateColumn}
          </Table>
        )
      case 'pagination':
        return <el-pagination class={['lk-pagination']} {...pagination} />
      case 'FormDialog':
        return <FormDialog {...formState} v-slots={slot} />
    }
  }

  const render = (slots?: { header?: JSX.Element; table?: JSX.Element; form?: JSX.Element }) => (
    <div class={['lk-content-page', ...pageClass]}>
      {renderPart('header', slots?.header)}
      <div class='content-table'>{renderPart('table', slots?.table)}</div>
      {renderPart('pagination')}
      {renderPart('FormDialog', slots?.form)}
    </div>
  )

  return {
    queryState,
    handleQuery,
    selection,
    tableState,
    getTableData,
    tableHandle,
    pagination,
    formState,
    renderPart,
    render
  }
}
