import { DICT_TYPE, getIntDictOptions, getBoolDictOptions, getStrDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'
// import { useI18n } from 'vue-i18n'

const { t } = useI18n() // 国际化


const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: t('admin_template_id'),
      prop: "id",
    },
    {
      label: t('admin_template_name'),
      prop: "bizTemplateName",
    },
    {
      label: t('admin_template_icon'),
      prop: "icon",
      clSlots: {
        default: ({row}) => {
          return <i style={{ color: row.iconColor, display: 'inline-block', 'font-size': '26px', margin: '2px 0px ' }} class={['iconfont', row.icon]}></i>
        }
      }
    },
    {
      label: t('admin_template_typeCode'),
      prop: "typeCode",
      filterOptions: getStrDictOptions(DICT_TYPE.SYSTEM_TEMPLATE_CREATE_TYPE)
    },
    {
      label: t('admin_template_industry'),
      prop: "industryName",
    },
    
    {
      label: t('admin_template_content'),
      prop: "content",
      clSlots: {
        default: ({row}) => {
          return <div>
            <p class={'my-2px'}>{t('admin_template_milestone')}: {row.milestoneNumber || 0}</p>
            <p class={'my-2px'}>{t('admin_template_form')}: {row.formNumber || 0}</p>
          </div>
        }
      }
    },
    {
      label: t('admin_template_creator'),
      prop: "creatorName",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_template_updater'),
      prop: "updaterName",
    },
    {
      label: t('admin_common_updateTime'),
      prop: "updateTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_template_status'),
      slotName: 'status'
    },
    {
      label: t('admin_common_description'),
      prop: "description",
      'min-width': 160
    },
    {
      label: t('admin_template_usedCount'),
      prop: "usedCount",
    },
    {
      label: t('admin_common_remark'),
      prop: "remark",
      'min-width': 260
    },
  ],
  tableBtnList: [
    { label: t('admin_common_edit'), key: "edit", type: "primary", link: true },
    { label: t('admin_template_formDesign'), key: "design", type: "primary", link: true },
    { label: t('admin_common_copy'), key: "copy", type: "primary", link: true },
    { label: t('admin_common_delect'), key: "delete", type: "primary", link: true },
  ],
  searchConfig: [
    //头部筛选数据
    { 
      label: t('admin_template_name'),
      prop: 'bizTemplateName'
    },
    { 
      label: t('admin_template_industry'),
      prop: 'industryId',
      type: 'select',
      loading: false,
      options: [],
      'popper-append-to-body':false,
      'popper-class': 'cmSelect'

    },
    {
      label: t('admin_template_typeCode'),
      prop: 'typeCode',
      type: 'select',
      loading: false,
      options: getStrDictOptions(DICT_TYPE.SYSTEM_TEMPLATE_CREATE_TYPE),
    },
    {
      label: t('admin_template_status'),
      prop: 'status',
      type: 'select',
      options: getIntDictOptions(DICT_TYPE.COMMON_ENABLED)
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator':"-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time':[new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    },
    {
      label: t('admin_common_updateTime'),
      prop: 'updateTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator':"-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time':[new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    }
  ],
  searchBtnList: [
    {
      label: t('admin_common_add'),
      key: 'add',
      icon: 'ep:plus',
      elProps: {
        type: 'primary',
      }
    }
  ]
}

export default fixedParameter