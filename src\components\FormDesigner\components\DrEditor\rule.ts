import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrEditor'
const label = '富文本'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-editor',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {
        placeholder: '请输入'
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("placeholderEn", langValue);
            api.activeRule.props["placeholderEn"] = langValue;
          },
        },
      }
    ]
  }
}

export default Rule
