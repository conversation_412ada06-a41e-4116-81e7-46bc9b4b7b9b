<template>
  <div class="dr-location">
    <el-input :model-value="address" :placeholder="placeholder" :readonly="!allowInput">
      <template #append>
        <el-button :icon="Location" />
      </template>
    </el-input>
  </div>
</template>
<script setup lang="ts">
import { Location } from '@element-plus/icons-vue'

type PropsType = {
  address?: string
  placeholder?: string
  allowInput?: boolean
}

withDefaults(defineProps<PropsType>(), {
  address: ''
})
</script>

<style scoped lang="scss"></style>
