/*!
 * FormCreate商业版可视化表单设计器-让表单设计更简单
 * fc-designer-pro v5.6.0
 * (c) 2021-2025 FormCreate Team
 * Github https://github.com/xaboy/form-create-designer
 * license 仅限于被授权主体（个人、企业或组织）使用
 */
const e = {
  name: "zh-cn",
  form: {
    field: "\u5B57\u6BB5 ID",
    title: "\u5B57\u6BB5\u540D\u79F0",
    info: "\u63D0\u793A\u4FE1\u606F",
    ignore: "\u5FFD\u7565\u5B57\u6BB5",
    native: "\u662F\u5426\u663E\u793A\u6807\u9898",
    control: "\u8054\u52A8\u6570\u636E",
    labelShow: "\u662F\u5426\u663E\u793A",
    labelPosition: "\u6807\u7B7E\u7684\u4F4D\u7F6E",
    labelStyle: "\u6807\u7B7E\u7684\u6837\u5F0F",
    labelSuffix: "\u6807\u7B7E\u7684\u540E\u7F00",
    formItemSpan: "\u7EC4\u4EF6\u5BBD\u5EA6",
    row: "\u6574\u884C",
    formItem: "\u914D\u7F6E\u8868\u5355\u9879",
    globalEvent: "\u5168\u5C40\u4E8B\u4EF6",
    globalFetch: "\u5168\u5C40\u6570\u636E\u6E90",
    globalClass: "\u5168\u5C40\u6837\u5F0F",
    globalConfig: "\u5168\u5C40\u914D\u7F6E",
    size: "\u8868\u5355\u7684\u5C3A\u5BF8",
    event: "\u8868\u5355\u4E8B\u4EF6",
    labelWidth: "\u6807\u7B7E\u7684\u5BBD\u5EA6",
    hideRequiredAsterisk: "\u9690\u85CF\u5FC5\u586B\u5B57\u6BB5\u7684\u6807\u7B7E\u65C1\u8FB9\u7684\u7EA2\u8272\u661F\u53F7",
    formItemMarginBottom: "\u8868\u5355\u9879\u7684\u4E0B\u8FB9\u8DDD",
    showMessage: "\u663E\u793A\u6821\u9A8C\u9519\u8BEF\u4FE1\u606F",
    inlineMessage: "\u4EE5\u884C\u5185\u5F62\u5F0F\u5C55\u793A\u6821\u9A8C\u4FE1\u606F",
    submitBtn: "\u662F\u5426\u663E\u793A\u8868\u5355\u63D0\u4EA4\u6309\u94AE",
    resetBtn: "\u662F\u5426\u663E\u793A\u8868\u5355\u91CD\u7F6E\u6309\u94AE",
    appendChild: "\u6DFB\u52A0\u5B50\u7EA7",
    formMode: "\u8868\u5355\u6A21\u5F0F",
    formName: "\u8868\u5355\u540D\u79F0",
    ignoreHiddenFields: "\u63D0\u4EA4\u8868\u5355\u65F6\u5FFD\u7565\u88AB\u9690\u85CF\u7684\u5B57\u6BB5",
    previewMode: "\u9605\u8BFB\u6A21\u5F0F",
    componentMode: "\u751F\u6210\u7EC4\u4EF6",
    sfcMode: "\u751F\u6210SFC",
    document: "\u5E2E\u52A9\u6587\u6863",
    controlDocument: "\u9700\u8981\u66F4\u8BE6\u7EC6\u7684\u914D\u7F6E\u65B9\u6CD5\uFF1F\u8BF7\u67E5\u770B{doc}",
    onSubmit: "\u8868\u5355\u63D0\u4EA4\u65F6\u89E6\u53D1",
    onReset: "\u8868\u5355\u91CD\u7F6E\u540E\u89E6\u53D1",
    onCreated: "\u8868\u5355\u7EC4\u4EF6\u521D\u59CB\u5316\u5B8C\u6BD5\u540E\u89E6\u53D1",
    onMounted: "\u8868\u5355\u7EC4\u4EF6\u6E32\u67D3\u5B8C\u6BD5\u540E\u89E6\u53D1",
    onReload: "\u8868\u5355\u6E32\u67D3\u89C4\u5219\u91CD\u8F7D\u540E\u89E6\u53D1",
    onChange: "\u8868\u5355\u7EC4\u4EF6\u7684\u503C\u53D1\u751F\u53D8\u5316\u65F6\u89E6\u53D1",
    beforeFetch: "\u8FDC\u7A0B\u6570\u636E\u8BF7\u6C42\u53D1\u9001\u524D\u89E6\u53D1"
  },
  ai: {
    name: "\u667A\u80FD\u8868\u5355\u52A9\u7406",
    info: "\u60A8\u597D\uFF0C\u6211\u662F\u60A8\u7684\u667A\u80FD\u8868\u5355\u52A9\u624B\uFF01\u6211\u53EF\u4EE5\u5E2E\u52A9\u60A8\u5FEB\u901F\u751F\u6210\u548C\u4FEE\u6539\u8868\u5355\uFF0C\u5E2E\u52A9\u60A8\u8F7B\u677E\u9AD8\u6548\u5730\u5B8C\u6210\u8868\u5355\u8BBE\u8BA1\u3002",
    try: "\u4F60\u53EF\u4EE5\u8BD5\u7740\u95EE\u6211",
    change: "\u6362\u4E00\u6362",
    loading: "\u6B63\u5728\u6839\u636E\u60A8\u7684\u9700\u6C42\u5236\u4F5C\u8868\u5355\uFF0C\u8BF7\u7A0D\u5019...",
    fail: "\u5236\u4F5C\u8868\u5355\u65F6\u9047\u5230\u95EE\u9898\uFF0C\u8BF7\u5C1D\u8BD5\u8C03\u6574\u5185\u5BB9\u3002",
    success: "\u5DF2\u5904\u7406\u5B8C\u6BD5\uFF01",
    placeholder: "\u8BF7\u63CF\u8FF0\u60A8\u7684\u9700\u6C42"
  },
  warning: {
    name: "\u7EC4\u4EF6\u7684\u552F\u4E00\u6807\u8BC6\uFF0C\u7528\u4E8E\u83B7\u53D6\u548C\u4FEE\u6539\u8BE5\u7EC4\u4EF6\u7684\u914D\u7F6E\u89C4\u5219\u3002\u901A\u8FC7\u8BE5\u6807\u8BC6\u53EF\u4EE5\u7CBE\u786E\u5B9A\u4F4D\u7EC4\u4EF6\uFF0C\u5B9E\u73B0\u5BF9\u7EC4\u4EF6\u5C5E\u6027\u548C\u884C\u4E3A\u7684\u63A7\u5236\u3002",
    field: "\u7EC4\u4EF6\u5BF9\u5E94\u7684\u5B57\u6BB5\u540D\u7528\u4E8E\u4E0E\u7EC4\u4EF6\u7684\u6570\u636E\u8FDB\u884C\u7ED1\u5B9A\u3002\u5B57\u6BB5\u540D\u9700\u4EE5\u5B57\u6BCD\u5F00\u5934\uFF0C\u4EE5\u786E\u4FDD\u80FD\u591F\u6B63\u786E\u8BC6\u522B\u3002",
    formList: "\u7528\u4E8E\u7BA1\u7406\u548C\u5207\u6362\u8868\u5355\u6A21\u677F\uFF0C\u65B9\u4FBF\u5728\u4E0D\u540C\u4E1A\u52A1\u573A\u666F\u4E2D\u5FEB\u901F\u5207\u6362\u548C\u914D\u7F6E\u8868\u5355\u3002",
    fetch: "\u8FDC\u7A0B\u6570\u636E\u901A\u8FC7\u8FDC\u7A0B\u8BF7\u6C42\u52A0\u8F7D\u7EC4\u4EF6\u7684\u914D\u7F6E\u9879\u3002\u914D\u7F6E\u8BF7\u6C42\u53C2\u6570\u540E\uFF0C\u7EC4\u4EF6\u4F1A\u81EA\u52A8\u53D1\u8D77\u8BF7\u6C42\uFF0C\u83B7\u53D6\u8FDC\u7A0B\u6570\u636E\u5E76\u6839\u636E\u8FD4\u56DE\u7684\u7ED3\u679C\u66F4\u65B0\u7EC4\u4EF6\u3002",
    fetchQuery: "\u5B9A\u4E49\u8BF7\u6C42\u7684 GET \u53C2\u6570\uFF0C\u901A\u8FC7 URL \u4F20\u9012\u6570\u636E\u3002",
    fetchData: "\u5B9A\u4E49\u8BF7\u6C42\u7684 POST \u53C2\u6570\uFF0C\u901A\u8FC7\u8BF7\u6C42\u4F53\u4F20\u9012\u6570\u636E\u3002",
    fetchDataType: "\u9009\u62E9\u8BF7\u6C42\u4F53\u7684\u6570\u636E\u7C7B\u578B\uFF0C\u786E\u4FDD\u6570\u636E\u683C\u5F0F\u6B63\u786E\u3002",
    fetchParse: "\u8BF7\u6C42\u8FD4\u56DE\u540E\uFF0C\u53EF\u4EE5\u901A\u8FC7\u5904\u7406\u51FD\u6570\u5BF9\u8FD4\u56DE\u7684\u7ED3\u679C\u8FDB\u884C\u5904\u7406\uFF0C\u5C06\u7ED3\u679C\u8F6C\u6362\u4E3A\u7EC4\u4EF6\u6240\u9700\u7684\u6570\u636E\u548C\u7ED3\u6784\u3002",
    language: "\u7BA1\u7406\u9875\u9762\u7684\u591A\u8BED\u8A00\u6570\u636E\uFF0C\u5728\u7EC4\u4EF6\u4E2D\u914D\u7F6E\u4E0D\u540C\u8BED\u8A00\u7684\u6587\u672C\uFF0C\u652F\u6301\u4E00\u952E\u5207\u6362\u8BED\u8A00\u4F53\u7CFB\uFF0C\u4FBF\u4E8E\u5728\u591A\u8BED\u8A00\u73AF\u5883\u4E0B\u4F7F\u7528\u548C\u5C55\u793A\u5185\u5BB9\u3002",
    variable: "\u4ECE\u53D8\u91CF\u8BFB\u53D6\u914D\u7F6E\u9879\u7684\u503C\uFF0C\u652F\u6301\u901A\u8FC7\u53D8\u91CF\uFF08\u5982 Cookie\u3001localStorage\u3001\u5168\u5C40\u53D8\u91CF\u548C\u5916\u90E8\u6570\u636E\u6E90\u7B49\uFF09\u8BBE\u7F6E\u914D\u7F6E\u9879\u3002\u5F53\u5173\u8054\u7684\u53D8\u91CF\u53D8\u5316\u65F6\uFF0C\u914D\u7F6E\u9879\u4F1A\u540C\u6B65\u66F4\u65B0\u3002",
    variableInfo: "\u8BF7\u5728\u53D8\u91CF\u5217\u8868\u4E2D\u9009\u62E9\u53D8\u91CF\u3002\u5982\u679C\u53D8\u91CF\u7684\u503C\u662F\u5BF9\u8C61\uFF0C\u60A8\u53EF\u4EE5\u901A\u8FC7 {{variableName.attributeName}} \u683C\u5F0F\u8BBF\u95EE\u5BF9\u8C61\u4E2D\u7684\u5C5E\u6027\u503C\u3002",
    pageManage: "\u8BBE\u8BA1\u8868\u5355\u4E2D\u7684\u5B50\u5F39\u7A97\uFF0C\u901A\u8FC7\u4E8B\u4EF6\u89E6\u53D1\u5F39\u7A97\u663E\u793A\uFF0C\u4FBF\u4E8E\u5728\u8868\u5355\u4E2D\u52A8\u6001\u5C55\u793A\u4FE1\u606F\u6216\u8FDB\u884C\u4EA4\u4E92\u64CD\u4F5C\u3002",
    globalConfig: "\u7BA1\u7406\u5168\u5C40\u4E8B\u4EF6\u3001\u6837\u5F0F\u3001\u6570\u636E\u548C\u53D8\u91CF\uFF0C\u7528\u4E8E\u5728\u4E0D\u540C\u7EC4\u4EF6\u95F4\u8FDB\u884C\u6570\u636E\u5171\u4EAB\u548C\u4E8B\u4EF6\u5904\u7406\u3002",
    globalClass: "\u63D0\u4F9B\u5B9A\u4E49\u5168\u5C40\u6837\u5F0F\u548C Class \u7684\u529F\u80FD\uFF0C\u7528\u6237\u53EF\u4EE5\u5728\u7EC4\u4EF6\u914D\u7F6E\u4E2D\u9009\u62E9\u5E76\u5E94\u7528\u8FD9\u4E9B\u9884\u5B9A\u4E49\u7684 Class\uFF0C\u4EE5\u5B9E\u73B0\u7EDF\u4E00\u7684\u6837\u5F0F\u7BA1\u7406\u3002",
    globalEvent: "\u5168\u5C40\u4E8B\u4EF6\u7528\u4E8E\u5B9A\u4E49\u7EC4\u4EF6\u4E8B\u4EF6\uFF0C\u4F9B\u7EC4\u4EF6\u8FDB\u884C\u914D\u7F6E\u548C\u9009\u62E9\uFF0C\u65B9\u4FBF\u5728\u4E0D\u540C\u7EC4\u4EF6\u4E2D\u7EDF\u4E00\u4F7F\u7528\u9884\u8BBE\u4E8B\u4EF6\uFF0C\u7B80\u5316\u4E8B\u4EF6\u7684\u7BA1\u7406\u548C\u5206\u53D1\u3002",
    globalFetch: "\u5168\u5C40\u6570\u636E\u6E90\u7528\u4E8E\u5B9A\u4E49\u5E38\u7528\u7684\u6570\u636E\u6E90\uFF0C\u652F\u6301\u9759\u6001\u6570\u636E\u548C\u8FDC\u7A0B\u6570\u636E\u3002\u8BE5\u6570\u636E\u6E90\u53EF\u5728\u5404\u79CD\u7EC4\u4EF6\u914D\u7F6E\u4E2D\u4F7F\u7528\uFF0C\u65B9\u4FBF\u7EDF\u4E00\u7BA1\u7406\u548C\u8C03\u7528\u6570\u636E\u3002",
    globalVariable: "\u5168\u5C40\u53D8\u91CF\u4E2D\u53EF\u4EE5\u5B9A\u4E49\u8BA1\u7B97\u5C5E\u6027\uFF0C\u901A\u8FC7\u8868\u5355\u6570\u636E\u548C\u5916\u90E8\u6570\u636E\u8FD4\u56DE\u7EC4\u4EF6\u6240\u9700\u7684\u6570\u636E\u3002\u5173\u8054\u6570\u636E\u53D8\u5316\u65F6\uFF0C\u8BA1\u7B97\u5C5E\u6027\u4F1A\u81EA\u52A8\u66F4\u65B0\uFF0C\u53EF\u4EE5\u5728\u7ED1\u5B9A\u53D8\u91CF\u6A21\u5757\u548C\u4E8B\u4EF6\u4E2D\u4F7F\u7528\u3002",
    ignore: "\u5F00\u542F\u540E\uFF0C\u8868\u5355\u63D0\u4EA4\u65F6\u4F1A\u81EA\u52A8\u6392\u9664\u8BE5\u7EC4\u4EF6\u7684\u5B57\u6BB5\uFF0C\u4E0D\u4F1A\u5C06\u5176\u5305\u542B\u5728\u63D0\u4EA4\u7684\u6570\u636E\u4E2D\u3002",
    ignoreHiddenFields: "\u5F00\u542F\u540E\uFF0C\u8868\u5355\u63D0\u4EA4\u65F6\u4F1A\u81EA\u52A8\u8FC7\u6EE4\u6389\u88AB\u9690\u85CF\u7684\u7EC4\u4EF6\u5B57\u6BB5\uFF0C\u786E\u4FDD\u4EC5\u63D0\u4EA4\u53EF\u89C1\u7684\u5B57\u6BB5\u6570\u636E\u3002",
    behaviorIgnoreError: "\u5982\u679C\u52A8\u4F5C\u6267\u884C\u8FC7\u7A0B\u4E2D\u53D1\u751F\u9519\u8BEF\uFF0C\u662F\u5426\u7EE7\u7EED\u6267\u884C\u540E\u7EED\u7684\u52A8\u4F5C\u3002",
    behaviorExpression: "\u5F53\u6761\u4EF6\u8FBE\u6210\u65F6\uFF0C\u52A8\u4F5C\u624D\u4F1A\u88AB\u6267\u884C\u3002",
    behaviorStopPropagation: "\u5F53\u6761\u4EF6\u8FBE\u6210\u65F6\uFF0C\u4F1A\u963B\u6B62\u540E\u7EED\u52A8\u4F5C\u7684\u6267\u884C\u3002",
    computedCondition: "\u901A\u8FC7\u5B9A\u4E49\u6761\u4EF6\uFF0C\u7EC4\u4EF6\u7684\u72B6\u6001\u548C\u6570\u636E\u503C\u4F1A\u6839\u636E\u6761\u4EF6\u662F\u5426\u6210\u7ACB\u81EA\u52A8\u8C03\u6574\u3002",
    computedFormula: "\u901A\u8FC7\u516C\u5F0F\u548C\u8868\u5355\u6570\u636E\uFF0C\u52A8\u6001\u8BA1\u7B97\u7EC4\u4EF6\u7684\u72B6\u6001\u548C\u6570\u636E\u503C\u3002"
  },
  computed: {
    value: {
      title: "\u6570\u636E\u8054\u52A8",
      btn: "\u8BBE\u7F6E\u6570\u636E\u8054\u52A8",
      name: "\u8BBE\u7F6E\u7EC4\u4EF6\u7684\u503C"
    },
    hidden: {
      title: "\u9690\u85CF\u6761\u4EF6",
      btn: "\u8BBE\u7F6E\u9690\u85CF\u6761\u4EF6",
      name: "\u8BBE\u7F6E\u7EC4\u4EF6\u7684\u9690\u85CF\u6761\u4EF6"
    },
    required: {
      title: "\u5FC5\u586B\u6761\u4EF6",
      btn: "\u8BBE\u7F6E\u5FC5\u586B\u6761\u4EF6",
      name: "\u8BBE\u7F6E\u7EC4\u4EF6\u7684\u5FC5\u586B\u6761\u4EF6"
    },
    disabled: {
      title: "\u7981\u7528\u6761\u4EF6",
      btn: "\u8BBE\u7F6E\u7981\u7528\u6761\u4EF6",
      name: "\u8BBE\u7F6E\u7EC4\u4EF6\u7684\u7981\u7528\u6761\u4EF6"
    },
    formulas: {
      "==": "\u7B49\u4E8E",
      "!=": "\u4E0D\u7B49\u4E8E",
      on: "\u5305\u542B",
      notOn: "\u4E0D\u5305\u542B",
      empty: "\u4E3A\u7A7A",
      notEmpty: "\u4E0D\u4E3A\u7A7A",
      pattern: "\u6B63\u5219\u8868\u8FBE\u5F0F",
      ">": "\u5927\u4E8E",
      ">=": "\u5927\u4E8E\u7B49\u4E8E",
      "<": "\u5C0F\u4E8E",
      "<=": "\u5C0F\u4E8E\u7B49\u4E8E"
    },
    variable: {
      attr: "\u5C5E\u6027\u5217\u8868",
      bind: "\u7ED1\u5B9A\u53D8\u91CF",
      list: "\u53D8\u91CF\u5217\u8868",
      btn: "\u8BBE\u7F6E\u53D8\u91CF",
      title: "\u5168\u5C40\u53D8\u91CF",
      create: "\u521B\u5EFA\u53D8\u91CF",
      placeholder: "\u8BF7\u8F93\u5165\u53D8\u91CF\u7684\u63CF\u8FF0\u4FE1\u606F"
    },
    linkage: {
      trigger: "\u89E6\u53D1\u4EE5\u4E0B\u8054\u52A8",
      info: [
        "\u5F53\u524D\u7EC4\u4EF6\u8054\u52A8\u663E\u793A",
        "\u7684\u503C"
      ]
    },
    name: "\u8BA1\u7B97\u516C\u5F0F",
    setting: "\u8BBE\u7F6E\u6761\u4EF6",
    invert: "\u6761\u4EF6\u6210\u7ACB\u540E\u7EC4\u4EF6\u72B6\u6001",
    condition: "\u903B\u8F91\u6761\u4EF6",
    addCondition: "\u6DFB\u52A0\u6761\u4EF6",
    addGroup: "\u6DFB\u52A0\u6761\u4EF6\u7EC4",
    form: "\u5F53\u524D\u8868\u5355",
    subform: "\u5B50\u8868\u5355",
    formula: "\u51FD\u6570\u516C\u5F0F",
    formulaInfo: "\u51FD\u6570\u8BF4\u660E",
    formulaExample: "\u51FD\u6570\u793A\u4F8B",
    fieldUsed: "\u3010{label}\u3011\u5728\u8BA1\u7B97\u516C\u5F0F\u4E2D\u88AB\u4F7F\u7528\uFF0C\u8BF7\u5148\u4FEE\u6539\u5BF9\u5E94\u516C\u5F0F",
    fieldExist: "\u3010{label}\u3011\u5B57\u6BB5\u5DF2\u5B58\u5728",
    fieldEmpty: "\u5B57\u6BB5\u540D\u79F0\u4E0D\u80FD\u4E3A\u7A7A",
    fieldChar: "\u5B57\u6BB5\u540D\u79F0\u5FC5\u987B\u4EE5\u5B57\u6BCD\u5F00\u5934"
  },
  validate: {
    type: "\u5B57\u6BB5\u7C7B\u578B",
    typePlaceholder: "\u8BF7\u9009\u62E9",
    trigger: "\u89E6\u53D1\u65B9\u5F0F",
    mode: "\u9A8C\u8BC1\u65B9\u5F0F",
    modes: {
      required: "\u5FC5\u586B",
      notRequired: "\u975E\u5FC5\u586B",
      pattern: "\u6B63\u5219\u8868\u8FBE\u5F0F",
      validator: "\u81EA\u5B9A\u4E49\u9A8C\u8BC1",
      min: "\u6700\u5C0F\u503C",
      max: "\u6700\u5927\u503C",
      len: "\u957F\u5EA6"
    },
    types: {
      string: "\u6587\u672C",
      boolean: "\u5E03\u5C14",
      array: "\u591A\u9009",
      number: "\u6570\u5B57",
      integer: "\u6574\u6570",
      float: "\u5C0F\u6570",
      object: "\u5408\u96C6",
      date: "\u65E5\u671F",
      url: "URL\u94FE\u63A5",
      email: "\u90AE\u7BB1\u5730\u5740"
    },
    message: "\u9519\u8BEF\u4FE1\u606F",
    auto: "\u81EA\u52A8\u83B7\u53D6",
    autoRequired: "\u8BF7\u8F93\u5165{title}",
    autoMode: "\u8BF7\u8F93\u5165\u6B63\u786E\u7684{title}",
    requiredPlaceholder: "\u8BF7\u8F93\u5165\u63D0\u793A\u8BED",
    required: "\u662F\u5426\u5FC5\u586B",
    rule: "\u9A8C\u8BC1\u89C4\u5219"
  },
  tableOptions: {
    handle: "\u64CD\u4F5C",
    add: "\u6DFB\u52A0",
    empty1: "\u70B9\u51FB\u53F3\u4E0B\u89D2",
    empty2: "\u6309\u94AE\u6DFB\u52A0\u4E00\u5217",
    rmCol: "\u5220\u9664\u5F53\u524D\u5217",
    rmRow: "\u5220\u9664\u5F53\u524D\u884C",
    splitRow: "\u62C6\u5206\u6210\u884C",
    splitCol: "\u62C6\u5206\u6210\u5217",
    mergeBottom: "\u5411\u4E0B\u5408\u5E76",
    mergeRight: "\u5411\u53F3\u5408\u5E76",
    addTop: "\u6DFB\u52A0\u4E0A\u5217",
    addBottom: "\u6DFB\u52A0\u4E0B\u5217",
    addLeft: "\u6DFB\u52A0\u5DE6\u5217",
    addRight: "\u6DFB\u52A0\u53F3\u5217",
    keyValue: "\u952E\u503C\u5BF9"
  },
  struct: {
    title: "\u7F16\u8F91\u6570\u636E",
    only: "\u3010{label}\u3011\u53EA\u5141\u8BB8\u6DFB\u52A0\u4E00\u4E2A",
    errorMsg: "\u8F93\u5165\u7684\u5185\u5BB9\u8BED\u6CD5\u9519\u8BEF",
    configured: "\u5DF2\u914D\u7F6E",
    configuredData: "\u5DF2\u914D\u7F6E {num} \u4E2A\u6570\u636E\u6E90",
    configuredEvent: "\u5DF2\u914D\u7F6E {num} \u4E2A\u4E8B\u4EF6"
  },
  class: {
    title: "\u8BBE\u7F6E\u6837\u5F0F",
    create: "\u521B\u5EFA\u6837\u5F0F",
    placeholder: "\u8BF7\u8F93\u5165\u6837\u5F0F\u7684\u63CF\u8FF0\u4FE1\u606F",
    saveMsg: "\u8BF7\u5148\u4FDD\u5B58\u5F53\u524D\u6B63\u5728\u7F16\u8F91\u7684\u6837\u5F0F",
    configured: "\u5DF2\u914D\u7F6E {num} \u4E2A\u6837\u5F0F"
  },
  event: {
    title: "\u8BBE\u7F6E\u4E8B\u4EF6",
    create: "\u521B\u5EFA\u4E8B\u4EF6",
    list: "\u4E8B\u4EF6\u5217\u8868",
    placeholder: "\u8BF7\u8F93\u5165\u4E8B\u4EF6\u7684\u540D\u79F0",
    saveMsg: "\u8BF7\u5148\u4FDD\u5B58\u5F53\u524D\u6B63\u5728\u7F16\u8F91\u7684\u4E8B\u4EF6",
    type: "\u7C7B\u578B",
    info: "\u8BF4\u660E",
    action: "\u7F16\u8F91\u884C\u4E3A",
    inject: {
      api: "\u5F53\u524D\u8868\u5355\u7684api",
      rule: "\u5F53\u524D\u8868\u5355\u7684\u751F\u6210\u89C4\u5219",
      self: "\u7EC4\u4EF6\u7684\u751F\u6210\u89C4\u5219",
      option: "\u8868\u5355\u7684\u914D\u7F6E",
      args: "\u4E8B\u4EF6\u7684\u539F\u59CB\u53C2\u6570"
    }
  },
  eventInfo: {
    blur: "\u5931\u53BB\u7126\u70B9\u65F6\u89E6\u53D1",
    focus: "\u83B7\u5F97\u7126\u70B9\u65F6\u89E6\u53D1",
    change: "\u5F53\u7ED1\u5B9A\u503C\u53D8\u5316\u65F6\u89E6\u53D1",
    input: "\u5728\u503C\u6539\u53D8\u65F6\u89E6\u53D1",
    clear: "\u5728\u70B9\u51FB\u6E05\u7A7A\u6309\u94AE\u65F6\u89E6\u53D1",
    close: "\u5173\u95ED\u7EC4\u4EF6\u65F6\u89E6\u53D1",
    click: "\u70B9\u51FB\u7EC4\u4EF6\u65F6\u89E6\u53D1",
    add: "\u589E\u52A0\u65F6\u89E6\u53D1",
    delete: "\u5220\u9664\u65F6\u89E6\u53D1",
    remove: "\u5220\u9664\u65F6\u89E6\u53D1",
    visibleChange: "\u4E0B\u62C9\u6846\u51FA\u73B0/\u9690\u85CF\u65F6\u89E6\u53D1",
    calendarChange: "\u5728\u65E5\u5386\u6240\u9009\u65E5\u671F\u66F4\u6539\u65F6\u89E6\u53D1",
    panelChange: "\u5F53\u65E5\u671F\u9762\u677F\u6539\u53D8\u65F6\u89E6\u53D1",
    open: "\u6253\u5F00\u7684\u56DE\u8C03",
    opened: "\u6253\u5F00\u52A8\u753B\u7ED3\u675F\u65F6\u7684\u56DE\u8C03",
    closed: "\u5173\u95ED\u52A8\u753B\u7ED3\u675F\u65F6\u7684\u56DE\u8C03",
    openAutoFocus: "\u8F93\u5165\u7126\u70B9\u805A\u7126\u5728\u5185\u5BB9\u65F6\u7684\u56DE\u8C03",
    closeAutoFocus: "\u8F93\u5165\u7126\u70B9\u4ECE\u5185\u5BB9\u5931\u7126\u65F6\u7684\u56DE\u8C03",
    submit: "\u8868\u5355\u63D0\u4EA4\u65F6\u89E6\u53D1",
    confirm: "\u70B9\u51FB\u786E\u8BA4\u6309\u94AE\u65F6\u89E6\u53D1",
    validateFail: "\u8868\u5355\u9A8C\u8BC1\u5931\u8D25\u65F6\u89E6\u53D1",
    beforeLoad: "\u521D\u59CB\u5316\u4E4B\u524D\u89E6\u53D1",
    loaded: "\u521D\u59CB\u5316\u5B8C\u6210\u4E4B\u540E\u89E6\u53D1",
    hook_load: "\u7EC4\u4EF6\u89C4\u5219\u52A0\u8F7D\u540E\u89E6\u53D1",
    hook_mounted: "\u7EC4\u4EF6\u6302\u8F7D\u540E\u89E6\u53D1",
    hook_deleted: "\u7EC4\u4EF6\u89C4\u5219\u88AB\u79FB\u9664\u540E\u89E6\u53D1",
    hook_watch: "\u7EC4\u4EF6\u89C4\u5219\u53D1\u751F\u53D8\u5316\u540E\u89E6\u53D1",
    hook_value: "\u7EC4\u4EF6\u7684\u503C\u53D1\u751F\u53D8\u5316\u540E\u89E6\u53D1",
    hook_hidden: "\u7EC4\u4EF6\u663E\u793A\u72B6\u6001\u53D1\u751F\u53D8\u5316\u540E\u89E6\u53D1"
  },
  fetch: {
    info: "\u5B9A\u4E49\u8BF7\u6C42\u65F6\uFF0C\u652F\u6301\u901A\u8FC7\u53CC\u5927\u62EC\u53F7\u8BED\u6CD5\uFF08\u5982 {{token}}\uFF09\u4F7F\u7528\u53D8\u91CF\u3002\u5728\u63A5\u53E3\u8BF7\u6C42\u65F6\u4F1A\u81EA\u52A8\u8BFB\u53D6\u8FD9\u4E9B\u53D8\u91CF\u3002\u5982\u679C\u53D8\u91CF\u7684\u503C\u662F\u5BF9\u8C61\uFF0C\u53EF\u4EE5\u901A\u8FC7 {{variableName.attributeName}} \u8BBF\u95EE\u5BF9\u8C61\u7684\u5C5E\u6027\u503C\u3002",
    title: "\u8BBE\u7F6E\u6570\u636E\u6E90",
    create: "\u521B\u5EFA\u6570\u636E\u6E90",
    config: "\u8BF7\u6C42\u914D\u7F6E",
    action: "\u8BF7\u6C42\u94FE\u63A5",
    actionRequired: "\u8BF7\u8F93\u5165\u6B63\u786E\u7684\u94FE\u63A5",
    placeholder: "\u8BF7\u8F93\u5165\u6570\u636E\u6E90\u7684\u63CF\u8FF0\u4FE1\u606F",
    method: "\u8BF7\u6C42\u65B9\u5F0F",
    data: "\u9644\u5E26\u6570\u636E",
    dataType: "\u6570\u636E\u7C7B\u578B",
    headers: "\u8BF7\u6C42\u5934\u90E8",
    query: "\u8BF7\u6C42\u53C2\u6570",
    parse: "\u6570\u636E\u5904\u7406",
    response: "\u63A5\u53E3\u8FD4\u56DE\u7684\u6570\u636E",
    onError: "\u9519\u8BEF\u5904\u7406",
    remote: "\u8FDC\u7A0B\u6570\u636E",
    static: "\u9759\u6001\u6570\u636E",
    optionsType: {
      fetch: "\u8FDC\u7A0B\u6570\u636E",
      global: "\u5168\u5C40\u6570\u636E\u6E90",
      struct: "\u9759\u6001\u6570\u636E"
    }
  },
  style: {
    width: "\u5BBD\u5EA6",
    height: "\u9AD8\u5EA6",
    minWidth: "\u6700\u5C0F\u5BBD",
    minHeight: "\u6700\u5C0F\u9AD8",
    maxWidth: "\u6700\u5927\u5BBD",
    maxHeight: "\u6700\u5927\u9AD8",
    color: "\u989C\u8272",
    backgroundColor: "\u80CC\u666F\u8272",
    margin: "\u5916\u8FB9\u8DDD",
    padding: "\u5185\u8FB9\u8DDD",
    borderRadius: "\u5706\u89D2",
    border: "\u8FB9\u6846",
    solid: "\u5B9E\u7EBF",
    dashed: "\u865A\u7EBF",
    dotted: "\u70B9\u72B6\u865A\u7EBF",
    double: "\u53CC\u5B9E\u7EBF",
    opacity: "\u900F\u660E\u5EA6",
    scale: "\u7F29\u653E",
    overflow: {
      name: "\u6EA2\u51FA",
      visible: "\u53EF\u89C1",
      hidden: "\u9690\u85CF",
      scroll: "\u6EDA\u52A8",
      auto: "\u6EA2\u51FA\u540E\u81EA\u52A8\u6EDA\u52A8"
    },
    shadow: {
      name: "\u9634\u5F71",
      x: "x\u8F74\u504F\u79FB\u91CF",
      y: "y\u8F74\u504F\u79FB\u91CF",
      vague: "\u6A21\u7CCA\u534A\u5F84",
      extend: "\u6269\u6563\u534A\u5F84",
      inset: "\u5411\u5185",
      external: "\u5411\u5916",
      mode: "\u6A21\u5F0F",
      classic: "\u7ECF\u5178",
      flat: "\u6241\u5E73",
      solid: "\u7ACB\u4F53"
    },
    display: {
      name: "\u5E03\u5C40",
      block: "\u533A\u5757",
      "inline-block": "\u884C\u5185\u533A\u5757",
      inline: "\u884C\u5185\u6587\u672C",
      flex: "\u5F39\u6027\u76D2\u5B50"
    },
    flexDirection: {
      name: "\u4E3B\u8F74\u7684\u65B9\u5411",
      row: "\u4E3B\u8F74\u4E3A\u6C34\u5E73\u65B9\u5411\uFF0C\u8D77\u70B9\u5728\u5DE6\u7AEF",
      "row-reverse": "\u4E3B\u8F74\u4E3A\u6C34\u5E73\u65B9\u5411\uFF0C\u8D77\u70B9\u5728\u53F3\u7AEF",
      column: "\u4E3B\u8F74\u4E3A\u5782\u76F4\u65B9\u5411\uFF0C\u8D77\u70B9\u5728\u4E0A\u6CBF",
      "column-reverse": "\u4E3B\u8F74\u4E3A\u5782\u76F4\u65B9\u5411\uFF0C\u8D77\u70B9\u5728\u4E0B\u6CBF"
    },
    flexWrap: {
      name: "\u662F\u5426\u6362\u884C",
      nowrap: "\u4E0D\u6362\u884C",
      wrap: "\u6362\u884C"
    },
    justifyContent: {
      name: "\u4E3B\u8F74\u4E0A\u7684\u5BF9\u9F50\u65B9\u5F0F",
      "flex-start": "\u5DE6\u5BF9\u9F50",
      "flex-end": "\u53F3\u5BF9\u9F50",
      center: "\u5C45\u4E2D",
      "space-between": "\u4E24\u7AEF\u5BF9\u9F50",
      "space-around": "\u5B50\u5143\u7D20\u4E24\u4FA7\u7684\u95F4\u9694\u76F8\u7B49",
      "space-evenly": "\u5B50\u5143\u7D20\u5E73\u5747\u5206\u5E03\u5728\u4E3B\u8F74\u4E0A"
    },
    alignItems: {
      name: "\u5B50\u5143\u7D20\u4EA4\u53C9\u8F74\u4E0A\u7684\u5BF9\u9F50\u65B9\u5F0F",
      "flex-start": "\u4EA4\u53C9\u8F74\u7684\u8D77\u70B9\u5BF9\u9F50",
      "flex-end": "\u4EA4\u53C9\u8F74\u7684\u7EC8\u70B9\u5BF9\u9F50",
      center: "\u4EA4\u53C9\u8F74\u7684\u4E2D\u70B9\u5BF9\u9F50",
      baseline: "\u6CBF\u7B2C\u4E00\u884C\u6587\u5B57\u7684\u57FA\u7EBF\u5BF9\u9F50",
      stretch: "\u6EE1\u6574\u4E2A\u5BB9\u5668\u7684\u9AD8\u5EA6"
    },
    alignContent: {
      name: "\u591A\u884C\u5B50\u5143\u7D20\u5728\u4EA4\u53C9\u8F74\u4E0A\u7684\u5BF9\u9F50\u65B9\u5F0F",
      "flex-start": "\u4E0E\u4EA4\u53C9\u8F74\u7684\u8D77\u70B9\u5BF9\u9F50",
      "flex-end": "\u4E0E\u4EA4\u53C9\u8F74\u7684\u7EC8\u70B9\u5BF9\u9F50",
      center: "\u4E0E\u4EA4\u53C9\u8F74\u7684\u4E2D\u70B9\u5BF9\u9F50",
      "space-between": "\u4E0E\u4EA4\u53C9\u8F74\u4E24\u7AEF\u5BF9\u9F50",
      "space-around": "\u591A\u884C\u5B50\u5143\u7D20\u5E73\u5747\u5206\u5E03\u5728\u4EA4\u53C9\u8F74",
      stretch: "\u8F74\u7EBF\u5360\u6EE1\u6574\u4E2A\u4EA4\u53C9\u8F74"
    },
    font: {
      name: "\u5B57\u4F53",
      size: "\u5927\u5C0F",
      align: "\u5BF9\u9F50\u65B9\u5F0F",
      height: "\u884C\u9AD8",
      spacing: "\u5B57\u95F4\u8DDD",
      preview: "\u6837\u5F0F\u9884\u89C8"
    },
    decoration: {
      name: "\u4FEE\u9970",
      underline: "\u4E0B\u5212\u7EBF",
      "line-through": "\u5220\u9664\u7EBF",
      overline: "\u4E0A\u5212\u7EBF"
    },
    weight: {
      name: "\u7C97\u7EC6",
      300: "\u7EC6\u4F53",
      400: "\u5E38\u89C4\u4F53",
      500: "\u4E2D\u9ED1\u4F53",
      700: "\u4E2D\u7C97\u4F53"
    }
  },
  designer: {
    component: "\u7EC4\u4EF6\u914D\u7F6E",
    id: "\u552F\u4E00\u503C",
    name: "\u7F16\u53F7",
    type: "\u7EC4\u4EF6\u7C7B\u578B",
    form: "\u663E\u9690\u914D\u7F6E",
    json: "\u6E32\u67D3\u89C4\u5219",
    style: "\u6837\u5F0F\u914D\u7F6E",
    rule: "\u57FA\u7840\u914D\u7F6E",
    advanced: "\u9AD8\u7EA7\u914D\u7F6E",
    props: "\u5C5E\u6027\u914D\u7F6E",
    slots: "\u63D2\u69FD\u914D\u7F6E",
    customProps: "\u81EA\u5B9A\u4E49\u5C5E\u6027\u914D\u7F6E",
    validate: "\u9A8C\u8BC1\u914D\u7F6E",
    event: "\u4E8B\u4EF6\u914D\u7F6E",
    clearWarn: "\u6E05\u7A7A\u540E\u5C06\u4E0D\u80FD\u6062\u590D\uFF0C\u786E\u5B9A\u8981\u6E05\u7A7A\u5417\uFF1F",
    childEmpty: "\u70B9\u51FB\u53F3\u4E0B\u89D2 \\e789  \u6309\u94AE\u6DFB\u52A0\u4E00\u5217",
    dragEmpty: "\u62D6\u62FD\u5DE6\u4FA7\u5217\u8868\u4E2D\u7684\u7EC4\u4EF6\u5230\u6B64\u5904",
    unload: "\u786E\u5B9A\u79BB\u5F00\u5F53\u524D\u9875\u9762\u5417?",
    sublist: "\u5B50\u8282\u70B9\u5217\u8868",
    formList: "\u8868\u5355\u5217\u8868",
    comList: "\u7EC4\u4EF6\u5217\u8868",
    addPage: "\u6DFB\u52A0\u6A21\u5757",
    pageManage: "\u6A21\u5757\u7BA1\u7406",
    main: "\u4E3B\u4F53",
    layout: "\u5FEB\u901F\u5E03\u5C40",
    col1: "\u4E00\u5217",
    col2: "\u4E24\u5217",
    col3: "\u4E09\u5217",
    col4: "\u56DB\u5217"
  },
  menu: {
    template: "\u6A21\u677F",
    main: "\u57FA\u7840\u7EC4\u4EF6",
    aide: "\u8F85\u52A9\u7EC4\u4EF6",
    layout: "\u5E03\u5C40\u7EC4\u4EF6",
    component: "\u7EC4\u4EF6",
    subform: "\u5B50\u8868\u5355\u7EC4\u4EF6",
    container: "\u5BB9\u5668\u7EC4\u4EF6",
    chart: "\u56FE\u8868\u7EC4\u4EF6",
    tree: "\u5927\u7EB2"
  },
  formula: {
    math: "\u6570\u5B57\u5904\u7406",
    string: "\u6587\u672C\u5904\u7406",
    date: "\u65F6\u95F4\u5904\u7406",
    collection: "\u5408\u96C6\u5904\u7406",
    condition: "\u903B\u8F91\u5904\u7406",
    ADD: "\u83B7\u53D6\u4E24\u4E2A\u6570\u5B57\u76F8\u52A0\u7684\u503C",
    SUB: "\u83B7\u53D6\u4E24\u4E2A\u6570\u5B57\u76F8\u51CF\u7684\u503C",
    MUL: "\u83B7\u53D6\u4E24\u4E2A\u6570\u5B57\u76F8\u4E58\u7684\u503C",
    DIV: "\u83B7\u53D6\u4E24\u4E2A\u6570\u5B57\u76F8\u9664\u7684\u503C",
    SUM: "\u83B7\u53D6\u5408\u96C6\u4E2D\u6570\u503C\u7684\u603B\u548C",
    MAX: "\u83B7\u53D6\u53C2\u6570\u5217\u8868\u4E2D\u7684\u6700\u5927\u503C",
    MIN: "\u83B7\u53D6\u53C2\u6570\u5217\u8868\u4E2D\u7684\u6700\u5C0F\u503C",
    ABS: "\u83B7\u53D6\u6570\u5B57\u7684\u7EDD\u5BF9\u503C",
    AVG: "\u83B7\u53D6\u53C2\u6570\u5217\u8868\u7684\u5E73\u5747\u503C",
    POWER: "\u83B7\u53D6\u6307\u5B9A\u6570\u5B57\u7684\u4E58\u5E42",
    RAND: "\u83B7\u53D6\u4E00\u4E2A\u5927\u4E8E\u7B49\u4E8E0\u4E14\u5C0F\u4E8E1\u7684\u968F\u673A\u6570",
    PI: "\u83B7\u53D6\u5706\u5468\u7387",
    ROUND: "\u5C06\u4E00\u4E2A\u5C0F\u6570\u56DB\u820D\u4E94\u5165\u5230\u6307\u5B9A\u7684\u4F4D\u6570",
    SQRT: "\u83B7\u53D6\u4E00\u4E2A\u6570\u5B57\u7684\u6B63\u5E73\u65B9\u6839",
    NOW: "\u83B7\u53D6\u5F53\u524D\u7684\u65F6\u95F4",
    TODAY: "\u83B7\u53D6\u4ECA\u5929\u7684\u65E5\u671F",
    YEAR: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u5E74\u4EFD",
    MONTH: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u6708\u4EFD",
    DAY: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u5929\u6570",
    HOUR: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u5C0F\u65F6\u6570",
    MINUTE: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u5206\u949F\u6570",
    SECOND: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u79D2\u6570",
    DIFFDAYS: "\u83B7\u53D6\u4E24\u4E2A\u65E5\u671F\u4E4B\u95F4\u7684\u5929\u6570",
    DIFFHOURS: "\u83B7\u53D6\u4E24\u4E2A\u65F6\u95F4\u4E4B\u95F4\u7684\u5C0F\u65F6\u6570,\u4FDD\u7559\u4E24\u4F4D\u5C0F\u6570",
    DIFFMINUTES: "\u83B7\u53D6\u4E24\u4E2A\u65F6\u95F4\u4E4B\u95F4\u7684\u5206\u949F\u6570",
    TIMESTAMP: "\u83B7\u53D6\u6307\u5B9A\u65E5\u671F\u7684\u65F6\u95F4\u6233",
    STARTSWITH: "\u68C0\u67E5\u5B57\u7B26\u4E32\u662F\u5426\u4EE5\u6307\u5B9A\u5B57\u7B26\u4E32\u5F00\u5934",
    EMPTY: "\u68C0\u67E5\u53C2\u6570\u662F\u5426\u4E3A\u7A7A",
    NOTEMPTY: "\u68C0\u67E5\u53C2\u6570\u662F\u5426\u4E0D\u4E3A\u7A7A",
    LEN: "\u83B7\u53D6\u6307\u5B9A\u5408\u96C6\u7684\u957F\u5EA6",
    MOD: "\u83B7\u53D6\u4E24\u4E2A\u6570\u5B57\u7684\u4F59\u6570",
    FLOOR: "\u83B7\u53D6\u6307\u5B9A\u6570\u5B57\u5411\u4E0B\u53D6\u6574\u7684\u503C",
    CEIL: "\u83B7\u53D6\u6307\u5B9A\u6570\u5B57\u5411\u4E0A\u53D6\u6574\u7684\u503C",
    FIXED: "\u5C06\u4E00\u4E2A\u5C0F\u6570\u4FDD\u7559\u6307\u5B9A\u4F4D\u6570\u7684\u5C0F\u6570",
    ISNUMBER: "\u68C0\u67E5\u53C2\u6570\u662F\u5426\u4E3A\u6570\u5B57",
    TONUMBER: "\u5C06\u53C2\u6570\u8F6C\u6362\u4E3A\u6570\u5B57",
    SLICELEFT: "\u83B7\u53D6\u4E00\u4E2A\u5B57\u7B26\u4E32\u4ECE\u5F00\u5934\u5F00\u59CB\u6307\u5B9A\u957F\u5EA6\u7684\u5B57\u7B26\u4E32",
    SLICERIGHT: "\u83B7\u53D6\u4E00\u4E2A\u5B57\u7B26\u4E32\u4ECE\u7ED3\u5C3E\u5F00\u59CB\u6307\u5B9A\u957F\u5EA6\u7684\u5B57\u7B26\u4E32",
    TOLOWER: "\u5C06\u5B57\u7B26\u4E32\u4E2D\u6240\u6709\u5927\u5199\u5B57\u6BCD\u8F6C\u6362\u4E3A\u5C0F\u5199\u5B57\u6BCD",
    TOUPPER: "\u5C06\u5B57\u7B26\u4E32\u4E2D\u6240\u6709\u5C0F\u5199\u5B57\u6BCD\u8F6C\u6362\u4E3A\u5927\u5199\u5B57\u6BCD",
    INCLUDES: "\u68C0\u67E5\u5B57\u7B26\u4E32\u4E2D\u662F\u5426\u5305\u542B\u6307\u5B9A\u5B57\u7B26\u4E32",
    REPLACE: "\u5C06\u5B57\u7B26\u4E32\u4E2D\u7684\u90E8\u5206\u6587\u672C\u66FF\u6362\u4E3A\u4E0D\u540C\u7684\u6587\u672C,\u53EA\u66FF\u6362\u5339\u914D\u5230\u7684\u7B2C\u4E00\u4E2A",
    REPLACEALL: "\u5C06\u5B57\u7B26\u4E32\u4E2D\u7684\u90E8\u5206\u6587\u672C\u66FF\u6362\u4E3A\u4E0D\u540C\u7684\u6587\u672C,\u66FF\u6362\u6240\u6709\u5339\u914D\u5230\u7684",
    TRIM: "\u5C06\u5B57\u7B26\u4E32\u524D\u540E\u7684\u7A7A\u683C\u5220\u9664",
    TOCHINSESAMOUNT: "\u83B7\u53D6\u6307\u5B9A\u6570\u5B57\u7684\u4E2D\u6587\u5927\u5199\u91D1\u989D",
    UNION: "\u5C06\u5408\u96C6/\u53C2\u6570\u4E2D\u7684\u503C\u53BB\u91CD,\u8FD4\u56DE\u53BB\u91CD\u540E\u7684\u5408\u96C6",
    INTERSECTIONSET: "\u83B7\u53D6\u4E24\u4E2A\u96C6\u5408\u7684\u4EA4\u96C6",
    LIST: "\u83B7\u53D6\u6240\u6709\u53C2\u6570\u7EC4\u6210\u7684\u96C6\u5408",
    AND: '\u5C06\u8868\u8FBE\u5F0F\u7528"\u5E76\u4E14"\u94FE\u63A5,\u5F53\u6240\u6709\u8868\u8FBE\u5F0F\u5747\u4E3Atrue\u65F6\u8FD4\u56DEtrue\uFF0C\u5426\u5219\u8FD4\u56DEfalse',
    OR: '\u5C06\u8868\u8FBE\u5F0F\u7528"\u6216"\u94FE\u63A5,\u5F53\u6709\u4E00\u4E2A\u8868\u8FBE\u5F0F\u4E3Atrue\u65F6\u8FD4\u56DEtrue\uFF0C\u5426\u5219\u8FD4\u56DEfalse',
    IF: "\u68C0\u67E5\u4E00\u4E2A\u6761\u4EF6\u80FD\u5426\u6EE1\u8DB3,\u5982\u679C\u6EE1\u8DB3\u8FD4\u56DE\u7B2C\u4E8C\u4E2A\u53C2\u6570\uFF0C\u5426\u5219\u8FD4\u56DE\u7B2C\u4E09\u4E2A\u53C2\u6570",
    IN: "\u68C0\u67E5\u7B2C\u4E8C\u4E2A\u53C2\u6570\u662F\u5426\u5728\u5408\u96C6\u4E2D",
    DEFAULT: "\u68C0\u67E5\u7B2C\u4E00\u4E2A\u53C2\u6570,\u5982\u679C\u4E3A\u7A7A\u8FD4\u56DE\u7B2C\u4E8C\u4E2A\u53C2\u6570,\u5426\u5219\u8FD4\u56DE\u7B2C\u4E00\u4E2A\u53C2\u6570",
    CASE: "\u68C0\u67E5\u662F\u5426\u6EE1\u8DB3\u4E00\u4E2A\u6216\u591A\u4E2A\u6761\u4EF6\uFF0C\u8FD4\u56DE\u7B2C\u4E00\u4E2A\u6EE1\u8DB3\u6761\u4EF6\u7684\u503C",
    COLUMN: "\u83B7\u53D6\u5B50\u8868\u5355\u4E2D\u6307\u5B9A\u5B57\u6BB5\u5E76\u8FD4\u56DE\u5408\u96C6",
    VALUE: "\u83B7\u53D6\u5206\u7EC4\u8868\u5355\u4E2D\u6307\u5B9A\u5B57\u6BB5",
    CONCAT: "\u5C06\u6240\u6709\u53C2\u6570\u62FC\u63A5,\u8FD4\u56DE\u62FC\u63A5\u540E\u7684\u5B57\u7B26\u4E32",
    FALSE: "\u8FD4\u56DE\u903B\u8F91\u503C false",
    TRUE: "\u8FD4\u56DE\u903B\u8F91\u503C true",
    NOT: "\u83B7\u53D6\u67D0\u4E2A\u903B\u8F91\u503C\u7684\u76F8\u53CD\u503C",
    EQ: "\u68C0\u67E5\u4E24\u4E2A\u503C\u662F\u5426\u76F8\u7B49",
    NE: "\u68C0\u67E5\u4E24\u4E2A\u503C\u662F\u5426\u4E0D\u76F8\u7B49",
    GE: "\u68C0\u67E5\u7B2C\u4E00\u4E2A\u503C\u662F\u5426\u5927\u4E8E\u7B49\u4E8E\u53E6\u4E00\u4E2A\u503C",
    GT: "\u68C0\u67E5\u7B2C\u4E00\u4E2A\u503C\u662F\u5426\u5927\u4E8E\u53E6\u4E00\u4E2A\u503C",
    LE: "\u68C0\u67E5\u7B2C\u4E00\u4E2A\u503C\u662F\u5426\u5C0F\u4E8E\u7B49\u4E8E\u53E6\u4E00\u4E2A\u503C",
    LT: "\u68C0\u67E5\u7B2C\u4E00\u4E2A\u503C\u662F\u5426\u5C0F\u4E8E\u53E6\u4E00\u4E2A\u503C"
  },
  language: {
    name: "\u56FD\u9645\u5316\u914D\u7F6E",
    add: "\u65B0\u589E\u8BCD\u6761",
    batchRemove: "\u6279\u91CF\u5220\u9664",
    select: "\u9009\u62E9\u591A\u8BED\u8A00"
  },
  props: {
    circle: "\u5706\u5F62",
    square: "\u6B63\u65B9\u5F62",
    image: "\u56FE\u7247",
    video: "\u89C6\u9891",
    audio: "\u97F3\u9891",
    document: "\u6587\u6863",
    size: "\u5C3A\u5BF8",
    info: "\u63D0\u793A",
    success: "\u6210\u529F",
    error: "\u5931\u8D25",
    warning: "\u8B66\u544A",
    primary: "\u91CD\u8981",
    danger: "\u5371\u9669",
    form: "\u8868\u5355",
    subform: "\u5B50\u8868\u5355",
    other: "\u5176\u4ED6",
    model: "\u5F39\u7A97",
    field: "\u5B57\u6BB5",
    variable: "\u53D8\u91CF",
    disabled: "\u7981\u7528",
    enable: "\u542F\u7528",
    time: "\u65F6\u95F4",
    email: "\u90AE\u7BB1",
    number: "\u6570\u5B57",
    globalData: "\u5168\u5C40\u6570\u636E",
    mobile: "\u79FB\u52A8\u7AEF",
    reactive: "\u54CD\u5E94\u5F0F",
    pc: "\u7535\u8111\u7AEF",
    title: "\u6807\u9898",
    content: "\u5185\u5BB9",
    collection: "\u5408\u96C6",
    group: "\u5206\u7EC4",
    custom: "\u81EA\u5B9A\u4E49",
    change: "\u6539\u53D8",
    blur: "\u5931\u53BB\u7126\u70B9",
    preview: "\u9884\u89C8",
    clear: "\u6E05\u7A7A",
    cancel: "\u53D6\u6D88",
    close: "\u5173\u95ED",
    ok: "\u786E\u5B9A",
    save: "\u4FDD\u5B58",
    refresh: "\u5237\u65B0",
    submit: "\u63D0\u4EA4",
    reset: "\u91CD\u7F6E",
    copy: "\u590D\u5236",
    delete: "\u5220\u9664",
    hide: "\u9690\u85CF",
    show: "\u663E\u793A",
    position: "\u4F4D\u7F6E",
    render: "\u6E32\u67D3",
    large: "\u5927",
    default: "\u9ED8\u8BA4",
    small: "\u5C0F",
    always: "\u5E38\u663E",
    never: "\u4E0D\u663E\u793A",
    hover: "\u60AC\u6D6E",
    click: "\u70B9\u51FB",
    button: "\u6309\u94AE",
    year: "\u5E74\u4EFD",
    month: "\u6708\u4EFD",
    date: "\u65E5\u671F",
    dates: "\u65E5\u671F\u591A\u9009",
    week: "\u4E00\u5468",
    datetime: "\u65E5\u671F\u65F6\u95F4",
    "datetime-local": "\u65E5\u671F\u65F6\u95F4",
    datetimerange: "\u65E5\u671F\u65F6\u95F4\u533A\u95F4",
    daterange: "\u65E5\u671F\u533A\u95F4",
    monthrange: "\u6708\u4EFD\u533A\u95F4",
    left: "\u5DE6\u5BF9\u9F50",
    right: "\u53F3\u5BF9\u9F50",
    top: "\u9876\u90E8",
    bottom: "\u5E95\u90E8",
    text: "\u6587\u5B57",
    icon: "\u56FE\u6807",
    picture: "\u56FE\u7247",
    "picture-card": "\u5361\u7247",
    center: "\u5C45\u4E2D",
    vertical: "\u7AD6\u5411",
    horizontal: "\u6A2A\u5411",
    manage: "\u7BA1\u7406",
    key: "\u952E\u540D",
    name: "\u540D\u79F0",
    value: "\u503C",
    inputData: "\u9ED8\u8BA4\u503C",
    append: "\u63D2\u5165",
    options: "\u9009\u9879\u6570\u636E",
    option: "\u9009\u9879",
    callback: "\u56DE\u8C03",
    _self: "\u5F53\u524D\u7A97\u53E3",
    _blank: "\u65B0\u7684\u7A97\u53E3",
    _parent: "\u7236\u7EA7\u7A97\u53E3",
    _top: "\u9876\u7EA7\u7A97\u53E3"
  },
  slots: {
    prefix: "\u5934\u90E8\u5185\u5BB9",
    suffix: "\u5C3E\u90E8\u5185\u5BB9",
    prepend: "\u524D\u7F6E\u5185\u5BB9",
    append: "\u540E\u7F6E\u5185\u5BB9"
  },
  behavior: {
    add: "\u6DFB\u52A0\u52A8\u4F5C",
    props: {
      id: "\u9009\u62E9\u7EC4\u4EF6",
      status: "\u72B6\u6001",
      compute: "\u6761\u4EF6",
      static: "\u9759\u6001",
      formula: "\u8868\u8FBE\u5F0F",
      setFormula: "\u914D\u7F6E\u8868\u8FBE\u5F0F",
      continue: "\u7EE7\u7EED\u6267\u884C\u52A8\u4F5C",
      stop: "\u4E2D\u65AD\u6267\u884C\u52A8\u4F5C",
      break: "\u8DF3\u8FC7\u5F53\u524D\u52A8\u4F5C",
      model: "\u9009\u62E9\u5F39\u7A97",
      fetch: "\u914D\u7F6E\u8BF7\u6C42",
      response: "\u8BF7\u6C42\u7ED3\u679C",
      callback: "\u81EA\u5B9A\u4E49JS",
      ignoreError: "\u6267\u884C\u5F02\u5E38",
      expression: "\u6267\u884C\u6761\u4EF6",
      stopPropagation: "\u963B\u65AD\u6761\u4EF6",
      execute: "\u6267\u884C\u52A8\u4F5C",
      info: "\u52A8\u4F5C\u8BF4\u660E"
    },
    openModel: {
      name: "\u6253\u5F00\u5F39\u7A97",
      info: "\u6253\u5F00\u6240\u9009\u7684\u5F39\u7A97"
    },
    closeModel: {
      name: "\u5173\u95ED\u5F39\u7A97",
      info: "\u5173\u95ED\u5F53\u524D\u5F39\u7A97"
    },
    hidden: {
      name: "\u7EC4\u4EF6\u53EF\u89C1\u6027",
      info: "\u63A7\u5236\u6240\u9009\u7684\u7EC4\u4EF6\u7684\u663E\u793A/\u9690\u85CF"
    },
    disabled: {
      name: "\u7EC4\u4EF6\u53EF\u7528\u6027",
      info: "\u63A7\u5236\u6240\u9009\u7684\u7EC4\u4EF6\u7684\u542F\u7528/\u7981\u7528"
    },
    resetFields: {
      name: "\u91CD\u7F6E\u8868\u5355",
      info: "\u91CD\u7F6E\u8868\u5355\u6570\u636E"
    },
    clearFields: {
      name: "\u6E05\u7A7A\u8868\u5355",
      info: "\u6E05\u7A7A\u8868\u5355\u6570\u636E"
    },
    validate: {
      name: "\u6821\u9A8C\u8868\u5355",
      info: "\u5BF9\u6574\u4E2A\u8868\u5355\u7684\u5185\u5BB9\u8FDB\u884C\u6821\u9A8C"
    },
    validateFields: {
      name: "\u6821\u9A8C\u8868\u5355\u9879",
      info: "\u6821\u9A8C\u6240\u9009\u7684\u7684\u8868\u5355\u9879"
    },
    setValue: {
      name: "\u8868\u5355\u8D4B\u503C",
      info: "\u4FEE\u6539\u8868\u5355\u7684\u6570\u636E"
    },
    fetch: {
      name: "\u53D1\u9001\u8BF7\u6C42",
      info: "\u914D\u7F6E\u5E76\u53D1\u9001API\u8BF7\u6C42",
      props: {
        append: "\u8FFD\u52A0\u8868\u5355\u6570\u636E"
      },
      warning: {
        append: "\u5F00\u542F\u540E\uFF0C\u8FDC\u7A0B\u8BF7\u6C42\u8FD4\u56DE\u7684\u6570\u636E\u5C06\u81EA\u52A8\u8FFD\u52A0\u5230\u8868\u5355\u7684\u6570\u636E\u4E2D\u3002",
        response: "\u8FDC\u7A0B\u8BF7\u6C42\u8FD4\u56DE\u7684\u6570\u636E\u5C06\u6682\u5B58\u5230\u6307\u5B9A\u7684\u53D8\u91CF\u4E2D\u3002"
      }
    },
    copy: {
      name: "\u590D\u5236\u5185\u5BB9",
      info: "\u590D\u5236\u6587\u672C\u5185\u5BB9\u81F3\u7C98\u8D34\u677F"
    },
    callback: {
      name: "\u81EA\u5B9A\u4E49\u64CD\u4F5C",
      info: "\u901A\u8FC7JavaScript\u81EA\u5B9A\u4E49\u52A8\u4F5C\u903B\u8F91"
    },
    message: {
      name: "\u6D88\u606F\u63D0\u9192",
      info: "\u5F39\u51FA\u6D88\u606F\u63D0\u9192",
      props: {
        type: "\u7C7B\u578B",
        message: "\u63D0\u793A\u4FE1\u606F",
        duration: "\u6301\u7EED\u65F6\u95F4(ms)",
        showClose: "\u663E\u793A\u5173\u95ED\u6309\u94AE"
      }
    },
    submit: {
      name: "\u63D0\u4EA4\u8868\u5355",
      info: "\u624B\u52A8\u63D0\u4EA4\u8868\u5355\u5E76\u89E6\u53D1\u8868\u5355\u63D0\u4EA4\u4E8B\u4EF6"
    }
  },
  com: {
    cascader: {
      name: "\u7EA7\u8054\u9009\u62E9\u5668",
      event: {
        expandChange: "\u5F53\u5C55\u5F00\u8282\u70B9\u53D1\u751F\u53D8\u5316\u65F6\u89E6\u53D1",
        removeTag: "\u5728\u591A\u9009\u6A21\u5F0F\u4E0B\uFF0C\u79FB\u9664Tag\u65F6\u89E6\u53D1"
      },
      props: {
        props: "\u914D\u7F6E\u9009\u9879",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C",
        disabled: "\u662F\u5426\u7981\u7528",
        clearable: "\u662F\u5426\u652F\u6301\u6E05\u7A7A\u9009\u9879",
        showAllLevels: "\u8F93\u5165\u6846\u4E2D\u662F\u5426\u663E\u793A\u9009\u4E2D\u503C\u7684\u5B8C\u6574\u8DEF\u5F84",
        collapseTags: "\u591A\u9009\u6A21\u5F0F\u4E0B\u662F\u5426\u6298\u53E0Tag",
        collapseTagsTooltip: "\u5F53\u9F20\u6807\u60AC\u505C\u4E8E\u6298\u53E0\u6807\u7B7E\u7684\u6587\u672C\u65F6\uFF0C\u662F\u5426\u663E\u793A\u6240\u6709\u9009\u4E2D\u7684\u6807\u7B7E",
        separator: "\u9009\u9879\u5206\u9694\u7B26",
        filterable: "\u8BE5\u9009\u9879\u662F\u5426\u53EF\u4EE5\u88AB\u641C\u7D22",
        tagType: "\u6807\u7B7E\u7C7B\u578B"
      },
      propsOpt: {
        multiple: "\u662F\u5426\u591A\u9009",
        expandTrigger: "\u6B21\u7EA7\u83DC\u5355\u7684\u5C55\u5F00\u65B9\u5F0F",
        checkStrictly: "\u662F\u5426\u4E25\u683C\u7684\u9075\u5B88\u7236\u5B50\u8282\u70B9\u4E0D\u4E92\u76F8\u5173\u8054",
        emitPath: "\u5728\u9009\u4E2D\u8282\u70B9\u6539\u53D8\u65F6\uFF0C\u662F\u5426\u8FD4\u56DE\u7531\u8BE5\u8282\u70B9\u6240\u5728\u7684\u5404\u7EA7\u83DC\u5355\u7684\u503C\u6240\u7EC4\u6210\u7684\u6570\u7EC4",
        value: "\u6307\u5B9A\u9009\u9879\u7684\u503C\u4E3A\u9009\u9879\u5BF9\u8C61\u7684\u67D0\u4E2A\u5C5E\u6027\u503C",
        label: "\u6307\u5B9A\u9009\u9879\u6807\u7B7E\u4E3A\u9009\u9879\u5BF9\u8C61\u7684\u67D0\u4E2A\u5C5E\u6027\u503C",
        children: "\u6307\u5B9A\u9009\u9879\u7684\u5B50\u9009\u9879\u4E3A\u9009\u9879\u5BF9\u8C61\u7684\u67D0\u4E2A\u5C5E\u6027\u503C",
        disabled: "\u6307\u5B9A\u9009\u9879\u7684\u7981\u7528\u4E3A\u9009\u9879\u5BF9\u8C61\u7684\u67D0\u4E2A\u5C5E\u6027\u503C",
        leaf: "\u6307\u5B9A\u9009\u9879\u7684\u53F6\u5B50\u8282\u70B9\u7684\u6807\u5FD7\u4F4D\u4E3A\u9009\u9879\u5BF9\u8C61\u7684\u67D0\u4E2A\u5C5E\u6027\u503C"
      }
    },
    checkbox: {
      name: "\u591A\u9009\u6846",
      props: {
        input: "\u662F\u5426\u53EF\u4EE5\u586B\u5199",
        type: "\u6309\u94AE\u7C7B\u578B",
        disabled: "\u662F\u5426\u7981\u7528",
        min: "\u53EF\u88AB\u52FE\u9009\u7684\u6700\u5C0F\u6570\u91CF",
        max: "\u53EF\u88AB\u52FE\u9009\u7684\u6700\u5927\u6570\u91CF",
        textColor: "\u5F53\u6309\u94AE\u4E3A\u6D3B\u8DC3\u72B6\u6001\u65F6\u7684\u5B57\u4F53\u989C\u8272",
        fill: "\u5F53\u6309\u94AE\u4E3A\u6D3B\u8DC3\u72B6\u6001\u65F6\u7684\u8FB9\u6846\u548C\u80CC\u666F\u989C\u8272"
      }
    },
    col: {
      name: "\u5E03\u5C40\u683C\u5B50",
      info: "\u901A\u8FC7\u54CD\u5E94\u5F0F\u914D\u7F6E\u591A\u7AEF\u5E03\u5C40\uFF0C\u79FB\u52A8\u7AEF\u4F1A\u81EA\u52A8\u5E94\u7528\u7B2C\u4E00\u4E2A",
      props: {
        span: "\u6805\u683C\u5360\u636E\u7684\u5217\u6570",
        offset: "\u6805\u683C\u5DE6\u4FA7\u7684\u95F4\u9694\u683C\u6570",
        push: "\u6805\u683C\u5411\u53F3\u79FB\u52A8\u683C\u6570",
        pull: "\u6805\u683C\u5411\u5DE6\u79FB\u52A8\u683C\u6570"
      }
    },
    colorPicker: {
      name: "\u989C\u8272\u9009\u62E9\u5668",
      event: {
        activeChange: "\u9762\u677F\u4E2D\u5F53\u524D\u663E\u793A\u7684\u989C\u8272\u53D1\u751F\u6539\u53D8\u65F6\u89E6\u53D1"
      },
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        showAlpha: "\u662F\u5426\u652F\u6301\u900F\u660E\u5EA6\u9009\u62E9",
        colorFormat: "\u989C\u8272\u7684\u683C\u5F0F",
        predefine: "\u9884\u5B9A\u4E49\u989C\u8272"
      }
    },
    datePicker: {
      name: "\u65E5\u671F",
      props: {
        pickerOptions: "\u5F53\u524D\u65F6\u95F4\u65E5\u671F\u9009\u62E9\u5668\u7279\u6709\u7684\u9009\u9879",
        readonly: "\u5B8C\u5168\u53EA\u8BFB",
        disabled: "\u7981\u7528",
        type: "\u663E\u793A\u7C7B\u578B",
        editable: "\u6587\u672C\u6846\u53EF\u8F93\u5165",
        clearable: "\u662F\u5426\u663E\u793A\u6E05\u9664\u6309\u94AE",
        placeholder: "\u975E\u8303\u56F4\u9009\u62E9\u65F6\u7684\u5360\u4F4D\u5185\u5BB9",
        startPlaceholder: "\u8303\u56F4\u9009\u62E9\u65F6\u5F00\u59CB\u65E5\u671F\u7684\u5360\u4F4D\u5185\u5BB9",
        endPlaceholder: "\u8303\u56F4\u9009\u62E9\u65F6\u7ED3\u675F\u65E5\u671F\u7684\u5360\u4F4D\u5185\u5BB9",
        format: "\u663E\u793A\u5728\u8F93\u5165\u6846\u4E2D\u7684\u683C\u5F0F",
        align: "\u5BF9\u9F50\u65B9\u5F0F",
        rangeSeparator: "\u9009\u62E9\u8303\u56F4\u65F6\u7684\u5206\u9694\u7B26",
        unlinkPanels: "\u5728\u8303\u56F4\u9009\u62E9\u5668\u91CC\u53D6\u6D88\u4E24\u4E2A\u65E5\u671F\u9762\u677F\u4E4B\u95F4\u7684\u8054\u52A8"
      }
    },
    dateRange: {
      name: "\u65E5\u671F\u533A\u95F4"
    },
    timeRange: {
      name: "\u65F6\u95F4\u533A\u95F4"
    },
    elAlert: {
      name: "\u63D0\u793A",
      description: "\u8BF4\u660E\u6587\u5B57",
      props: {
        title: "\u6807\u9898",
        type: "\u4E3B\u9898",
        description: "\u8F85\u52A9\u6027\u6587\u5B57",
        closable: "\u662F\u5426\u53EF\u5173\u95ED",
        center: "\u6587\u5B57\u662F\u5426\u5C45\u4E2D",
        closeText: "\u5173\u95ED\u6309\u94AE\u81EA\u5B9A\u4E49\u6587\u672C",
        showIcon: "\u662F\u5426\u663E\u793A\u56FE\u6807",
        effect: "\u9009\u62E9\u63D0\u4F9B\u7684\u4E3B\u9898"
      }
    },
    elButton: {
      name: "\u6309\u94AE",
      props: {
        formCreateChild: "\u5185\u5BB9",
        size: "\u5C3A\u5BF8",
        type: "\u7C7B\u578B",
        plain: "\u662F\u5426\u6734\u7D20\u6309\u94AE",
        round: "\u662F\u5426\u5706\u89D2\u6309\u94AE",
        circle: "\u662F\u5426\u5706\u5F62\u6309\u94AE",
        loading: "\u662F\u5426\u52A0\u8F7D\u4E2D\u72B6\u6001",
        disabled: "\u662F\u5426\u7981\u7528\u72B6\u6001"
      }
    },
    elCard: {
      name: "\u5361\u7247",
      props: {
        header: "\u6807\u9898",
        shadow: "\u9634\u5F71\u663E\u793A\u65F6\u673A"
      }
    },
    elCollapse: {
      name: "\u6298\u53E0\u9762\u677F",
      event: {
        change: "\u5207\u6362\u5F53\u524D\u6D3B\u52A8\u9762\u677F\uFF0C\u5728\u624B\u98CE\u7434\u6A21\u5F0F\u4E0B\u5176\u7C7B\u578B\u662Fstring\uFF0C\u5728\u5176\u4ED6\u6A21\u5F0F\u4E0B\u662Farray"
      },
      props: {
        accordion: "\u662F\u5426\u624B\u98CE\u7434\u6A21\u5F0F"
      }
    },
    elCollapseItem: {
      name: "\u9762\u677F",
      props: {
        title: "\u9762\u677F\u6807\u9898",
        name: "\u552F\u4E00\u6807\u5FD7\u7B26",
        disabled: "\u662F\u5426\u7981\u7528"
      }
    },
    elDescriptions: {
      name: "\u63CF\u8FF0\u5217\u8868",
      props: {
        title: "\u6807\u9898\u6587\u672C\uFF0C\u663E\u793A\u5728\u5DE6\u4E0A\u65B9",
        extra: "\u64CD\u4F5C\u533A\u6587\u672C\uFF0C\u663E\u793A\u5728\u53F3\u4E0A\u65B9",
        column: "\u4E00\u884C\u4E2D\u8868\u683C\u7684\u6570\u91CF",
        border: "\u662F\u5426\u5E26\u6709\u8FB9\u6846",
        direction: "\u6392\u5217\u7684\u65B9\u5411",
        size: "\u5217\u8868\u7684\u5C3A\u5BF8"
      }
    },
    elDescriptionsItem: {
      name: "\u63CF\u8FF0\u683C\u5B50",
      props: {
        label: "\u6807\u7B7E\u6587\u672C",
        __child: "\u5185\u5BB9",
        span: "\u5217\u7684\u6570\u91CF",
        width: "\u5217\u7684\u5BBD\u5EA6\uFF0C\u4E0D\u540C\u884C\u76F8\u540C\u5217\u7684\u5BBD\u5EA6\u6309\u6700\u5927\u503C\u8BBE\u5B9A\uFF08\u5982\u65E0 border \uFF0C\u5BBD\u5EA6\u5305\u542B\u6807\u7B7E\u4E0E\u5185\u5BB9",
        minWidth: "\u5217\u7684\u6700\u5C0F\u5BBD\u5EA6",
        align: "\u5217\u7684\u5185\u5BB9\u5BF9\u9F50\u65B9\u5F0F\uFF08\u5982\u65E0 border\uFF0C\u5BF9\u6807\u7B7E\u548C\u5185\u5BB9\u5747\u751F\u6548\uFF09",
        labelAlign: "\u5217\u7684\u6807\u7B7E\u5BF9\u9F50\u65B9\u5F0F\uFF0C\u82E5\u4E0D\u8BBE\u7F6E\u8BE5\u9879\uFF0C\u5219\u4F7F\u7528\u5185\u5BB9\u7684\u5BF9\u9F50\u65B9\u5F0F\uFF08\u5982\u65E0 border\uFF0C\u8BF7\u4F7F\u7528 align \u53C2\u6570\uFF09",
        className: "\u5217\u7684\u5185\u5BB9\u81EA\u5B9A\u4E49\u7C7B\u540D"
      }
    },
    elDivider: {
      name: "\u5206\u5272\u7EBF",
      props: {
        direction: "\u8BBE\u7F6E\u5206\u5272\u7EBF\u65B9\u5411",
        formCreateChild: "\u8BBE\u7F6E\u5206\u5272\u7EBF\u6587\u6848",
        contentPosition: "\u8BBE\u7F6E\u5206\u5272\u7EBF\u6587\u6848\u7684\u4F4D\u7F6E"
      }
    },
    elTabPane: {
      name: "\u9009\u9879\u5361",
      props: {
        label: "\u9009\u9879\u5361\u6807\u9898",
        disabled: "\u662F\u5426\u7981\u7528",
        name: "\u9009\u9879\u5361\u7684\u6807\u8BC6\u7B26",
        lazy: "\u6807\u7B7E\u662F\u5426\u5EF6\u8FDF\u6E32\u67D3"
      }
    },
    elTabs: {
      name: "\u6807\u7B7E\u9875",
      event: {
        tabClick: "tab \u88AB\u9009\u4E2D\u65F6\u89E6\u53D1",
        tabChange: "activeName \u6539\u53D8\u65F6\u89E6\u53D1",
        tabRemove: "\u70B9\u51FB tab \u79FB\u9664\u6309\u94AE\u65F6\u89E6\u53D1",
        tabAdd: "\u70B9\u51FB tab \u65B0\u589E\u6309\u94AE\u65F6\u89E6\u53D1",
        edit: "\u70B9\u51FB tab \u7684\u65B0\u589E\u6216\u79FB\u9664\u6309\u94AE\u540E\u89E6\u53D1"
      },
      props: {
        type: "\u98CE\u683C\u7C7B\u578B",
        closable: "\u6807\u7B7E\u662F\u5426\u53EF\u5173\u95ED",
        tabPosition: "\u9009\u9879\u5361\u6240\u5728\u4F4D\u7F6E",
        stretch: "\u6807\u7B7E\u7684\u5BBD\u5EA6\u662F\u5426\u81EA\u6491\u5F00"
      }
    },
    elTag: {
      name: "\u6807\u7B7E",
      props: {
        formCreateChild: "\u6807\u7B7E\u5185\u5BB9",
        type: "\u6807\u7B7E\u7684\u7C7B\u578B",
        size: "\u6807\u7B7E\u7684\u5C3A\u5BF8",
        effect: "\u6807\u7B7E\u7684\u4E3B\u9898",
        closable: "\u662F\u5426\u53EF\u5173\u95ED",
        disableTransitions: "\u662F\u5426\u7981\u7528\u6E10\u53D8\u52A8\u753B",
        hit: "\u662F\u5426\u6709\u8FB9\u6846\u63CF\u8FB9",
        round: "\u662F\u5426\u4E3A\u5706\u5F62",
        color: "\u80CC\u666F\u8272"
      }
    },
    elTransfer: {
      name: "\u7A7F\u68AD\u6846",
      event: {
        leftCheckChange: "\u5DE6\u4FA7\u5217\u8868\u5143\u7D20\u88AB\u7528\u6237\u9009\u4E2D / \u53D6\u6D88\u9009\u4E2D\u65F6\u89E6\u53D1",
        rightCheckChange: "\u53F3\u4FA7\u5217\u8868\u5143\u7D20\u88AB\u7528\u6237\u9009\u4E2D / \u53D6\u6D88\u9009\u4E2D\u65F6\u89E6\u53D1"
      },
      props: {
        filterable: "\u662F\u5426\u53EF\u641C\u7D22",
        filterPlaceholder: "\u641C\u7D22\u6846\u5360\u4F4D\u7B26",
        targetOrder: "\u53F3\u4FA7\u5217\u8868\u5143\u7D20\u7684\u6392\u5E8F\u7B56\u7565",
        targetOrderInfo: "\u82E5\u4E3A original\uFF0C\u5219\u4FDD\u6301\u4E0E\u6570\u636E\u76F8\u540C\u7684\u987A\u5E8F\uFF1B\u82E5\u4E3A push\uFF0C\u5219\u65B0\u52A0\u5165\u7684\u5143\u7D20\u6392\u5728\u6700\u540E\uFF1B\u82E5\u4E3A unshift\uFF0C\u5219\u65B0\u52A0\u5165\u7684\u5143\u7D20\u6392\u5728\u6700\u524D",
        titles: "\u81EA\u5B9A\u4E49\u5217\u8868\u6807\u9898",
        buttonTexts: "\u81EA\u5B9A\u4E49\u6309\u94AE\u6587\u6848",
        props: "\u6570\u636E\u6E90\u7684\u5B57\u6BB5\u522B\u540D"
      }
    },
    elTreeSelect: {
      name: "\u6811\u5F62\u9009\u62E9",
      event: {
        removeTag: "\u591A\u9009\u6A21\u5F0F\u4E0B\u79FB\u9664tag\u65F6\u89E6\u53D1"
      },
      props: {
        multiple: "\u662F\u5426\u591A\u9009",
        disabled: "\u662F\u5426\u7981\u7528",
        clearable: "\u662F\u5426\u53EF\u4EE5\u6E05\u7A7A\u9009\u9879",
        collapseTags: "\u591A\u9009\u65F6\u662F\u5426\u5C06\u9009\u4E2D\u503C\u6309\u6587\u5B57\u7684\u5F62\u5F0F\u5C55\u793A",
        multipleLimit: "\u591A\u9009\u65F6\u7528\u6237\u6700\u591A\u53EF\u4EE5\u9009\u62E9\u7684\u9879\u76EE\u6570\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",
        placeholder: "\u5360\u4F4D\u7B26",
        props: "\u914D\u7F6E\u9009\u9879",
        renderAfterExpand: "\u662F\u5426\u5728\u7B2C\u4E00\u6B21\u5C55\u5F00\u67D0\u4E2A\u6811\u8282\u70B9\u540E\u624D\u6E32\u67D3\u5176\u5B50\u8282\u70B9",
        defaultExpandAll: "\u662F\u5426\u9ED8\u8BA4\u5C55\u5F00\u6240\u6709\u8282\u70B9",
        expandOnClickNode: "\u662F\u5426\u5728\u70B9\u51FB\u8282\u70B9\u7684\u65F6\u5019\u5C55\u5F00\u6216\u8005\u6536\u7F29\u8282\u70B9",
        checkOnClickNode: "\u662F\u5426\u5728\u70B9\u51FB\u8282\u70B9\u7684\u65F6\u5019\u9009\u4E2D\u8282\u70B9",
        nodeKey: "\u6BCF\u4E2A\u6811\u8282\u70B9\u7528\u6765\u4F5C\u4E3A\u552F\u4E00\u6807\u8BC6\u7684\u5C5E\u6027\uFF0C\u6574\u68F5\u6811\u5E94\u8BE5\u662F\u552F\u4E00\u7684"
      }
    },
    elLink: {
      name: "\u94FE\u63A5",
      props: {
        formCreateChild: "\u5185\u5BB9",
        href: "\u8DF3\u8F6C\u94FE\u63A5",
        type: "\u7C7B\u578B",
        underline: "\u662F\u5426\u663E\u793A\u4E0B\u5212\u7EBF",
        disabled: "\u662F\u5426\u7981\u7528",
        target: "\u6253\u5F00\u65B9\u5F0F"
      }
    },
    elWatermark: {
      name: "\u6C34\u5370",
      props: {
        content: "\u6C34\u5370\u6587\u672C\u5185\u5BB9",
        image: "\u6C34\u5370\u56FE\u7247\uFF0C\u5EFA\u8BAE\u4F7F\u7528 2x \u6216 3x \u56FE\u50CF",
        width: "\u6C34\u5370\u7684\u5BBD\u5EA6\uFF0C content \u7684\u9ED8\u8BA4\u503C\u662F\u5B83\u81EA\u5DF1\u7684\u5BBD\u5EA6",
        height: "\u6C34\u5370\u7684\u9AD8\u5EA6\uFF0C content \u7684\u9ED8\u8BA4\u503C\u662F\u5B83\u81EA\u5DF1\u7684\u9AD8\u5EA6",
        rotate: "\u6C34\u5370\u7684\u65CB\u8F6C\u89D2\u5EA6, \u5355\u4F4D \xB0",
        zIndex: "\u6C34\u5370\u5143\u7D20\u7684z-index\u503C",
        gap: "\u6C34\u5370\u4E4B\u95F4\u7684\u95F4\u8DDD"
      }
    },
    elTooltip: {
      name: "\u6587\u5B57\u63D0\u793A",
      props: {
        content: "\u663E\u793A\u7684\u5185\u5BB9",
        disabled: "\u662F\u5426\u7981\u7528",
        rawContent: "\u5185\u5BB9\u662F\u5426\u4F5C\u4E3A HTML \u5B57\u7B26\u4E32\u5904\u7406",
        enterable: "\u9F20\u6807\u662F\u5426\u53EF\u8FDB\u5165\u5230 Tooltip \u4E2D",
        effect: "\u4E3B\u9898",
        placement: "Tooltip \u7EC4\u4EF6\u51FA\u73B0\u7684\u4F4D\u7F6E",
        trigger: "\u5982\u4F55\u89E6\u53D1 Tooltip",
        offset: "\u51FA\u73B0\u4F4D\u7F6E\u7684\u504F\u79FB\u91CF",
        showAfter: "\u5728\u89E6\u53D1\u540E\u591A\u4E45\u663E\u793A\u5185\u5BB9\uFF0C\u5355\u4F4D\u6BEB\u79D2",
        hideAfter: "\u5EF6\u8FDF\u5173\u95ED\uFF0C\u5355\u4F4D\u6BEB\u79D2",
        autoClose: "\u51FA\u73B0\u540E\u81EA\u52A8\u9690\u85CF\u5EF6\u65F6\uFF0C\u5355\u4F4D\u6BEB\u79D2"
      }
    },
    elImage: {
      name: "\u56FE\u7247",
      props: {
        src: "\u56FE\u7247\u94FE\u63A5",
        previewSrcList: "\u9884\u89C8\u56FE\u7247\u5217\u8868"
      }
    },
    elAvatar: {
      name: "\u5934\u50CF\u6846",
      props: {
        src: "\u56FE\u7247\u94FE\u63A5",
        shape: "\u5F62\u72B6",
        size: "\u5C3A\u5BF8"
      }
    },
    elMention: {
      name: "\u63D0\u53CA",
      event: {
        search: "\u6309\u4E0B\u89E6\u53D1\u5B57\u6BB5\u65F6\u89E6\u53D1",
        select: "\u5F53\u7528\u6237\u9009\u62E9\u9009\u9879\u65F6\u89E6\u53D1"
      },
      props: {
        type: "\u7C7B\u578B",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C",
        clearable: "\u662F\u5426\u663E\u793A\u6E05\u9664\u6309\u94AE",
        disabled: "\u662F\u5426\u7981\u7528",
        whole: "\u5F53\u9000\u683C\u952E\u88AB\u6309\u4E0B\u505A\u5220\u9664\u64CD\u4F5C\u65F6\uFF0C\u662F\u5426\u5C06\u63D0\u53CA\u90E8\u5206\u4F5C\u4E3A\u6574\u4F53\u5220\u9664",
        checkIsWhole: "\u5F53\u9000\u683C\u952E\u88AB\u6309\u4E0B\u505A\u5220\u9664\u64CD\u4F5C\u65F6\uFF0C\u68C0\u67E5\u662F\u5426\u5C06\u63D0\u53CA\u90E8\u5206\u4F5C\u4E3A\u6574\u4F53\u5220\u9664",
        filterOption: "\u7B5B\u9009\u5668\u9009\u9879\u903B\u8F91"
      }
    },
    elSegmented: {
      name: "\u5206\u6BB5\u63A7\u5236\u5668",
      props: {
        size: "\u5C3A\u5BF8",
        block: "\u6491\u6EE1\u7236\u5143\u7D20\u5BBD\u5EA6",
        disabled: "\u662F\u5426\u7981\u7528"
      }
    },
    elFormItem: {
      name: "\u8868\u5355\u9879",
      props: {
        label: "\u6807\u7B7E\u6587\u672C",
        labelWidth: "\u6807\u7B7E\u5BBD\u5EA6",
        size: "\u5C3A\u5BF8"
      }
    },
    elStatistic: {
      name: "\u7EDF\u8BA1\u680F",
      props: {
        value: "\u6570\u503C",
        title: "\u6807\u9898",
        prefix: "\u6570\u503C\u7684\u524D\u7F00",
        suffix: "\u6570\u503C\u7684\u540E\u7F00"
      }
    },
    fcTitle: {
      name: "\u6807\u9898",
      props: {
        title: "\u6807\u9898",
        size: "\u5C3A\u5BF8",
        align: "\u5BF9\u9F50\u65B9\u5F0F"
      }
    },
    fcId: {
      name: "\u552F\u4E00\u503C",
      props: {
        prefix: "\u524D\u7F00"
      }
    },
    fcCell: {
      name: "\u683C\u5B50"
    },
    fcEditor: {
      name: "\u5BCC\u6587\u672C\u6846",
      props: {
        disabled: "\u662F\u5426\u7981\u7528"
      }
    },
    fcFlex: {
      name: "\u76D2\u5B50\u5E03\u5C40"
    },
    fcFlex2: {
      name: "\u76D2\u5B50\u5E03\u5C40",
      horizontal: "\u6C34\u5E73\u5C45\u4E2D",
      vertical: "\u5782\u76F4\u5C45\u4E2D",
      left: "\u5DE6\u5BF9\u9F50",
      right: "\u53F3\u5BF9\u9F50",
      reset: "\u91CD\u7F6E\u5E03\u5C40"
    },
    fcRow: {
      name: "\u6805\u683C\u5E03\u5C40",
      props: {
        gutter: "\u6805\u683C\u95F4\u9694",
        type: "flex\u5E03\u5C40\u6A21\u5F0F",
        justify: "flex\u5E03\u5C40\u4E0B\u7684\u6C34\u5E73\u6392\u5217\u65B9\u5F0F",
        align: "flex\u5E03\u5C40\u4E0B\u7684\u5782\u76F4\u6392\u5217\u65B9\u5F0F"
      }
    },
    fcDialog: {
      name: "\u5F39\u51FA\u6846",
      props: {
        title: "\u6807\u9898",
        width: "\u5BF9\u8BDD\u6846\u7684\u5BBD\u5EA6",
        fullscreen: "\u662F\u5426\u4E3A\u5168\u5C4F",
        modal: "\u662F\u5426\u9700\u8981\u906E\u7F69\u5C42",
        autoClose: "\u63D0\u4EA4\u8868\u5355\u540E\u81EA\u52A8\u5173\u95ED\u5F39\u51FA\u6846",
        footer: "\u662F\u5426\u663E\u793A\u64CD\u4F5C\u6309\u94AE",
        beforeClose: "\u5173\u95ED\u524D\u7684\u56DE\u8C03"
      }
    },
    fcDrawer: {
      name: "\u62BD\u5C49",
      props: {
        title: "\u6807\u9898",
        size: "\u5BF9\u8BDD\u6846\u7684\u5BBD\u5EA6",
        direction: "\u6253\u5F00\u7684\u65B9\u5411",
        modal: "\u662F\u5426\u9700\u8981\u906E\u7F69\u5C42",
        autoClose: "\u63D0\u4EA4\u8868\u5355\u540E\u81EA\u52A8\u5173\u95ED\u5F39\u51FA\u6846",
        footer: "\u662F\u5426\u663E\u793A\u64CD\u4F5C\u6309\u94AE",
        beforeClose: "\u5173\u95ED\u524D\u7684\u56DE\u8C03"
      },
      directionType: {
        ltr: "\u5DE6\u4FA7",
        rtl: "\u53F3\u4FA7"
      }
    },
    fcTable: {
      name: "\u8868\u683C\u5E03\u5C40",
      props: {
        border: "\u662F\u5426\u663E\u793A\u8FB9\u6846",
        borderColor: "\u8FB9\u6846\u989C\u8272",
        borderWidth: "\u8FB9\u6846\u5BBD\u5EA6"
      }
    },
    fcTableGrid: {
      name: "\u683C\u5B50"
    },
    fcValue: {
      name: "\u8BA1\u7B97\u516C\u5F0F",
      empty: "\u5728\u53F3\u4FA7\u914D\u7F6E\u4E2D\u8BBE\u7F6E\u8BA1\u7B97\u516C\u5F0F"
    },
    fcSlot: {
      name: "\u63D2\u69FD\u533A\u57DF",
      empty: "\u901A\u8FC7\u5B9A\u4E49\u63D2\u69FD&nbsp;{tag}&nbsp;\u586B\u5145\u533A\u57DF",
      props: {
        name: "\u63D2\u69FD\u540D\u79F0"
      }
    },
    fcJson: {
      name: "\u52A8\u6001\u533A\u57DF",
      empty: "\u901A\u8FC7\u52A0\u8F7D&nbsp;{tag}&nbsp;\u89C4\u5219\u586B\u5145\u533A\u57DF",
      props: {
        _loadType: "JSON \u89C4\u5219",
        type: "\u7C7B\u578B"
      }
    },
    lineChart: {
      name: "\u6298\u7EBF\u56FE",
      data: "\u56FE\u8868\u6570\u636E",
      pieTypeOpt: {
        pie: "\u5706\u5F62",
        doughnut: "\u73AF\u5F62",
        "half-doughnut": "\u534A\u73AF\u5F62"
      },
      funnelSortOpt: {
        descending: "\u5012\u5E8F",
        ascending: "\u6B63\u5E8F"
      },
      props: {
        title: "\u56FE\u8868\u7684\u540D\u79F0",
        subtitle: "\u56FE\u8868\u7684\u7B80\u4ECB",
        valueFormat: "\u683C\u5F0F\u5316\u6570\u503C\u663E\u793A",
        stack: "\u591A\u5217\u6570\u636E\u65F6\u662F\u5426\u5806\u53E0",
        smooth: "\u7EBF\u6761\u662F\u5426\u5E73\u6ED1\u8FC7\u6E21",
        showLegend: "\u662F\u5426\u663E\u793A\u6807\u8BB0",
        showSeriesLabel: "\u662F\u5426\u663E\u793A\u6570\u503C",
        barBackgroundColor: "\u67F1\u72B6\u7684\u80CC\u666F\u8272",
        funnelSort: "\u6392\u5E8F\u65B9\u5F0F",
        pieType: "\u5F62\u72B6",
        min: "\u6700\u5C0F\u503C",
        max: "\u6700\u5927\u503C",
        value: "\u6570\u503C",
        indicator: "\u6307\u793A\u5668",
        loadOptions: "\u521D\u59CB\u5316"
      }
    },
    areaChart: {
      name: "\u4F53\u79EF\u56FE"
    },
    barChart: {
      name: "\u67F1\u72B6\u56FE"
    },
    customChart: {
      name: "\u81EA\u5B9A\u4E49\u56FE\u8868"
    },
    funnelChart: {
      name: "\u6F0F\u6597\u56FE"
    },
    gaugeChart: {
      name: "\u4EEA\u8868\u76D8"
    },
    pieChart: {
      name: "\u997C\u56FE"
    },
    radarChart: {
      name: "\u96F7\u8FBE\u56FE"
    },
    scatterChart: {
      name: "\u6563\u70B9\u56FE"
    },
    stripeChart: {
      name: "\u6761\u5F62\u56FE"
    },
    fcInlineForm: {
      name: "\u884C\u5185\u5E03\u5C40"
    },
    group: {
      name: "\u5B50\u8868\u5355",
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        syncDisabled: "\u662F\u5426\u4E0E\u5B50\u8868\u5355\u5F3A\u5236\u540C\u6B65\u7981\u7528\u72B6\u6001",
        expand: "\u8BBE\u7F6E\u9ED8\u8BA4\u5C55\u5F00\u51E0\u9879",
        button: "\u662F\u5426\u663E\u793A\u64CD\u4F5C\u6309\u94AE",
        sortBtn: "\u662F\u5426\u663E\u793A\u6392\u5E8F\u6309\u94AE",
        min: "\u8BBE\u7F6E\u6700\u5C0F\u6DFB\u52A0\u51E0\u9879",
        max: "\u8BBE\u7F6E\u6700\u591A\u6DFB\u52A0\u51E0\u9879"
      }
    },
    html: {
      name: "HTML",
      props: {
        formCreateChild: "\u5185\u5BB9"
      }
    },
    input: {
      name: "\u8F93\u5165\u6846",
      event: {
        change: "\u5F53\u503C\u6539\u53D8\u65F6\uFF0C\u5F53\u7EC4\u4EF6\u5931\u53BB\u7126\u70B9\u6216\u7528\u6237\u6309Enter\u65F6\u89E6\u53D1"
      },
      props: {
        type: "\u7C7B\u578B",
        maxlength: "\u6700\u5927\u8F93\u5165\u957F\u5EA6",
        minlength: "\u6700\u5C0F\u8F93\u5165\u957F\u5EA6",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C",
        clearable: "\u662F\u5426\u663E\u793A\u6E05\u9664\u6309\u94AE",
        disabled: "\u662F\u5426\u7981\u7528",
        readonly: "\u662F\u5426\u53EA\u8BFB"
      }
    },
    inputNumber: {
      name: "\u6570\u5B57\u8F93\u5165\u6846",
      props: {
        precision: "\u6570\u503C\u7CBE\u5EA6",
        min: "\u8BBE\u7F6E\u8BA1\u6570\u5668\u5141\u8BB8\u7684\u6700\u5C0F\u503C",
        max: "\u8BBE\u7F6E\u8BA1\u6570\u5668\u5141\u8BB8\u7684\u6700\u5927\u503C",
        step: "\u8BA1\u6570\u5668\u6B65\u957F",
        stepStrictly: "\u662F\u5426\u53EA\u80FD\u8F93\u5165 step \u7684\u500D\u6570",
        disabled: "\u662F\u5426\u7981\u7528\u8BA1\u6570\u5668",
        controls: "\u662F\u5426\u4F7F\u7528\u63A7\u5236\u6309\u94AE",
        controlsPosition: "\u63A7\u5236\u6309\u94AE\u4F4D\u7F6E",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C"
      }
    },
    password: {
      name: "\u5BC6\u7801\u8F93\u5165\u6846",
      event: {
        change: "\u5F53\u503C\u6539\u53D8\u65F6\uFF0C\u5F53\u7EC4\u4EF6\u5931\u53BB\u7126\u70B9\u6216\u7528\u6237\u6309Enter\u65F6\u89E6\u53D1"
      },
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        readonly: "\u662F\u5426\u53EA\u8BFB",
        maxlength: "\u6700\u5927\u8F93\u5165\u957F\u5EA6",
        minlength: "\u6700\u5C0F\u8F93\u5165\u957F\u5EA6",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C",
        clearable: "\u662F\u5426\u663E\u793A\u6E05\u9664\u6309\u94AE"
      }
    },
    radio: {
      name: "\u5355\u9009\u6846",
      props: {
        input: "\u662F\u5426\u53EF\u4EE5\u586B\u5199",
        disabled: "\u662F\u5426\u7981\u7528",
        type: "\u6309\u94AE\u5F62\u5F0F",
        textColor: "\u6309\u94AE\u5F62\u5F0F\u6FC0\u6D3B\u65F6\u7684\u6587\u672C\u989C\u8272",
        fill: "\u6309\u94AE\u5F62\u5F0F\u6FC0\u6D3B\u65F6\u7684\u586B\u5145\u8272\u548C\u8FB9\u6846\u8272"
      }
    },
    rate: {
      name: "\u8BC4\u5206",
      props: {
        max: "\u6700\u5927\u5206\u503C",
        disabled: "\u662F\u5426\u7981\u7528",
        allowHalf: "\u662F\u5426\u5141\u8BB8\u534A\u9009",
        voidColor: "\u672A\u9009\u4E2D\u65F6\u56FE\u6807\u7684\u989C\u8272",
        disabledVoidColor: "\u53EA\u8BFB\u65F6\u672A\u9009\u4E2D\u65F6\u56FE\u6807\u7684\u989C\u8272",
        voidIconClass: "\u672A\u9009\u4E2D\u65F6\u56FE\u6807\u7684\u7C7B\u540D",
        disabledVoidIconClass: "\u53EA\u8BFB\u65F6\u672A\u9009\u4E2D\u65F6\u56FE\u6807\u7684\u7C7B\u540D",
        showScore: "\u662F\u5426\u663E\u793A\u5F53\u524D\u5206\u6570",
        textColor: "\u8F85\u52A9\u6587\u5B57\u7684\u989C\u8272",
        scoreTemplate: "\u5206\u6570\u663E\u793A\u6A21\u677F"
      }
    },
    select: {
      name: "\u9009\u62E9\u5668",
      event: {
        removeTag: "\u591A\u9009\u6A21\u5F0F\u4E0B\u79FB\u9664tag\u65F6\u89E6\u53D1"
      },
      props: {
        multiple: "\u662F\u5426\u591A\u9009",
        disabled: "\u662F\u5426\u7981\u7528",
        clearable: "\u662F\u5426\u53EF\u4EE5\u6E05\u7A7A\u9009\u9879",
        collapseTags: "\u591A\u9009\u65F6\u662F\u5426\u5C06\u9009\u4E2D\u503C\u6309\u6587\u5B57\u7684\u5F62\u5F0F\u5C55\u793A",
        multipleLimit: "\u591A\u9009\u65F6\u7528\u6237\u6700\u591A\u53EF\u4EE5\u9009\u62E9\u7684\u9879\u76EE\u6570\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",
        placeholder: "\u5360\u4F4D\u7B26",
        filterable: "\u662F\u5426\u53EF\u641C\u7D22",
        allowCreate: "\u662F\u5426\u5141\u8BB8\u7528\u6237\u521B\u5EFA\u65B0\u6761\u76EE",
        noMatchText: "\u641C\u7D22\u6761\u4EF6\u65E0\u5339\u914D\u65F6\u663E\u793A\u7684\u6587\u5B57",
        noDataText: "\u9009\u9879\u4E3A\u7A7A\u65F6\u663E\u793A\u7684\u6587\u5B57",
        reserveKeyword: "\u591A\u9009\u4E14\u53EF\u641C\u7D22\u65F6\uFF0C\u662F\u5426\u5728\u9009\u4E2D\u4E00\u4E2A\u9009\u9879\u540E\u4FDD\u7559\u5F53\u524D\u7684\u641C\u7D22\u5173\u952E\u8BCD",
        defaultFirstOption: "\u5728\u8F93\u5165\u6846\u6309\u4E0B\u56DE\u8F66\uFF0C\u9009\u62E9\u7B2C\u4E00\u4E2A\u5339\u914D\u9879",
        remote: "\u5176\u4E2D\u7684\u9009\u9879\u662F\u5426\u4ECE\u670D\u52A1\u5668\u8FDC\u7A0B\u52A0\u8F7D",
        remoteMethod: "\u81EA\u5B9A\u4E49\u8FDC\u7A0B\u641C\u7D22\u65B9\u6CD5"
      }
    },
    slider: {
      name: "\u6ED1\u5757",
      props: {
        min: "\u6700\u5C0F\u503C",
        max: "\u6700\u5927\u503C",
        disabled: "\u662F\u5426\u7981\u7528",
        step: "\u6B65\u957F",
        showInput: "\u662F\u5426\u663E\u793A\u8F93\u5165\u6846\uFF0C\u4EC5\u5728\u975E\u8303\u56F4\u9009\u62E9\u65F6\u6709\u6548",
        showInputControls: "\u5728\u663E\u793A\u8F93\u5165\u6846\u7684\u60C5\u51B5\u4E0B\uFF0C\u662F\u5426\u663E\u793A\u8F93\u5165\u6846\u7684\u63A7\u5236\u6309\u94AE",
        showStops: "\u662F\u5426\u663E\u793A\u95F4\u65AD\u70B9",
        range: "\u662F\u5426\u4E3A\u8303\u56F4\u9009\u62E9",
        vertical: "\u662F\u5426\u7AD6\u5411\u6A21\u5F0F",
        height: "Slider \u9AD8\u5EA6\uFF0C\u7AD6\u5411\u6A21\u5F0F\u65F6\u5FC5\u586B"
      }
    },
    space: {
      name: "\u95F4\u8DDD"
    },
    stepForm: {
      name: "\u5206\u6B65\u8868\u5355",
      event: {
        next: "\u70B9\u51FB\u4E0B\u4E00\u6B65\u6309\u94AE\u65F6\u89E6\u53D1"
      },
      props: {
        autoValidate: "\u8FDB\u5165\u4E0B\u4E00\u6B65\u524D\u81EA\u52A8\u9A8C\u8BC1\u8868\u5355",
        "stepsProps>alignCenter": "\u8FDB\u884C\u5C45\u4E2D\u5BF9\u9F50",
        "stepsProps>simple": "\u662F\u5426\u5E94\u7528\u7B80\u6D01\u98CE\u683C"
      }
    },
    stepFormItem: {
      name: "\u6B65\u9AA4\u6761",
      props: {
        title: "\u6807\u9898",
        description: "\u63CF\u8FF0\u6587\u6848"
      }
    },
    subForm: {
      name: "\u5206\u7EC4",
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        syncDisabled: "\u662F\u5426\u4E0E\u5B50\u8868\u5355\u5F3A\u5236\u540C\u6B65\u7981\u7528\u72B6\u6001"
      }
    },
    switch: {
      name: "\u5F00\u5173",
      slots: {
        "active-action": "\u6253\u5F00\u65F6\u7684\u5185\u5BB9",
        "inactive-action": "\u5173\u95ED\u65F6\u7684\u5185\u5BB9"
      },
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        width: "\u5BBD\u5EA6\uFF08px\uFF09",
        activeText: "\u6253\u5F00\u65F6\u7684\u6587\u5B57\u63CF\u8FF0",
        inactiveText: "\u5173\u95ED\u65F6\u7684\u6587\u5B57\u63CF\u8FF0",
        activeValue: "\u6253\u5F00\u65F6\u7684\u503C",
        inactiveValue: "\u5173\u95ED\u65F6\u7684\u503C",
        activeColor: "\u6253\u5F00\u65F6\u7684\u80CC\u666F\u8272",
        inactiveColor: "\u5173\u95ED\u65F6\u7684\u80CC\u666F\u8272"
      }
    },
    tableForm: {
      name: "\u8868\u683C\u8868\u5355",
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        filterEmptyColumn: "\u662F\u5426\u8FC7\u6EE4\u7A7A\u884C\u7684\u6570\u636E",
        max: "\u6700\u591A\u6DFB\u52A0\u51E0\u884C\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236"
      }
    },
    nestedTableForm: {
      name: "\u5D4C\u5957\u8868\u5355",
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        max: "\u6700\u591A\u6DFB\u52A0\u51E0\u884C\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",
        nestedMax: "\u5B50\u8868\u5355\u6700\u591A\u6DFB\u52A0\u51E0\u884C\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236"
      }
    },
    infiniteTableForm: {
      name: "\u65E0\u9650\u7EA7\u8868\u5355",
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        childrenField: "\u8BBE\u7F6E\u5B50\u7EA7\u7684\u5B57\u6BB5\u540D",
        max: "\u6700\u591A\u6DFB\u52A0\u51E0\u884C\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236",
        layerMax: "\u6700\u591A\u6DFB\u52A0\u51E0\u5C42\uFF0C\u4E3A 0 \u5219\u4E0D\u9650\u5236"
      }
    },
    nestedSubTableForm: {
      name: "\u5B50\u8868\u683C\u8868\u5355"
    },
    tableFormColumn: {
      name: "\u8868\u683C\u683C\u5B50",
      label: "\u81EA\u5B9A\u4E49\u540D\u79F0",
      props: {
        label: "\u6807\u9898",
        width: "\u5BBD\u5EA6",
        color: "\u989C\u8272",
        required: "\u662F\u5426\u663E\u793A\u5FC5\u586B\u661F\u53F7"
      }
    },
    dataTable: {
      name: "\u6570\u636E\u8868\u683C",
      handle: "\u7981\u7528\u903B\u8F91",
      click: "\u70B9\u51FB\u4E8B\u4EF6",
      filter: "\u7B5B\u9009",
      event: {
        cellMouseEnter: "\u5F53\u5355\u5143\u683C hover \u8FDB\u5165\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        cellMouseLeave: "\u5F53\u5355\u5143\u683C hover \u9000\u51FA\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        handleClick: "\u70B9\u51FB\u64CD\u4F5C\u6309\u94AE\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        rowClick: "\u5F53\u67D0\u4E00\u884C\u88AB\u70B9\u51FB\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        rowDblclick: "\u5F53\u67D0\u4E00\u884C\u88AB\u53CC\u51FB\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        headerClick: "\u5F53\u67D0\u4E00\u5217\u7684\u8868\u5934\u88AB\u70B9\u51FB\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        filterChange: "\u7B5B\u9009\u6761\u4EF6\u53D8\u5316\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        expandChange: "\u5F53\u7528\u6237\u5BF9\u67D0\u4E00\u884C\u5C55\u5F00\u6216\u8005\u5173\u95ED\u7684\u65F6\u5019\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        sortChange: "\u5F53\u8868\u683C\u7684\u6392\u5E8F\u6761\u4EF6\u53D1\u751F\u53D8\u5316\u7684\u65F6\u5019\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        selectionChange: "\u5F53\u9009\u62E9\u9879\u53D1\u751F\u53D8\u5316\u65F6\u89E6\u53D1"
      },
      button: {
        title: "\u64CD\u4F5C\u6309\u94AE",
        btn: "\u914D\u7F6E\u64CD\u4F5C\u6309\u94AE",
        link: "\u6587\u5B57",
        round: "\u5706\u89D2",
        plain: "\u6734\u7D20",
        disabled: "\u7981\u7528"
      },
      column: {
        title: "\u5217\u8868\u7BA1\u7406",
        btn: "\u914D\u7F6E\u8868\u683C\u5217\u8868",
        prop: "\u5B57\u6BB5\u540D\u79F0",
        sort: "\u6392\u5E8F"
      },
      format: {
        default: "\u5E38\u89C4",
        tag: "\u6807\u7B7E",
        image: "\u56FE\u7247",
        custom: "\u81EA\u5B9A\u4E49"
      },
      fixed: {
        default: "\u5E38\u89C4",
        left: "\u5DE6\u60AC\u6D6E",
        right: "\u53F3\u60AC\u6D6E"
      },
      sortable: {
        disabled: "\u4E0D\u6392\u5E8F",
        default: "\u5E38\u89C4\u6392\u5E8F",
        custom: "\u63A5\u53E3\u6392\u5E8F"
      },
      props: {
        _optionType: "\u8868\u683C\u6570\u636E",
        "page>totalField": "\u63A5\u53E3\u54CD\u5E94\u6570\u636E\u4E2D\u603B\u6761\u76EE\u6570\u7684\u5B57\u6BB5\u540D\u79F0",
        "page>dataField": "\u63A5\u53E3\u54CD\u5E94\u6570\u636E\u4E2D\u5217\u8868\u6570\u636E\u7684\u5B57\u6BB5\u540D\u79F0",
        "page>orderField": "\u8BF7\u6C42\u63A5\u53E3\u65F6\u6392\u5E8F\u7684\u53C2\u6570\u540D\u79F0",
        "page>orderByField": "\u8BF7\u6C42\u63A5\u53E3\u65F6\u6392\u5E8F\u65B9\u5F0F\u7684\u53C2\u6570\u540D\u79F0",
        "page>pageField": "\u8BF7\u6C42\u63A5\u53E3\u65F6\u9875\u7801\u7684\u53C2\u6570\u540D\u79F0(\u5206\u9875)",
        "page>pageSizeField": "\u8BF7\u6C42\u63A5\u53E3\u65F6\u6761\u76EE\u6570\u7684\u53C2\u6570\u540D\u79F0(\u5206\u9875)",
        column: "\u8868\u683C\u7BA1\u7406",
        showSummary: "\u662F\u5426\u5728\u8868\u5C3E\u663E\u793A\u5408\u8BA1\u884C",
        selection: "\u662F\u5426\u663E\u793A\u591A\u9009\u6846",
        size: "\u5C3A\u5BF8",
        rowKey: "\u884C\u6570\u636E\u7684Key",
        emptyText: "\u7A7A\u6570\u636E\u65F6\u663E\u793A\u7684\u6587\u672C\u5185\u5BB9",
        height: "\u9AD8\u5EA6",
        index: "\u663E\u793A\u884C\u53F7",
        stripe: "\u663E\u793A\u6591\u9A6C\u7EB9",
        border: "\u8FB9\u6846",
        defaultExpandAll: "\u9ED8\u8BA4\u5C55\u5F00\u6240\u6709\u884C",
        button: "\u64CD\u4F5C\u6309\u94AE",
        page: "\u5206\u9875",
        "button>column": "\u6309\u94AE\u7BA1\u7406",
        "button>label": "\u64CD\u4F5C",
        "button>fixed": "\u4F4D\u7F6E",
        "button>width": "\u5217\u7684\u5BBD\u5EA6",
        "page>position": "\u4F4D\u7F6E",
        "page>props>pageSize": "\u6BCF\u9875\u663E\u793A\u6761\u76EE\u4E2A\u6570",
        "page>props>small": "\u4F7F\u7528\u5C0F\u578B\u5206\u9875\u6837\u5F0F",
        "page>props>background": "\u4E3A\u5206\u9875\u6309\u94AE\u6DFB\u52A0\u80CC\u666F\u8272"
      },
      requiredName: "\u8BF7\u8F93\u5165\u6309\u94AE\u540D\u79F0",
      requiredKey: "\u8BF7\u8F93\u5165\u6309\u94AE\u6807\u8BC6",
      requiredLabel: "\u8BF7\u8F93\u5165\u6807\u9898",
      requiredRender: "\u8BF7\u8F93\u5165\u6E32\u67D3\u51FD\u6570"
    },
    text: {
      name: "\u6587\u5B57",
      props: {
        formCreateChild: "\u5185\u5BB9"
      }
    },
    textarea: {
      name: "\u591A\u884C\u8F93\u5165\u6846",
      event: {
        change: "\u5F53\u503C\u6539\u53D8\u65F6\uFF0C\u5F53\u7EC4\u4EF6\u5931\u53BB\u7126\u70B9\u6216\u7528\u6237\u6309Enter\u65F6\u89E6\u53D1"
      },
      props: {
        disabled: "\u662F\u5426\u7981\u7528",
        readonly: "\u662F\u5426\u53EA\u8BFB",
        maxlength: "\u6700\u5927\u8F93\u5165\u957F\u5EA6",
        minlength: "\u6700\u5C0F\u8F93\u5165\u957F\u5EA6",
        showWordLimit: "\u662F\u5426\u663E\u793A\u7EDF\u8BA1\u5B57\u6570",
        placeholder: "\u8F93\u5165\u6846\u5360\u4F4D\u6587\u672C",
        rows: "\u8F93\u5165\u6846\u884C\u6570",
        autosize: "\u9AD8\u5EA6\u662F\u5426\u81EA\u9002\u5E94"
      }
    },
    timePicker: {
      name: "\u65F6\u95F4",
      "HH:mm:ss": "\u65F6:\u5206:\u79D2",
      "HH:mm": "\u65F6:\u5206",
      props: {
        __format: "\u8F93\u5165\u6846\u4E2D\u7684\u683C\u5F0F",
        pickerOptions: "\u5F53\u524D\u65F6\u95F4\u65E5\u671F\u9009\u62E9\u5668\u7279\u6709\u7684\u9009\u9879",
        readonly: "\u5B8C\u5168\u53EA\u8BFB",
        disabled: "\u7981\u7528",
        editable: "\u6587\u672C\u6846\u53EF\u8F93\u5165",
        clearable: "\u662F\u5426\u663E\u793A\u6E05\u9664\u6309\u94AE",
        placeholder: "\u975E\u8303\u56F4\u9009\u62E9\u65F6\u7684\u5360\u4F4D\u5185\u5BB9",
        startPlaceholder: "\u8303\u56F4\u9009\u62E9\u65F6\u5F00\u59CB\u65E5\u671F\u7684\u5360\u4F4D\u5185\u5BB9",
        endPlaceholder: "\u8303\u56F4\u9009\u62E9\u65F6\u7ED3\u675F\u65E5\u671F\u7684\u5360\u4F4D\u5185\u5BB9",
        isRange: "\u662F\u5426\u4E3A\u65F6\u95F4\u8303\u56F4\u9009\u62E9",
        arrowControl: "\u662F\u5426\u4F7F\u7528\u7BAD\u5934\u8FDB\u884C\u65F6\u95F4\u9009\u62E9",
        align: "\u5BF9\u9F50\u65B9\u5F0F"
      }
    },
    tree: {
      name: "\u6811\u5F62\u63A7\u4EF6",
      event: {
        nodeClick: "\u5F53\u8282\u70B9\u88AB\u70B9\u51FB\u7684\u65F6\u5019\u89E6\u53D1",
        nodeContextmenu: "\u5F53\u67D0\u4E00\u8282\u70B9\u88AB\u9F20\u6807\u53F3\u952E\u70B9\u51FB\u65F6\u4F1A\u89E6\u53D1\u8BE5\u4E8B\u4EF6",
        checkChange: "\u5F53\u590D\u9009\u6846\u88AB\u70B9\u51FB\u7684\u65F6\u5019\u89E6\u53D1",
        check: "\u70B9\u51FB\u8282\u70B9\u590D\u9009\u6846\u4E4B\u540E\u89E6\u53D1",
        currentChange: "\u5F53\u524D\u9009\u4E2D\u8282\u70B9\u53D8\u5316\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeExpand: "\u8282\u70B9\u88AB\u5C55\u5F00\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeCollapse: "\u8282\u70B9\u88AB\u5173\u95ED\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeDragStart: "\u8282\u70B9\u5F00\u59CB\u62D6\u62FD\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeDragEnter: "\u62D6\u62FD\u8FDB\u5165\u5176\u4ED6\u8282\u70B9\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeDragLeave: "\u62D6\u62FD\u79BB\u5F00\u67D0\u4E2A\u8282\u70B9\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeDragOver: "\u5728\u62D6\u62FD\u8282\u70B9\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6\uFF08\u7C7B\u4F3C\u6D4F\u89C8\u5668\u7684 mouseover \u4E8B\u4EF6\uFF09",
        nodeDragEnd: "\u62D6\u62FD\u7ED3\u675F\u65F6\uFF08\u53EF\u80FD\u672A\u6210\u529F\uFF09\u89E6\u53D1\u7684\u4E8B\u4EF6",
        nodeDrop: "\u62D6\u62FD\u6210\u529F\u5B8C\u6210\u65F6\u89E6\u53D1\u7684\u4E8B\u4EF6"
      },
      props: {
        emptyText: "\u5185\u5BB9\u4E3A\u7A7A\u7684\u65F6\u5019\u5C55\u793A\u7684\u6587\u672C",
        props: "\u914D\u7F6E\u9009\u9879",
        renderAfterExpand: "\u662F\u5426\u5728\u7B2C\u4E00\u6B21\u5C55\u5F00\u67D0\u4E2A\u6811\u8282\u70B9\u540E\u624D\u6E32\u67D3\u5176\u5B50\u8282\u70B9",
        defaultExpandAll: "\u662F\u5426\u9ED8\u8BA4\u5C55\u5F00\u6240\u6709\u8282\u70B9",
        expandOnClickNode: "\u662F\u5426\u5728\u70B9\u51FB\u8282\u70B9\u7684\u65F6\u5019\u5C55\u5F00\u6216\u8005\u6536\u7F29\u8282\u70B9\uFF0C\u5982\u679C\u4E3A false\uFF0C\u5219\u53EA\u6709\u70B9\u7BAD\u5934\u56FE\u6807\u7684\u65F6\u5019\u624D\u4F1A\u5C55\u5F00\u6216\u8005\u6536\u7F29\u8282\u70B9\u3002",
        checkOnClickNode: "\u662F\u5426\u5728\u70B9\u51FB\u8282\u70B9\u7684\u65F6\u5019\u9009\u4E2D\u8282\u70B9",
        autoExpandParent: "\u5C55\u5F00\u5B50\u8282\u70B9\u7684\u65F6\u5019\u662F\u5426\u81EA\u52A8\u5C55\u5F00\u7236\u8282\u70B9",
        checkStrictly: "\u5728\u663E\u793A\u590D\u9009\u6846\u7684\u60C5\u51B5\u4E0B\uFF0C\u662F\u5426\u4E25\u683C\u7684\u9075\u5FAA\u7236\u5B50\u4E0D\u4E92\u76F8\u5173\u8054\u7684\u505A\u6CD5",
        accordion: "\u662F\u5426\u6BCF\u6B21\u53EA\u6253\u5F00\u4E00\u4E2A\u540C\u7EA7\u6811\u8282\u70B9\u5C55\u5F00",
        indent: "\u76F8\u90BB\u7EA7\u8282\u70B9\u95F4\u7684\u6C34\u5E73\u7F29\u8FDB(px)",
        nodeKey: "\u6BCF\u4E2A\u6811\u8282\u70B9\u7528\u6765\u4F5C\u4E3A\u552F\u4E00\u6807\u8BC6\u7684\u5C5E\u6027\uFF0C\u6574\u68F5\u6811\u5E94\u8BE5\u662F\u552F\u4E00\u7684"
      }
    },
    upload: {
      name: "\u4E0A\u4F20",
      info: "\u4E0A\u4F20\u6210\u529F\u540E\uFF0C\u5C06\u63A5\u53E3\u8FD4\u56DE\u7684 URL \u8D4B\u503C\u7ED9 file.url\uFF0C\u6216\u5C06\u8FD4\u56DE\u7ED3\u679C\u8D4B\u503C\u7ED9 file.value\uFF0C\u4EE5\u4FBF\u5728\u540E\u7EED\u7684\u8868\u5355\u63D0\u4EA4\u65F6\u83B7\u53D6\u8FD9\u4E9B\u6570\u636E\u3002",
      event: {
        remove: "\u6587\u4EF6\u5217\u8868\u79FB\u9664\u6587\u4EF6\u65F6\u89E6\u53D1",
        preview: "\u70B9\u51FB\u6587\u4EF6\u5217\u8868\u4E2D\u5DF2\u4E0A\u4F20\u7684\u6587\u4EF6\u65F6\u89E6\u53D1",
        error: "\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25\u65F6\u89E6\u53D1",
        progress: "\u6587\u4EF6\u4E0A\u4F20\u65F6\u89E6\u53D1",
        exceed: "\u5F53\u8D85\u51FA\u9650\u5236\u65F6\u89E6\u53D1"
      },
      slots: {
        tip: "\u8BF4\u660E"
      },
      props: {
        listType: "\u4E0A\u4F20\u7C7B\u578B",
        multiple: "\u662F\u5426\u652F\u6301\u591A\u9009\u6587\u4EF6",
        action: "\u4E0A\u4F20\u7684\u5730\u5740(\u5FC5\u586B)",
        beforeUpload: "\u4E0A\u4F20\u6587\u4EF6\u4E4B\u524D\u89E6\u53D1",
        onSuccess: "\u4E0A\u4F20\u6210\u529F\u65F6\u89E6\u53D1",
        beforeRemove: "\u5220\u9664\u6587\u4EF6\u4E4B\u524D\u89E6\u53D1",
        headers: "\u8BBE\u7F6E\u4E0A\u4F20\u7684\u8BF7\u6C42\u5934\u90E8",
        data: "\u4E0A\u4F20\u65F6\u9644\u5E26\u7684\u989D\u5916\u53C2\u6570",
        name: "\u4E0A\u4F20\u7684\u6587\u4EF6\u5B57\u6BB5\u540D",
        withCredentials: "\u652F\u6301\u53D1\u9001 cookie \u51ED\u8BC1\u4FE1\u606F",
        accept: "\u63A5\u53D7\u4E0A\u4F20\u7684\u6587\u4EF6\u7C7B\u578B",
        autoUpload: "\u662F\u5426\u5728\u9009\u53D6\u6587\u4EF6\u540E\u7ACB\u5373\u8FDB\u884C\u4E0A\u4F20",
        disabled: "\u662F\u5426\u7981\u7528",
        limit: "\u6700\u5927\u5141\u8BB8\u4E0A\u4F20\u4E2A\u6570"
      }
    },
    audioBox: {
      name: "\u97F3\u9891\u64AD\u653E\u5668",
      preloadOpt: {
        auto: "\u81EA\u52A8",
        metadata: "\u5143\u6570\u636E",
        none: "\u7981\u6B62"
      },
      event: {
        pause: "\u97F3\u9891\u64AD\u653E\u6682\u505C\u65F6\u89E6\u53D1",
        play: "\u97F3\u9891\u5F00\u59CB\u64AD\u653E\u65F6\u89E6\u53D1",
        ended: "\u97F3\u9891\u64AD\u653E\u7ED3\u675F\u540E\u89E6\u53D1"
      },
      props: {
        src: "\u97F3\u9891\u5730\u5740",
        type: "\u97F3\u9891\u7C7B\u578B",
        autoplay: "\u662F\u5426\u81EA\u52A8\u64AD\u653E",
        loop: "\u662F\u5426\u5FAA\u73AF\u64AD\u653E",
        muted: "\u662F\u5426\u9759\u97F3",
        controls: "\u662F\u5426\u663E\u793A\u63A7\u5236\u6761",
        preload: "\u9884\u52A0\u8F7D"
      }
    },
    barCodeBox: {
      name: "\u6761\u5F62\u7801",
      props: {
        value: "\u5185\u5BB9",
        format: "\u6761\u5F62\u7801\u7684\u7C7B\u578B",
        width: "\u5355\u4E2A\u6761\u5F62\u7684\u5BBD\u5EA6",
        height: "\u6761\u5F62\u7801\u7684\u9AD8\u5EA6",
        displayValue: "\u662F\u5426\u663E\u793A\u5185\u5BB9",
        fontSize: "\u8BBE\u7F6E\u6587\u5B57\u7684\u5927\u5C0F",
        textPosition: "\u8BBE\u7F6E\u6587\u5B57\u7684\u4F4D\u7F6E",
        textAlign: "\u8BBE\u7F6E\u6587\u5B57\u7684\u5BF9\u9F50\u65B9\u5F0F",
        textMargin: "\u8BBE\u7F6E\u6587\u5B57\u7684\u8FB9\u8DDD",
        background: "\u6761\u5F62\u7801\u7684\u80CC\u666F\u8272",
        lineColor: "\u7EBF\u6761\u7684\u989C\u8272"
      }
    },
    iframeBox: {
      name: "\u5185\u5D4C\u9875\u9762",
      event: {
        load: "\u9875\u9762\u52A0\u8F7D\u5B8C\u6210\u540E\u89E6\u53D1"
      },
      loadingOpt: {
        eager: "\u7ACB\u5373\u52A0\u8F7D",
        lazy: "\u5EF6\u8FDF\u52A0\u8F7D"
      },
      props: {
        src: "\u9875\u9762\u94FE\u63A5",
        loading: "\u52A0\u8F7D\u65B9\u5F0F"
      }
    },
    qrCodeBox: {
      name: "\u4E8C\u7EF4\u7801",
      circleTypeOpt: {
        square: "\u6B63\u65B9\u5F62",
        dots: "\u70B9\u72B6",
        rounded: "\u5706\u5F62",
        classy: "\u7ECF\u5178"
      },
      props: {
        data: "\u5185\u5BB9",
        image: "\u4E2D\u5FC3\u7684\u56FE\u7247\u94FE\u63A5",
        width: "\u4E8C\u7EF4\u7801\u7684\u5BBD\u5EA6",
        height: "\u4E8C\u7EF4\u7801\u7684\u9AD8\u5EA6",
        circleType: "\u70B9\u7684\u7C7B\u578B",
        circleColor: "\u70B9\u7684\u989C\u8272"
      }
    },
    signaturePad: {
      name: "\u624B\u5199\u7B7E\u540D",
      props: {
        penColor: "\u7EBF\u6761\u7684\u989C\u8272"
      }
    },
    videoBox: {
      name: "\u89C6\u9891\u64AD\u653E\u5668",
      event: {
        error: "\u89C6\u9891\u52A0\u8F7D\u5931\u8D25\u65F6\u89E6\u53D1",
        pause: "\u89C6\u9891\u64AD\u653E\u6682\u505C\u65F6\u89E6\u53D1",
        play: "\u89C6\u9891\u5F00\u59CB\u64AD\u653E\u65F6\u89E6\u53D1",
        ended: "\u89C6\u9891\u64AD\u653E\u7ED3\u675F\u540E\u89E6\u53D1"
      },
      props: {
        src: "\u89C6\u9891\u5730\u5740",
        type: "\u89C6\u9891\u7C7B\u578B",
        autoplay: "\u662F\u5426\u81EA\u52A8\u64AD\u653E",
        loop: "\u662F\u5426\u5FAA\u73AF\u64AD\u653E",
        isLive: "\u662F\u5426\u662F\u76F4\u64AD",
        controls: "\u662F\u5426\u663E\u793A\u63A7\u5236\u6761",
        withCredentials: "\u662F\u5426\u643A\u5E26\u51ED\u8BC1"
      }
    }
  },
  tmp: {
    duration: "\u65F6\u957F",
    chineseAmount: "\u91D1\u989D",
    col3: "\u4E09\u5217\u6805\u683C",
    col4: "\u56DB\u5217\u6805\u683C",
    table43: "4x3\u8868\u683C"
  }
};
export {
  e as default
};
