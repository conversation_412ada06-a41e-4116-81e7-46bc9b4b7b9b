<template>
  <el-scrollbar height="calc(100vh - 150px)">
    <ContentWrap :title="t('admin_user_userDetail')" :loading="loading">
      <!-- 基础信息 -->
      <div class="menu mb-50px">
        <div class="menu-title flex items-center font-700"><span
            class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
          {{ t('admin_user_basicInfo') }}
        </div>
        <div class="menu-content mt-12px">
          <el-descriptions :column="4" size="default" direction="vertical">
            <el-descriptions-item :label="t('admin_user_avatar')" :span="1">
              <el-image v-if="pageData.avatar" :src="pageData.avatar" class="h-80px w-80px"
                @click="imagePreview(pageData.avatar)" />
              <span v-else>-</span>
            </el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_name')" :span="1">{{ pageData.username || '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_userEmail')" :span="1">{{ pageData.email || '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_userStatus')" :span="1">
              <el-switch :model-value="pageData.status" active-color="#409eff" :active-value="1" :inactive-value="0"
                :loading="statusLoading" @change="handleChangeStatus()" />
            </el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_isActivate')">{{ getDict(pageData.activate,
      getBoolDictOptions(DICT_TYPE.COMMON_BOOLEAN_STRING))
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_profile_phone')" :span="1">{{ pageData.mobile || '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_profile_sex')" :span="1"> {{ getDict(pageData.sex,
      getIntDictOptions(DICT_TYPE.SYSTEM_USER_SEX))
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_registerTime')">{{ formatDate(pageData.createTime) || '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_latestLoginTime')">{{ formatDate(pageData.latestLoginTime) ||
      '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_userAgent')">{{ pageData.userAgent || '-'
              }}</el-descriptions-item>
            <el-descriptions-item :label="t('admin_user_userIp')">{{ pageData.userIp || '-'
              }}</el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <!-- 拥有的团队 -->
      <div class="menu mb-50px">
        <div class="menu-title flex items-center font-700"><span
            class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
          {{ t('admin_user_ownerTeamCount') }}
          <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
      pageData.ownerTeamList?.length || 0 }} {{ t('admin_user_individual') }}</span>
        </div>
        <div class="menu-content mt-12px">
          <Table :columns="tableState.ownerTeamColumns" :data="pageData.ownerTeamList" min-height="600">
          </Table>
        </div>
      </div>
      <!-- 加入的团队 -->
      <div class="menu mb-50px">
        <div class="menu-title flex items-center font-700"><span
            class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
          {{ t('admin_user_joinTeamCount') }}
          <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
      pageData.joinTeamList?.length || 0 }} {{ t('admin_user_individual') }}</span>
        </div>
        <div class="menu-content mt-12px">
          <Table :columns="tableState.joinTeamColumn" :data="pageData.joinTeamList" min-height="600">
          </Table>
        </div>
      </div>
      <!-- 关联的SCCS -->
      <div class="menu mb-50px">
        <div class="menu-title flex items-center font-700"><span
            class="inline-block w-5px h-15px mr-5px bg-#409eff"></span>
          {{ t('admin_user_joinSccsCount') }}
          <span class="ml-5px text-12px color-#b6b6b6 font-500">{{ t('admin_user_common') }} {{
      pageData.joinSccsList?.length || 0 }} {{ t('admin_user_individual') }}</span>
        </div>
        <div class="menu-content mt-12px">
          <Table :columns="tableState.sccsListColumns" :data="pageData.joinSccsList" min-height="600">
          </Table>
        </div>
      </div>
    </ContentWrap>
  </el-scrollbar>
</template>
<script setup lang="ts">
import { formatDate } from '@/utils/formatTime'
import businessConfig from './index.config'
import { createImageViewer } from '@/components/ImageViewer'
import { DICT_TYPE, getBoolDictOptions, getIntDictOptions } from '@/utils/dict'
import { UserApi } from '@/api/user/userMsg'


/** 运营端行业管理 表单 */
defineOptions({ name: 'UserDetail' })
const message = useMessage() // 消息弹窗

const { t } = useI18n() // 国际化
const route = useRoute()
const loading = ref(false)
const userId = ref('')
const pageData: any = ref({ status: 1 }) // 页面数据
const statusLoading = ref(false)
const tableState = reactive({
  ownerTeamColumns: businessConfig.ownerTeamListTableThs,
  joinTeamColumn: businessConfig.joinTeamTableThs,
  sccsListColumns: businessConfig.sccsListTableThs
})

const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

const handleChangeStatus = async () => {
  statusLoading.value = true
  try {
    // 确认的二次确认
    await message.confirm(pageData.value.status == 1 ? t('admin_common_sureOutContent') : t('admin_common_sureOnContent'), pageData.value.status == 1 ? t('admin_common_sureOutTip') : t('admin_common_sureOnTip'))
    const data = { userId: userId.value }
    data['status'] = pageData.value.status == 1 ? 0 : 1
    await UserApi.setStatus(data)
    getDetail()
  } finally {
    statusLoading.value = false
  }
}

const getDetail = async () => {
  loading.value = true
  try {
    pageData.value = await UserApi.getUser(userId.value)
    console.log('pageData', pageData.value)
  } finally {
    loading.value = false
  }
}
const init = async () => {
  userId.value = route.query.id as string
  getDetail()
}
init()

const getDict = (val, options) => {
  if (!val) return '-'
  for (let data of options) {
    if (data.value === val) {
      return data.label
    }
  }
}


</script>
<style scoped lang="scss">
:deep(.el-descriptions__label) {
  font-weight: 700 !important;
}

:deep(.el-descriptions__cell) {
  width: 25% !important;
  padding: 0 10px;
  vertical-align: top;
}
</style>
