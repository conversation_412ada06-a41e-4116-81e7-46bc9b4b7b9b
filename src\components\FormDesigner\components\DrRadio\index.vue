<template>
  <div class="fc-dr-radio">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <el-tag v-if="currentOption" :color="currentOption.color || '#EFEFEF'" style="color: #000">{{
        currentOption.label
      }}</el-tag>
      <span v-else>--</span>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <template v-if="layout !== 'select'">
        <el-radio-group v-model="modelValue">
          <div class="flex-layout" :class="direction">
            <div v-for="option in options" :key="option.value" class="flex-layout-item">
              <el-radio :value="option.value">
                <div class="radio-label" :style="`background-color: ${option.color || '#EFEFEF'};`">
                  {{ option.label }}
                </div>
              </el-radio>
            </div>
          </div>
        </el-radio-group>
      </template>
      <template v-else>
        <el-select v-model="modelValue" clearable :placeholder="placeholder">
          <template #label="{ label }">
            <el-tag :color="currentOption?.color || '#EFEFEF'" size="small" style="color: #000">{{
              label
            }}</el-tag>
          </template>
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="select-option">
              <el-tag class="color-tag" :color="item.color" size="small" />
              <span>{{ item.label }}</span>
            </div>
          </el-option>
        </el-select>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'

type PropsType = {
  formCreateInject?: any
  options: any[]
  disabled?: boolean
  layout: 'horizontal' | 'vertical' | 'select'
  placeholder?: string
}
const modelValue = defineModel<string | undefined>({ required: false, default: undefined })

const props = withDefaults(defineProps<PropsType>(), {
  options: () => [],
  layout: 'horizontal',
  disabled: false
})

watch(
  () => props.options,
  (options) => {
    const defaultOption = options.find((item) => item.checked)
    if (!defaultOption) {
      modelValue.value = undefined
      return
    }
    modelValue.value = defaultOption.value
  },
  { immediate: true, deep: true }
)

const onChange = (value: string) => {
  console.log(value)

  // emit('update:modelValue', value)
}

const currentOption = computed(() => {
  const option = props.options.find((item) => item.value === modelValue.value)
  return option
})

const direction = computed(() => {
  const directionMap = {
    horizontal: 'row',
    vertical: 'column',
    select: ''
  }

  return directionMap[props.layout]
})
</script>
<style scoped lang="scss">
.fc-dr-radio {
  width: 100%;

  .flex-layout {
    display: flex;

    &.row {
      flex-wrap: wrap;
      flex-direction: row;
      gap: 0 24px;
    }

    &.column {
      flex-direction: column;
    }

    .radio-label {
      height: 20px;
      line-height: 20px;
      padding: 0 10px;
      border-radius: 8px;
    }
  }
}

.select-option {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;

  .color-tag {
    border: none;
    aspect-ratio: 1;
  }
}
</style>
