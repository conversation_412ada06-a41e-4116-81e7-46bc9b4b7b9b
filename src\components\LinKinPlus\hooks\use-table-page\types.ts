import type { ComponentProps, ResType } from '../types'
export type RequestType = (val: any) => Promise<any>

export type CommandType<D> = {
  label?: string
  key: string
  type?: string
  render?: (val: D, index?: number) => JSX.Element
  width?: number
  [key: string]: any
}

export type PageProps<D> = {
  pageClass?: string[]
  API: {
    get: (...arg: any[]) => Promise<ResType<D>>
    post?: RequestType
    put?: RequestType
    delete?: RequestType
    [key: string]: RequestType | undefined
  }
  prefix?: () => JSX.Element
  deleteProps?: object
  tableColumns?: ComponentProps<D>[]
  queryColumns?: ComponentProps<D>[]
  formColumns?: ComponentProps<D>[]
  columns: ComponentProps<D>[]
  commands?: CommandType<D>[]
  operateProps?: object
  rules?: object
  query?: object
  handleCommand?: (type: string, data?: D, index?: number) => void
}
