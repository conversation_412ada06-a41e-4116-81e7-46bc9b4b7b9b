/*!
 * FormCreate商业版可视化表单设计器-让表单设计更简单
 * fc-designer-pro v5.6.0
 * (c) 2021-2025 FormCreate Team
 * Github https://github.com/xaboy/form-create-designer
 * license 仅限于被授权主体（个人、企业或组织）使用
 */
const e = {
  name: "en",
  form: {
    field: "Field",
    title: "Title",
    info: "Info",
    ignore: "ignore field",
    native: "Whether to display title",
    control: "Control",
    labelShow: "Whether to display",
    labelPosition: "Label position",
    labelStyle: "Label style",
    labelSuffix: "Label suffix",
    formItem: "Configure form item",
    formItemSpan: "Form item width",
    row: "Row",
    globalEvent: "Global event",
    globalFetch: "Global dataset",
    globalClass: "Global style",
    globalConfig: "Global",
    size: "Form size",
    event: "Form event",
    labelWidth: "Label width",
    hideRequiredAsterisk: "Hide the red asterisk next to the label of a required field",
    formItemMarginBottom: "Bottom margin of form items",
    showMessage: "Display verification error message",
    inlineMessage: "Display validation information inline",
    submitBtn: "Whether to display the form submit button",
    resetBtn: "Whether to display the form reset button",
    appendChild: "Insert child",
    formMode: "Form mode",
    formName: "Form name",
    ignoreHiddenFields: "Ignore hidden fields when submitting form",
    previewMode: "Reading mode",
    componentMode: "Component",
    sfcMode: "SFC",
    document: "Document",
    controlDocument: "Need more detailed configuration methods? Please view {doc}",
    onSubmit: "Triggered when form is submitted",
    onReset: "Triggered after form is reset",
    onCreated: "Triggered after the form component is initialized",
    onMounted: "Triggered after the form component is mounted",
    onReload: "Triggered after the form rendering rule is reloaded",
    onChange: "Triggered when the component value changes",
    beforeFetch: "Triggered before remote data request is sent"
  },
  ai: {
    name: "Smart Assistant",
    info: "Hello! I'm your smart form assistant. I can help you quickly create and edit forms for efficient design.",
    try: "Try asking me",
    change: "Change",
    loading: "Generating your form, please wait...",
    fail: "There was an issue creating the form. Please adjust your request.",
    success: "Processing complete!",
    placeholder: "Describe your requirements"
  },
  warning: {
    name: "Unique identifier for the component, used to access and modify its configuration rules.",
    field: "Field name for binding data to the component. Must start with a letter for proper recognition.",
    formList: "Manages and switches form templates for quick switching and configuration in different business scenarios.",
    fetch: "Loads remote data through requests, updating the component based on the returned result.",
    fetchQuery: "Defines GET parameters for requests, passed via the URL.",
    fetchData: "Defines POST parameters for requests, passed in the request body.",
    fetchDataType: "Selects the data type for the request body to ensure correct format.",
    fetchParse: "Processes the response data after the request and converts it into the required structure.",
    language: "Manages multilingual data, allowing easy language switching for content display.",
    variable: "Reads configuration values from variables (e.g., Cookie, localStorage) and syncs updates when variables change.",
    variableInfo: "Select a variable from the list. If the variable is an object, access its properties using the format {{variableName.attributeName}}.",
    pageManage: "Manages sub-dialogs in forms, triggered by events to dynamically display information or enable interaction.",
    globalConfig: "Manages global events, styles, data, and variables for data sharing and event handling across components.",
    globalClass: "Defines global styles and classes for unified style management in component configurations.",
    globalEvent: "Defines global events for components to use, simplifying event management across components.",
    globalFetch: "Defines global data sources for static and remote data, used across component configurations.",
    globalVariable: "Defines computed properties in global variables, automatically updating when associated data changes.",
    ignore: "Excludes the component\u2019s field from submission data when enabled.",
    ignoreHiddenFields: "Excludes hidden fields from form submission when enabled.",
    behaviorIgnoreError: "Defines whether to continue executing subsequent actions if an error occurs.",
    behaviorExpression: "Executes actions only when conditions are met.",
    behaviorStopPropagation: "Stops subsequent actions from executing when conditions are met.",
    computedCondition: "Automatically adjusts component states and data based on defined conditions.",
    computedFormula: "Dynamically calculates component states and data based on formulas and form data."
  },
  computed: {
    value: {
      title: "Data linkage",
      btn: "Set data linkage",
      name: "Set component value"
    },
    hidden: {
      title: "Hide condition",
      btn: "Set condition",
      name: "Set Hide condition for component"
    },
    required: {
      title: "Required condition",
      btn: "Set condition",
      name: "Set required condition for component"
    },
    disabled: {
      title: "Disable condition",
      btn: "Set condition",
      name: "Set disabled condition for component"
    },
    formulas: {
      "==": "Equal",
      "!=": "Not equal",
      on: "Include",
      notOn: "Not included",
      empty: "Is empty",
      notEmpty: "Not empty",
      pattern: "Regular",
      ">": "Greater than",
      ">=": "Greater than or equal",
      "<": "Less than",
      "<=": "Less than or equal"
    },
    variable: {
      attr: "Attribute list",
      bind: "Bind variable",
      list: "Variable list",
      btn: "Set variable",
      title: "Global variable",
      create: "Create variable",
      placeholder: "Please enter a description of the variable"
    },
    linkage: {
      trigger: "Trigger the following linkage",
      info: [
        "The current component linkage display the value of"
      ]
    },
    name: "Calculation formula",
    setting: "Setting conditions",
    invert: "Component status after the condition is met",
    condition: "Logic condition",
    addCondition: "Add condition",
    addGroup: "Add condition group",
    form: "Current",
    subform: "Subform",
    formula: "Formula",
    formulaInfo: "Function description",
    formulaExample: "Function example",
    fieldUsed: "[{label}] Is used in the calculation formula, please modify the corresponding formula first",
    fieldExist: "[{label}] Field already exists",
    fieldEmpty: "Field is required",
    fieldChar: "Field must begin with a letter"
  },
  validate: {
    type: "Type",
    typePlaceholder: "Please select",
    trigger: "Trigger",
    mode: "Verification method",
    modes: {
      required: "required",
      notRequired: "Not required",
      pattern: "pattern",
      validator: "validator",
      min: "min",
      max: "max",
      len: "length"
    },
    types: {
      string: "String",
      boolean: "Boolean",
      array: "Multiple",
      number: "Number",
      integer: "Integer",
      float: "Float",
      object: "Collection",
      date: "Date",
      url: "Url",
      email: "Email"
    },
    message: "Error",
    auto: "Automatic",
    autoRequired: "Please enter {title}",
    autoMode: "Please enter the correct {title}",
    requiredPlaceholder: "Please enter",
    required: "Is it required",
    rule: "Validation"
  },
  tableOptions: {
    handle: "Operation",
    add: "Add",
    empty1: "Click the lower right corner",
    empty2: "Button to add a column",
    rmCol: "Delete current column",
    rmRow: "Delete current row",
    splitRow: "Split into rows",
    splitCol: "Split into columns",
    mergeBottom: "Merge downward",
    mergeRight: "Merge right",
    addTop: "Add top column",
    addBottom: "Add the following",
    addLeft: "Add left column",
    addRight: "Add right column",
    keyValue: "key-value"
  },
  struct: {
    title: "Edit",
    only: "[{label}] Only one allowed to be added",
    errorMsg: "The input content is syntactically incorrect",
    configured: "Configured",
    configuredData: "{num} configured",
    configuredEvent: "{num} configured"
  },
  class: {
    title: "Set style",
    create: "Create style",
    placeholder: "Please enter the name of the class",
    saveMsg: "Please save the style you are editing first",
    configured: "{num} configured"
  },
  event: {
    title: "Edit",
    create: "Create",
    list: "List",
    placeholder: "Please enter the name of the event",
    saveMsg: "Please save the event currently being edited",
    type: "Type",
    info: "Info",
    action: "Edit behavior",
    inject: {
      api: "API of current form",
      rule: "Generate rules for the current form",
      self: "Component generation rule",
      option: "Form configuration",
      args: "Original parameters of event"
    }
  },
  eventInfo: {
    blur: "Triggered when focus is lost",
    focus: "Triggered when focus is obtained",
    change: "Triggered when the binding value changes",
    input: "Trigger when value changes",
    clear: "Triggered when the clear button is clicked",
    close: "Triggered when the component is closed",
    click: "Fires when the component is clicked",
    add: "Trigger when added",
    delete: "Triggered when deleted",
    remove: "Triggered when deleted",
    visibleChange: "Triggered when the drop-down box appears/hides",
    calendarChange: "Triggered when the selected date in the calendar changes",
    panelChange: "Fires when the date panel changes",
    open: "Triggered when opening",
    opened: "Triggered when opening animation ends",
    closed: "Triggered when closing animation ends",
    openAutoFocus: "Triggered when entering focus on content",
    closeAutoFocus: "Triggered when entering focus from content",
    submit: "Triggered when submitting table",
    confirm: "Triggered when clicking confirm",
    validateFail: "Triggered when table verification fails",
    beforeLoad: "Triggered before initialization",
    loaded: "Triggered after initialization is completed",
    hook_load: "Triggered after component rules are loaded",
    hook_mounted: "Triggered after component is mounted",
    hook_deleted: "Triggered after component rules are removed",
    hook_watch: "Triggered after component rules change",
    hook_value: "Triggered after component value changes",
    hook_hidden: "Triggered after component display status changes"
  },
  fetch: {
    info: "When defining a request, variables can be used with double curly braces (e.g., {{token}}). These variables are automatically read during the request. If the variable is an object, its properties can be accessed using {{variableName.attributeName}}.",
    title: "Set data",
    create: "Create data",
    config: "Request",
    action: "Action",
    actionRequired: "Please enter the correct link",
    placeholder: "Please enter the name of the data source",
    method: "Method",
    data: "Data",
    dataType: "DataType",
    headers: "Headers",
    query: "Query",
    parse: "Processing",
    response: "Data returned by the interface",
    onError: "onError",
    remote: "Remote",
    static: "Static",
    optionsType: {
      fetch: "Fetch",
      global: "Dataset",
      struct: "Static"
    }
  },
  style: {
    width: "Width",
    height: "Height",
    minWidth: "Min Width",
    minHeight: "Min Height",
    maxWidth: "Max Width",
    maxHeight: "Max Height",
    color: "Color",
    backgroundColor: "Background color",
    margin: "Margin",
    padding: "Padding",
    borderRadius: "Border radius",
    border: "Border",
    solid: "Solid",
    dashed: "Dashed",
    dotted: "Dotted",
    double: "Double",
    opacity: "Opacity",
    scale: "Scale",
    overflow: {
      name: "Overflow",
      visible: "Visible",
      hidden: "Hidden",
      scroll: "Scroll",
      auto: "Auto scroll after overflow"
    },
    shadow: {
      name: "Shadow",
      x: "x-axis offset",
      y: "y-axis offset",
      vague: "blurred radius",
      extend: "diffusion radius",
      inset: "inward",
      external: "outward",
      mode: "Mode",
      classic: "Classic",
      flat: "Flat",
      solid: "Stereoscopic"
    },
    display: {
      name: "Display",
      block: "Block",
      "inline-block": "Inline block",
      inline: "Inline",
      flex: "Flex"
    },
    flexDirection: {
      name: "Direction of the spindle",
      row: "The main axis is horizontal and the starting point is on the left",
      "row-reverse": "The main axis is horizontal and the starting point is on the right",
      column: "The main axis is vertical and the starting point is on the upper edge",
      "column-reverse": "The main axis is vertical and the starting point is at the lower edge"
    },
    flexWrap: {
      name: "Whether to wrap",
      nowrap: "No newline",
      wrap: "Newline"
    },
    justifyContent: {
      name: "Alignment on main axis",
      "flex-start": "Left-aligned",
      "flex-end": "Right-aligned",
      center: "Center",
      "space-between": "Align both ends",
      "space-around": "Equal spacing on both sides of child elements",
      "space-evenly": "Child elements are evenly distributed on the main axis"
    },
    alignItems: {
      name: "Alignment of child elements on the cross axis",
      "flex-start": "Align the starting point of the cross axis",
      "flex-end": "Align the end of the cross axis",
      center: "Align the midpoint of the cross axis",
      baseline: "Align along the baseline of the first line of text",
      stretch: "Full the height of the entire container"
    },
    alignContent: {
      name: "Alignment of multi-line child elements on the cross axis",
      "flex-start": "Align with the starting point of the cross axis",
      "flex-end": "Align with the end point of the cross axis",
      center: "Align with the midpoint of the cross axis",
      "space-between": "Align with both ends of the cross axis",
      "space-around": "Multiple rows of sub-elements evenly distributed on the cross axis",
      stretch: "The axis occupies the entire cross axis"
    },
    font: {
      name: "Font",
      size: "Size",
      align: "Align",
      height: "line-height",
      spacing: "letter-spacing",
      preview: "Preview"
    },
    decoration: {
      name: "Decoration",
      underline: "underline",
      "line-through": "line-through",
      overline: "overline"
    },
    weight: {
      name: "font-weight",
      300: "Fine",
      400: "Default",
      500: "Medium",
      700: "Bold"
    }
  },
  designer: {
    component: "Component",
    id: "Unique id",
    name: "Serial number",
    type: "Type",
    form: "Visible Form",
    json: "Rule",
    style: "Style",
    rule: "Basis",
    advanced: "Advanced",
    props: "Props",
    slots: "Slots",
    customProps: "Custom props",
    validate: "Validate",
    event: "Event",
    clearWarn: "It cannot be restored after clearing it. Are you sure you want to clear it? ",
    childEmpty: "Click the \\e789  button in the lower right corner to add a column",
    dragEmpty: "Drag the components from the list on the left here",
    unload: "Are you sure you want to leave the current page?",
    sublist: "Sublist",
    formList: "Form",
    comList: "Component",
    addPage: "Add module",
    pageManage: "Module",
    main: "Main",
    layout: "Quick Layout",
    col1: "One column",
    col2: "Two columns",
    col3: "Three columns",
    col4: "Four columns"
  },
  menu: {
    template: "Template",
    main: "Basic",
    aide: "Auxiliary",
    layout: "Layout",
    component: "Component",
    subform: "Subform",
    container: "Container",
    chart: "Chart",
    tree: "Structure"
  },
  formula: {
    math: "Math",
    string: "Text",
    date: "Date",
    collection: "Collection",
    condition: "Condition",
    ADD: "Get the value of adding two numbers",
    SUB: "Get the value of the subtraction of two numbers",
    MUL: "Get the value of multiplying two numbers",
    DIV: "Get the value of dividing two numbers",
    SUM: "Get the sum of the values in the collection",
    MAX: "Get the maximum value in the parameter list",
    MIN: "Get the minimum value in the parameter list",
    ABS: "Get the absolute value of a number",
    AVG: "Get the average value of the parameter list",
    POWER: "Get the power of the specified number",
    RAND: "Get a random number greater than or equal to 0 and less than 1",
    PI: "Get PI",
    ROUND: "Round a decimal to the specified number of digits",
    SQRT: "Get the positive square root of a number",
    NOW: "Get the current time",
    TODAY: "Get today date",
    YEAR: "Get the year of the specified date",
    MONTH: "Get the month of the specified date",
    DAY: "Get the number of days on the specified date",
    HOUR: "Get the number of hours on the specified date",
    MINUTE: "Get the minutes of the specified date",
    SECOND: "Get the seconds of the specified date",
    DIFFDAYS: "Get the number of days between two dates",
    DIFFHOURS: "Get the number of hours between two times, keeping two decimal places",
    DIFFMINUTES: "Get the number of minutes between two times",
    TIMESTAMP: "Get the timestamp of a specified date",
    STARTSWITH: "Check whether the string starts with the specified string",
    EMPTY: "Check whether the parameter is empty",
    NOTEMPTY: "Check whether the parameter is not empty",
    LEN: "Get the length of the specified collection",
    MOD: "Get the remainder of two numbers",
    FLOOR: "Get the value of the specified number rounded down",
    CEIL: "Get the value of the specified number rounded up",
    FIXED: "Round a decimal to the specified number of decimal places",
    ISNUMBER: "Check whether the parameter is a number",
    TONUMBER: "Convert parameter to number",
    SLICELEFT: "Get a string with a specified length starting from the beginning",
    SLICERIGHT: "Get a string with a specified length starting from the end",
    TOLOWER: "Convert all uppercase letters in the string to lowercase letters",
    TOUPPER: "Convert all lowercase letters in the string to uppercase letters",
    INCLUDES: "Check whether the string contains the specified string",
    REPLACE: "Replace part of the text in the string with different text, only replacing the first match",
    REPLACEALL: "Replace part of the text in the string with different text, replacing all matches",
    TRIM: "Remove spaces before and after the string",
    TOCHINSESAMOUNT: "Get the Chinese capital amount of the specified number",
    UNION: "Deduplicate the values in the collection/parameters and return the deduplicated collection",
    INTERSECTIONSET: "Get the intersection of two collections",
    LIST: "Get a collection of all parameters",
    AND: 'Link expressions with "AND", return true when all expressions are true, otherwise return false',
    OR: 'Link expressions with "OR", return true when one expression is true, otherwise return false',
    IF: "Check whether a condition is met, return the second parameter if it is met, otherwise return the third parameter",
    IN: "Check whether the second parameter is in the collection",
    DEFAULT: "Check the first parameter, return the second parameter if it is empty, otherwise return the first parameter",
    CASE: "Check whether one or more conditions are met and return the first value that meets the conditions",
    COLUMN: "Get the specified fields in the subform and return the collection",
    VALUE: "Get the specified field in the group form",
    CONCAT: "Concatenate all parameters and return the concatenated string",
    FALSE: "Return logical value false",
    TRUE: "Return logical value true",
    NOT: "Get the opposite value of a logical value",
    EQ: "Check if two values are equal",
    NE: "Check if two values are not equal",
    GE: "Check if the first value is greater than or equal to another value",
    GT: "Check if the first value is greater than another value",
    LE: "Check if the first value is less than or equal to another value",
    LT: "Check if the first value is less than another value"
  },
  language: {
    name: "Language",
    add: "Add",
    batchRemove: "Batch Deletion",
    select: "Select language"
  },
  props: {
    circle: "Circle",
    square: "Square",
    image: "Image",
    video: "Video",
    audio: "Audio",
    document: "Document",
    size: "Size",
    info: "Info",
    success: "Success",
    error: "Error",
    warning: "Warning",
    primary: "Primary",
    danger: "Danger",
    form: "Form",
    subform: "Subform",
    other: "Other",
    model: "Model",
    field: "Field",
    variable: "Variable",
    disabled: "Disabled",
    enable: "Enable",
    time: "time",
    email: "email",
    number: "number",
    globalData: "Global data",
    mobile: "Mobile",
    reactive: "Reactive",
    pc: "Pc",
    title: "Title",
    content: "Content",
    collection: "Collection",
    group: "Group",
    custom: "Custom",
    change: "Change",
    blur: "Blur",
    preview: "Preview",
    clear: "Clear",
    cancel: "Cancel",
    close: "Close",
    ok: "Ok",
    save: "Save",
    refresh: "Refresh",
    submit: "Submit",
    reset: "Reset",
    copy: "Copy",
    delete: "Delete",
    hide: "Hidden",
    show: "Show",
    position: "Position",
    render: "Render",
    large: "large",
    default: "default",
    small: "small",
    always: "always",
    never: "never",
    hover: "hover",
    click: "click",
    button: "button",
    year: "year",
    month: "month",
    date: "date",
    dates: "dates",
    week: "week",
    datetime: "datetime",
    "datetime-local": "datetime",
    datetimerange: "datetimerange",
    daterange: "daterange",
    monthrange: "monthrange",
    left: "left",
    right: "right",
    top: "top",
    bottom: "bottom",
    text: "text",
    icon: "icon",
    picture: "picture",
    "picture-card": "picture-card",
    center: "center",
    vertical: "vertical",
    horizontal: "horizontal",
    manage: "Manage",
    key: "key",
    name: "Name",
    value: "Value",
    inputData: "Default value",
    append: "Append",
    options: "Options",
    option: "Option",
    callback: "Callback",
    _self: "Current Window",
    _blank: "New Window",
    _parent: "Parent Window",
    _top: "Top Window"
  },
  slots: {
    prefix: "Prefix",
    suffix: "Suffix",
    prepend: "Prepend",
    append: "Append"
  },
  behavior: {
    add: "Add action",
    props: {
      id: "Component",
      status: "Status",
      compute: "Condition",
      static: "Static",
      formula: "Expression",
      setFormula: "Configure expression",
      continue: "Continue execution",
      stop: "Interrupt execution",
      break: "Skip current",
      model: "Model",
      fetch: "Request",
      response: "Request result",
      callback: "Custom JS",
      ignoreError: "Exception",
      expression: "Execution condition",
      stopPropagation: "Blocking condition",
      execute: "Execute action",
      info: "Action description"
    },
    openModel: {
      name: "Open pop-up window",
      info: "Open the selected pop-up window"
    },
    closeModel: {
      name: "Close pop-up window",
      info: "Close the current pop-up window"
    },
    hidden: {
      name: "Component visibility",
      info: "Control the display/hide of the selected component"
    },
    disabled: {
      name: "Component availability",
      info: "Control the enable/disable of the selected component"
    },
    resetFields: {
      name: "Reset form",
      info: "Reset form data"
    },
    clearFields: {
      name: "Clear form",
      info: "Clear form data"
    },
    validate: {
      name: "Validate form",
      info: "Validate the contents of the entire form"
    },
    validateFields: {
      name: "Validate form items",
      info: "Validate selected form items"
    },
    setValue: {
      name: "Form assignment",
      info: "Modify form data"
    },
    fetch: {
      name: "Send request",
      info: "Configure and send API request",
      props: {
        append: "Append formData"
      },
      warning: {
        append: "When enabled, the data from the remote request will be automatically appended to the form data.",
        response: "The data from the remote request will be temporarily stored in the specified variable."
      }
    },
    copy: {
      name: "Copy content",
      info: "Copy text content to the clipboard"
    },
    callback: {
      name: "Custom operation",
      info: "Customize action logic through JavaScript"
    },
    message: {
      name: "Message reminder",
      info: "Pop-up message reminder",
      props: {
        type: "Type",
        message: "Message",
        duration: "Duration (ms)",
        showClose: "Show close button"
      }
    },
    submit: {
      name: "Submit form",
      info: "Manually submit the form and trigger the form submission event"
    }
  },
  com: {
    cascader: {
      name: "Cascader",
      event: {
        expandChange: "Triggered when the expanded node changes",
        removeTag: "In multi-select mode, triggered when Tag is removed"
      },
      props: {
        props: "Options",
        placeholder: "Placeholder",
        disabled: "Disabled",
        clearable: "Whether clearing options are supported",
        showAllLevels: "Whether the full path of the selected value is displayed in the input box",
        collapseTags: "Whether to collapse Tags in multi-select mode",
        collapseTagsTooltip: "Whether to display all selected tags when the mouse hovers over the text of a collapsed tag",
        separator: "Separator",
        filterable: "Whether this option can be searched",
        tagType: "Type"
      },
      propsOpt: {
        multiple: "Whether there are multiple selections",
        expandTrigger: "How to expand the secondary menu",
        checkStrictly: "Whether it is strictly observed that parent and child nodes are not related to each other",
        emitPath: "When the selected node changes, whether to return an array consisting of the values of the menus at each level where the node is located",
        value: "The value of the specified option is an attribute value of the option object",
        label: "Specify the option label as a certain attribute value of the option object",
        children: "The child option of the specified option is a certain attribute value of the option object",
        disabled: "The specified option is disabled as a certain attribute value of the option object",
        leaf: "The flag bit of the leaf node of the specified option is an attribute value of the option object"
      }
    },
    checkbox: {
      name: "Checkbox",
      props: {
        input: "Whether to fill in",
        type: "Type",
        disabled: "Disabled",
        min: "Minimum number that can be checked",
        max: "The maximum number that can be checked",
        textColor: "Font color when the button is active",
        fill: "Border and background color when the button is active"
      }
    },
    col: {
      name: "Col",
      info: "Responsive layout configures multiple devices, with mobile automatically using the first one.",
      props: {
        span: "Number of columns occupied by grid",
        offset: "Number of spaces on the left side of the grid",
        push: "Move the grid to the right by the number of cells",
        pull: "Move the grid to the left by the number of cells"
      }
    },
    colorPicker: {
      name: "ColorPicker",
      event: {
        activeChange: "Triggered when the color currently displayed in the panel changes"
      },
      props: {
        disabled: "Disabled",
        showAlpha: "Whether transparency selection is supported",
        colorFormat: "Color format",
        predefine: "Predefined color"
      }
    },
    datePicker: {
      name: "Date",
      props: {
        pickerOptions: "Options specific to the current time and date picker",
        readonly: "Readonly",
        disabled: "Disabled",
        type: "Type",
        editable: "Text box can be input",
        clearable: "Whether to display the clear button",
        placeholder: "Placeholder content for non-range selection",
        startPlaceholder: "Placeholder content for the start date when selecting the range",
        endPlaceholder: "Placeholder content for the end date when selecting the range",
        format: "Format displayed in the input box",
        align: "Alignment",
        rangeSeparator: "Separator when selecting range",
        unlinkPanels: "Unlink the two date panels in the range selector"
      }
    },
    dateRange: {
      name: "DateRange"
    },
    timeRange: {
      name: "TimeRange"
    },
    elAlert: {
      name: "Alert",
      description: "Description",
      props: {
        title: "Title",
        type: "Type",
        description: "Supporting text",
        closable: "Whether it can be closed",
        center: "Whether the text is centered",
        closeText: "Close button custom text",
        showIcon: "Whether to display the icon",
        effect: "Select a provided theme"
      }
    },
    elButton: {
      name: "Button",
      props: {
        formCreateChild: "Content",
        size: "Size",
        type: "Type",
        plain: "Whether the button is plain",
        round: "Whether the button has rounded corners",
        circle: "Whether the button is round",
        loading: "Whether it is loading status",
        disabled: "Disabled"
      }
    },
    elCard: {
      name: "Card",
      props: {
        header: "Title",
        shadow: "Shadow display timing"
      }
    },
    elCollapse: {
      name: "Collapse",
      event: {
        change: "Switch the currently active panel, its type is string in accordion mode and array in other modes"
      },
      props: {
        accordion: "Whether it is in accordion mode"
      }
    },
    elCollapseItem: {
      name: "CollapseItem",
      props: {
        title: "Panel title",
        name: "Identifier",
        disabled: "Disabled"
      }
    },
    elDescriptions: {
      name: "Descriptions",
      props: {
        title: "Title text, displayed in the upper left",
        extra: "Text in the operating area, displayed in the upper right",
        column: "Number of tables in one row",
        border: "Whether it has a border",
        direction: "Direction of arrangement",
        size: "Size"
      }
    },
    elDescriptionsItem: {
      name: "DescriptionsItem",
      props: {
        label: "Label",
        __child: "Content",
        span: "Number of columns",
        width: "The width of the column, the width of the same column in different rows is set according to the maximum value",
        minWidth: "Minimum width of column",
        align: "Column content alignment",
        labelAlign: "The label alignment of the column. If this item is not set, the content alignment will be used",
        className: "Column content custom class name"
      }
    },
    elDivider: {
      name: "Divider",
      props: {
        direction: "Set the direction of the dividing line",
        formCreateChild: "Set Content",
        contentPosition: "Set content position"
      }
    },
    elTabPane: {
      name: "TabPane",
      props: {
        label: "Title",
        disabled: "Disabled",
        name: "Identifier of the tab",
        lazy: "Whether the label is delayed in rendering"
      }
    },
    elTabs: {
      name: "Tabs",
      event: {
        tabClick: "Triggered when tab is selected",
        tabChange: "Triggered when activeName changes",
        tabRemove: "Triggered when the tab remove button is clicked",
        tabAdd: "Triggered when a new tab button is clicked",
        edit: "Triggered after clicking the add or remove button of the tab"
      },
      props: {
        type: "Type",
        closable: "Whether the label can be closed",
        tabPosition: "Tab position",
        stretch: "Whether the width of the label is self-stretching"
      }
    },
    elTag: {
      name: "Tag",
      props: {
        formCreateChild: "Content",
        type: "Type",
        size: "Label size",
        effect: "Label theme",
        closable: "Whether it can be closed",
        disableTransitions: "Whether to disable gradient animation",
        hit: "Whether there is a border stroke",
        round: "Whether it is round",
        color: "Background color"
      }
    },
    elTransfer: {
      name: "Transfer",
      event: {
        leftCheckChange: "Triggered when the left list element is selected/unselected by the user",
        rightCheckChange: "Triggered when the right list element is selected/unselected by the user"
      },
      props: {
        filterable: "Is it searchable",
        filterPlaceholder: "Search box placeholder",
        targetOrder: "Sort strategy of list elements on the right",
        targetOrderInfo: "If it is original, keep the same order as the data; if it is push, the newly added elements will be ranked last; if it is unshift, the newly added elements will be ranked first",
        titles: "Title",
        buttonTexts: "Set button content",
        props: "Field alias of data source"
      }
    },
    elTreeSelect: {
      name: "TreeSelect",
      event: {
        removeTag: "Triggered when tag is removed in multi-select mode"
      },
      props: {
        multiple: "Whether there are multiple selections",
        disabled: "Disabled",
        clearable: "Whether the option can be cleared",
        collapseTags: "Whether to display the selected value as text during multi-selection",
        multipleLimit: "The maximum number of items that the user can select during multiple selection, if it is 0, there is no limit",
        placeholder: "Placeholder",
        props: "Options",
        renderAfterExpand: "Whether to render its child nodes after expanding a tree node for the first time",
        defaultExpandAll: "Whether to expand all nodes by default",
        expandOnClickNode: "Whether to expand or shrink nodes when clicking on them",
        checkOnClickNode: "Whether to select the node when clicking the node",
        nodeKey: "Each tree node is used as an attribute for unique identification, and the entire tree should be unique"
      }
    },
    elLink: {
      name: "Link",
      props: {
        formCreateChild: "Content",
        href: "Jump link",
        type: "Type",
        underline: "Whether to display underline",
        disabled: "Whether it is disabled",
        target: "Open with"
      }
    },
    elWatermark: {
      name: "Watermark",
      props: {
        content: "Watermark text content",
        image: "Image source, it is recommended to export 2x or 3x image, high priority",
        width: "The width of the watermark, the default value of content is its own width",
        height: "The height of the watermark, the default value of content is its own height",
        rotate: "When the watermark is drawn, the rotation Angle, unit \xB0",
        zIndex: "The z-index of the appended watermark element",
        gap: "The spacing between watermarks"
      }
    },
    elTooltip: {
      name: "Tooltip",
      props: {
        content: "Content",
        disabled: "Disabled",
        rawContent: "Whether the content is treated as an HTML string",
        enterable: "Whether the mouse can enter the Tooltip",
        effect: "Theme",
        placement: "The position where the Tooltip component appears",
        trigger: "How to trigger Tooltip",
        offset: "Offset of occurrence position",
        showAfter: "How long to display content after triggering, in milliseconds",
        hideAfter: "Delay closing in milliseconds",
        autoClose: "Automatically hide delay after appearing, unit is milliseconds"
      }
    },
    elImage: {
      name: "Image",
      props: {
        src: "Image path",
        previewSrcList: "Preview image list"
      }
    },
    elAvatar: {
      name: "Avatar",
      props: {
        src: "Image path",
        shape: "Shape",
        size: "Size"
      }
    },
    elMention: {
      name: "Mention",
      event: {
        search: "Triggered when the trigger field is pressed",
        select: "Triggered when user selects an option"
      },
      props: {
        type: "Type",
        placeholder: "Placeholder",
        clearable: "whether to show clear button",
        disabled: "Disabled",
        whole: "whether to delete the mention as a whole when backspace is pressed",
        checkIsWhole: "whether to delete the mention as a whole when backspace is pressed",
        filterOption: "filter options logic"
      }
    },
    elSegmented: {
      name: "Segmented",
      props: {
        size: "Size",
        block: "Fill the width of the parent element",
        disabled: "Disabled"
      }
    },
    elFormItem: {
      name: "FormItem",
      props: {
        label: "Label",
        labelWidth: "Label Width",
        size: "Size"
      }
    },
    elStatistic: {
      name: "Statistic",
      props: {
        value: "Value",
        title: "Title",
        prefix: "Prefix of the value",
        suffix: "Suffix of the value"
      }
    },
    fcTitle: {
      name: "Title",
      props: {
        title: "Title",
        size: "Size",
        align: "Align"
      }
    },
    fcId: {
      name: "ID",
      props: {
        prefix: "Prefix"
      }
    },
    fcCell: {
      name: "Cell"
    },
    fcEditor: {
      name: "Editor",
      props: {
        disabled: "Disabled"
      }
    },
    fcFlex: {
      name: "Flex"
    },
    fcFlex2: {
      name: "Flex",
      horizontal: "Horizontal Center",
      vertical: "Vertical Center",
      left: "Left align",
      right: "Right align",
      reset: "Reset layout"
    },
    fcRow: {
      name: "Row",
      props: {
        gutter: "Grid interval",
        type: "Flex layout mode",
        justify: "Horizontal arrangement under flex layout",
        align: "Vertical arrangement under flex layout"
      }
    },
    fcDialog: {
      name: "Dialog",
      props: {
        title: "Title",
        width: "Width of dialog box",
        fullscreen: "Is it full screen",
        modal: "Does it need a mask layer",
        autoClose: "Automatically close the popup after submitting the form",
        footer: "Whether to show operation buttons",
        beforeClose: "Callback before closing"
      }
    },
    fcDrawer: {
      name: "Drawer",
      props: {
        title: "Title",
        size: "Width of drawer box",
        direction: "Opening direction",
        modal: "Does it need a mask layer",
        autoClose: "Automatically close the popup after submitting the form",
        footer: "Whether to show operation buttons",
        beforeClose: "Callback before closing"
      },
      directionType: {
        ltr: "Left",
        rtl: "Right"
      }
    },
    fcTable: {
      name: "Table",
      props: {
        border: "Whether to display border",
        borderColor: "Border color",
        borderWidth: "Border width"
      }
    },
    fcTableGrid: {
      name: "Grid"
    },
    fcValue: {
      name: "Computed",
      empty: "Set the calculation formula in the right configuration"
    },
    fcSlot: {
      name: "Slot",
      empty: "Define&nbsp;{tag}&nbsp;slot",
      props: {
        name: "Slot Name"
      }
    },
    fcJson: {
      name: "Dynamic",
      empty: "Populate a zone by loading&nbsp;{tag}&nbsp;rules",
      props: {
        _loadType: "JSON Rules",
        type: "Type"
      }
    },
    lineChart: {
      name: "Line Chart",
      data: "Chart Data",
      pieTypeOpt: {
        pie: "Pie",
        doughnut: "Doughnut",
        "half-doughnut": "Half-doughnut"
      },
      funnelSortOpt: {
        descending: "descending",
        ascending: "ascending"
      },
      props: {
        title: "Chart name",
        subtitle: "Chart introduction",
        valueFormat: "Format numerical display",
        stack: "Whether to stack when there are multiple columns of data",
        smooth: "Whether the line transitions smoothly",
        showLegend: "Whether to display the mark",
        showSeriesLabel: "Whether to display the value",
        barBackgroundColor: "Column background color",
        funnelSort: "Sort method",
        pieType: "Shape",
        min: "Minimum value",
        max: "Maximum value",
        value: "Number",
        indicator: "Indicator",
        loadOptions: "Initialization"
      }
    },
    areaChart: {
      name: "Area Chart"
    },
    barChart: {
      name: "Bar Chart"
    },
    customChart: {
      name: "Custom Chart"
    },
    funnelChart: {
      name: "Funnel Chart"
    },
    gaugeChart: {
      name: "Gauge Chart"
    },
    pieChart: {
      name: "Pie Chart"
    },
    radarChart: {
      name: "Radar Chart"
    },
    scatterChart: {
      name: "Scatter Chart"
    },
    stripeChart: {
      name: "Horizontal bar Chart"
    },
    fcInlineForm: {
      name: "Inline"
    },
    group: {
      name: "Subform",
      props: {
        disabled: "Disabled",
        syncDisabled: "Whether to force synchronization of the disabled state with the subform",
        expand: "Set the default expansion items",
        button: "Whether to display the operation button",
        sortBtn: "Whether to display the sort button",
        min: "Set the minimum number of items to add",
        max: "Set the maximum number of items to add"
      }
    },
    html: {
      name: "HTML",
      props: {
        formCreateChild: "Content"
      }
    },
    input: {
      name: "Input",
      event: {
        change: "Triggered when the value changes, when the component loses focus or the user presses Enter"
      },
      props: {
        type: "Type",
        maxlength: "Maximum input length",
        minlength: "Minimum input length",
        placeholder: "Placeholder",
        clearable: "Whether to display the clear button",
        disabled: "Disabled",
        readonly: "Readonly"
      }
    },
    inputNumber: {
      name: "InputNumber",
      props: {
        precision: "Precision of input value",
        min: "Set the minimum value allowed for the counter",
        max: "Set the maximum allowed value of the counter",
        step: "Step",
        stepStrictly: "Whether only multiples of step can be entered",
        disabled: "Disabled",
        controls: "Whether to use control buttons",
        controlsPosition: "Control button position",
        placeholder: "Placeholder"
      }
    },
    password: {
      name: "Password",
      event: {
        change: "Triggered when the value changes, when the component loses focus or the user presses Enter"
      },
      props: {
        disabled: "Disabled",
        readonly: "Readonly",
        maxlength: "Maximum input length",
        minlength: "Minimum input length",
        placeholder: "Placeholder",
        clearable: "Whether to display the clear button"
      }
    },
    radio: {
      name: "Radio",
      props: {
        input: "Whether to fill in",
        disabled: "Disabled",
        type: "Type",
        textColor: "Text color when button form is activated",
        fill: "Fill color and border color when the button form is activated"
      }
    },
    rate: {
      name: "Rate",
      props: {
        max: "Maximum score",
        disabled: "Disabled",
        allowHalf: "Whether to allow half selection",
        voidColor: "Color of the icon when not selected",
        disabledVoidColor: "The color of the icon when it is not selected when read-only",
        voidIconClass: "Class name of the icon when not selected",
        disabledVoidIconClass: "The class name of the icon when it is not selected when read-only",
        showScore: "Whether to display the current score",
        textColor: "Color of auxiliary text",
        scoreTemplate: "Score display template"
      }
    },
    select: {
      name: "Select",
      event: {
        removeTag: "Triggered when tag is removed in multi-select mode"
      },
      props: {
        multiple: "Whether there are multiple selections",
        disabled: "Disabled",
        clearable: "Whether the option can be cleared",
        collapseTags: "Whether to display the selected value as text during multi-selection",
        multipleLimit: "The maximum number of items that the user can select when multiple-selecting, if it is 0, there is no limit",
        placeholder: "Placeholder",
        filterable: "Is it searchable",
        allowCreate: "Whether users are allowed to create new entries",
        noMatchText: "Text displayed when no search conditions match",
        noDataText: "Text displayed when option is empty",
        reserveKeyword: "When multiple selections are searchable, whether to retain the current search keyword after selecting an option",
        defaultFirstOption: "Press Enter in the input box and select the first matching item",
        remote: "Whether the options are loaded remotely from the server",
        remoteMethod: "Custom remote search methods"
      }
    },
    slider: {
      name: "Slider",
      props: {
        min: "Minimum value",
        max: "Maximum value",
        disabled: "Disabled",
        step: "Step",
        showInput: "Whether to display the input box, it is only valid during non-range selection",
        showInputControls: "Whether to display the control buttons of the input box when the input box is displayed",
        showStops: "Whether to display discontinuities",
        range: "Whether it is a range selection",
        vertical: "Whether portrait mode",
        height: "Slider height, required in portrait mode"
      }
    },
    space: {
      name: "Space"
    },
    stepForm: {
      name: "StepForm",
      event: {
        next: "Triggered when the next button is clicked"
      },
      props: {
        autoValidate: "Automatically validate the form before entering the next step",
        "stepsProps>alignCenter": "Align center",
        "stepsProps>simple": "Whether to apply simple style"
      }
    },
    stepFormItem: {
      name: "StepFormItem",
      props: {
        title: "Title",
        description: "Description"
      }
    },
    subForm: {
      name: "Group",
      props: {
        disabled: "Disabled",
        syncDisabled: "Whether to force synchronization of the disabled state with the subform"
      }
    },
    switch: {
      name: "Switch",
      slots: {
        "active-action": "Contents when opened",
        "inactive-action": "Content when closed"
      },
      props: {
        disabled: "Disabled",
        width: "Width (px)",
        activeText: "Text description when opening",
        inactiveText: "Text description when closing",
        activeValue: "Value when opening",
        inactiveValue: "Value when closed",
        activeColor: "Background color when opening",
        inactiveColor: "Background color when closed"
      }
    },
    tableForm: {
      name: "TableForm",
      props: {
        disabled: "Disabled",
        filterEmptyColumn: "Whether to filter empty rows",
        max: "Maximum number of rows to add, if 0, there is no limit"
      }
    },
    nestedTableForm: {
      name: "NestedForm",
      props: {
        disabled: "Disabled",
        max: "Maximum number of rows to add, if 0, there is no limit",
        nestedMax: "Maximum number of rows that can be added to a subform, if 0, there is no limit"
      }
    },
    infiniteTableForm: {
      name: "InfiniteForm",
      props: {
        disabled: "Disabled",
        childrenField: "Set the field name of the child",
        max: "Maximum number of rows to add, if 0, there is no limit",
        layerMax: "Maximum number of layers to add, if 0, there is no limit"
      }
    },
    nestedSubTableForm: {
      name: "SubTableForm"
    },
    tableFormColumn: {
      name: "TableFormColumn",
      label: "TableFormColumn",
      props: {
        label: "Title",
        width: "Width",
        color: "Color",
        required: "Whether to display required asterisks"
      }
    },
    dataTable: {
      name: "DataTable",
      handle: "Handle",
      click: "onClick",
      filter: "Filter",
      event: {
        cellMouseEnter: "This event is triggered when the cell is hovered into",
        cellMouseLeave: "This event is triggered when the cell is hovered out",
        handleClick: "This event is triggered when the operation button is clicked",
        rowClick: "This event is triggered when a row is clicked",
        rowDblclick: "This event is triggered when a row is double-clicked",
        headerClick: "This event is triggered when the header of a column is clicked",
        filterChange: "This event is triggered when the filter condition changes",
        expandChange: "This event is triggered when the user expands or closes a row",
        sortChange: "This event is triggered when the sort condition of the table changes",
        selectionChange: "Triggered when selection changes"
      },
      button: {
        title: "Operation Button",
        btn: "Configure operation button",
        link: "Link",
        round: "Round",
        plain: "Plain",
        disabled: "Disabled"
      },
      column: {
        title: "Table list",
        btn: "Configuration table list",
        prop: "Field",
        sort: "Sort"
      },
      format: {
        default: "Default",
        tag: "Tag",
        image: "Image",
        custom: "Custom"
      },
      fixed: {
        default: "default",
        left: "left",
        right: "right"
      },
      sortable: {
        disabled: "normal",
        default: "default",
        custom: "interface"
      },
      props: {
        _optionType: "Table data",
        "page>totalField": "Field name of total number of entries in interface response data",
        "page>dataField": "Field name of list data in interface response data",
        "page>orderField": "Parameter name for sorting when requesting the interface",
        "page>orderByField": "Parameter name of the sorting method when requesting the interface",
        "page>pageField": "Parameter name of the page number when requesting the interface (Page)",
        "page>pageSizeField": "Parameter name of the number of entries when requesting the interface (Page)",
        column: "Column management",
        showSummary: "Whether to display total row at the end of table",
        selection: "Whether to display multiple selection box",
        size: "Size",
        rowKey: "Key of row data",
        emptyText: "Text content displayed when empty data",
        height: "Height",
        index: "Display line number",
        stripe: "Show zebra stripe",
        border: "Border",
        defaultExpandAll: "Expand all lines by default",
        button: "Operation button",
        page: "Page",
        "button>column": "Button management",
        "button>label": "Operation",
        "button>fixed": "Position",
        "button>width": "Column width",
        "page>position": "Position",
        "page>props>pageSize": "Number of items displayed on each page",
        "page>props>small": "Use small pagination style",
        "page>props>background": "Add a background color to the pagination button"
      },
      requiredName: "Please enter the button name",
      requiredKey: "Please enter the button ID",
      requiredLabel: "Please enter the title",
      requiredRender: "Please enter the render function"
    },
    text: {
      name: "Text",
      props: {
        formCreateChild: "Content"
      }
    },
    textarea: {
      name: "Textarea",
      event: {
        change: "Triggered when the value changes, when the component loses focus or the user presses Enter"
      },
      props: {
        disabled: "Disabled",
        readonly: "Readonly",
        maxlength: "Maximum input length",
        minlength: "Minimum input length",
        showWordLimit: "Whether to display word count statistics",
        placeholder: "Placeholder",
        rows: "Number of input box rows",
        autosize: "Whether the height is adaptive"
      }
    },
    timePicker: {
      name: "Time",
      "HH:mm:ss": "HH:mm:ss",
      "HH:mm": "HH:mm",
      props: {
        __format: "Format",
        pickerOptions: "Options specific to the current time and date picker",
        readonly: "Readonly",
        disabled: "Disabled",
        editable: "Text box can be input",
        clearable: "Whether to display the clear button",
        placeholder: "Placeholder content for non-range selection",
        startPlaceholder: "Placeholder content for the start date when selecting the range",
        endPlaceholder: "Placeholder content for the end date when selecting the range",
        isRange: "Whether to select a time range",
        arrowControl: "Whether to use arrows for time selection",
        align: "Align"
      }
    },
    tree: {
      name: "Tree",
      event: {
        nodeClick: "Triggered when the node is clicked",
        nodeContextmenu: "This event will be triggered when a node is right-clicked",
        checkChange: "Triggered when the check box is clicked",
        check: "Triggered after clicking the node checkbox",
        currentChange: "Event triggered when the currently selected node changes",
        nodeExpand: "Event triggered when a node is expanded",
        nodeCollapse: "Event triggered when a node is closed",
        nodeDragStart: "Event triggered when a node starts dragging",
        nodeDragEnter: "Event triggered when dragging into other nodes",
        nodeDragLeave: "Event triggered when dragging leaves a node",
        nodeDragOver: "Event triggered when dragging a node",
        nodeDragEnd: "Event triggered when drag ends",
        nodeDrop: "Event triggered when drag and drop is successfully completed"
      },
      props: {
        emptyText: "Text displayed when the content is empty",
        props: "Options",
        renderAfterExpand: "Whether to render its child nodes after expanding a tree node for the first time",
        defaultExpandAll: "Whether to expand all nodes by default",
        expandOnClickNode: "Whether to expand or contract the node when clicking the node, if it is false, the node will only be expanded or contracted when the arrow icon is clicked. ",
        checkOnClickNode: "Whether to select the node when clicking the node",
        autoExpandParent: "Whether to automatically expand the parent node when expanding the child node",
        checkStrictly: "When the check box is displayed, whether the parent and child are strictly not related to each other should be strictly followed",
        accordion: "Whether to open only one sibling tree node for expansion at a time",
        indent: "Horizontal indent (px) between adjacent level nodes",
        nodeKey: "Each tree node is used as an attribute for unique identification, and the entire tree should be unique"
      }
    },
    upload: {
      name: "Upload",
      info: "After a successful upload, assign the returned URL to file.url or the result to file.value for use in subsequent form submissions.",
      event: {
        remove: "Triggered when a file is removed from the file list",
        preview: "Triggered when clicking an uploaded file in the file list",
        error: "Triggered when file upload fails",
        progress: "Triggered when file is uploaded",
        exceed: "Triggered when the limit is exceeded"
      },
      slots: {
        tip: "Description"
      },
      props: {
        listType: "Upload type",
        multiple: "Whether multiple selection of files is supported",
        action: "Upload address (required)",
        beforeUpload: "Triggered before uploading a file",
        onSuccess: "Triggered when the upload is successful",
        beforeRemove: "Triggered before deleting a file",
        headers: "Set upload request headers",
        data: "Extra parameters attached when uploading",
        name: "Uploaded file field name",
        withCredentials: "Support sending cookie credential information",
        accept: "Accept uploaded file types",
        autoUpload: "Whether to upload the file immediately after selecting it",
        disabled: "Disabled",
        limit: "Maximum number of uploads allowed"
      }
    },
    audioBox: {
      name: "AudioPlayer",
      preloadOpt: {
        auto: "Auto",
        metadata: "Metadata",
        none: "None"
      },
      event: {
        pause: "Triggered when audio playback is paused",
        play: "Triggered when audio starts playing",
        ended: "Triggered when the audio playback ends"
      },
      props: {
        src: "Audio path",
        type: "Audio Type",
        autoplay: "Whether to play automatically",
        loop: "Whether to loop playback",
        muted: "Whether to mute",
        controls: "Whether to display the control bar",
        preload: "Preload"
      }
    },
    barCodeBox: {
      name: "Barcode",
      props: {
        value: "Content",
        format: "Barcode type",
        width: "Width of a single bar",
        height: "Height of the barcode",
        displayValue: "Whether to display content",
        fontSize: "Set text size",
        textPosition: "Set text position",
        textAlign: "Set text alignment",
        textMargin: "Set text margin",
        background: "Background color",
        lineColor: "Line color"
      }
    },
    iframeBox: {
      name: "Iframe",
      event: {
        load: "Trigger after page loading"
      },
      loadingOpt: {
        eager: "Load immediately",
        lazy: "Lazy loading"
      },
      props: {
        src: "Website URL",
        loading: "Loading method"
      }
    },
    qrCodeBox: {
      name: "QR Code",
      circleTypeOpt: {
        square: "Square",
        dots: "Dots",
        rounded: "Rounded",
        classy: "Classy"
      },
      props: {
        data: "Content",
        image: "Center image path",
        width: "Width of QR code",
        height: "Height of QR code",
        circleType: "Dots type",
        circleColor: "Dots color"
      }
    },
    signaturePad: {
      name: "Signature",
      props: {
        penColor: "Line color"
      }
    },
    videoBox: {
      name: "Video",
      event: {
        error: "Triggered when video loading fails",
        pause: "Triggered when video playback is paused",
        play: "Triggered when video starts playing",
        ended: "Triggered after video playback ends"
      },
      props: {
        src: "Video path",
        type: "Video type",
        autoplay: "Whether to play automatically",
        loop: "Whether to play in a loop",
        isLive: "Whether to broadcast live",
        controls: "Whether to display the control bar",
        withCredentials: "Whether to carry credentials"
      }
    }
  },
  tmp: {
    duration: "duration",
    chineseAmount: "chineseAmount",
    col3: "3-Col",
    col4: "4-Col",
    table43: "4x3Table"
  }
};
export {
  e as default
};
