<template>
  <div class="dr-editor">
    <Toolbar
      style="border-bottom: 1px solid #ccc"
      :editor="editorRef"
      :defaultConfig="toolbarConfig"
      :mode="mode"
    />
    <Editor
      style="height: 500px; overflow-y: hidden"
      v-model="modelValue"
      :defaultConfig="editorConfig"
      :mode="mode"
      :autofocus="false"
      @on-created="handleCreated"
    />
  </div>
</template>

<script setup lang="ts">
import '@wangeditor/editor/dist/css/style.css' // 引入 css

import { onBeforeUnmount, ref, shallowRef, computed } from 'vue'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'

type PropsType = {
  placeholder?: string
}

const props = withDefaults(defineProps<PropsType>(), {
  placeholder: '请输入内容...'
})

// 编辑器实例，必须用 shallowRef
const editorRef = shallowRef()

const mode = ref('default')

const modelValue = defineModel<string>({ required: false, default: '' })

const toolbarConfig = {}
const editorConfig = computed(() => {
  return {
    placeholder: props.placeholder
  }
})

// 组件销毁时，也及时销毁编辑器
onBeforeUnmount(() => {
  const editor = editorRef.value
  if (editor == null) return
  editor.destroy()
})

const handleCreated = (editor) => {
  editorRef.value = editor // 记录 editor 实例，重要！
}
</script>

<style scoped lang="scss">
.dr-editor {
  border: 1px solid #ccc;
}
</style>
