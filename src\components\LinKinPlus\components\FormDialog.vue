<!-- 弹窗Form -->
<script lang="jsx">
import { ref } from 'vue'
import Form from './Form.vue'
import { defineComponent, watch, nextTick } from 'vue'

export default defineComponent({
  name: 'lk-form-dialog',
  props: {
    visible: { type: Boolean, default: () => false },
    submitText: { type: String, default: () => '确定' },
    data: { type: Object, default: () => ({}) },
    formProps: { type: Object, default: () => ({}) }
  },
  emits: ['update:visible', 'close', 'submit'],
  setup(props, { emit, attrs, slots }) {
    const submitting = ref(false)
    const formRef = ref('')
    const handleSubmit = () => {
      formRef.value.validate((valid) => {
        if (valid) {
          submitting.value = true
          emit('submit')
          setTimeout(() => {
            submitting.value = false
          }, 1500)
        }
      })
    }
    const close = () => {
      emit('update:visible', false)
      emit('close')
    }

    watch(
      () => props.visible,
      (status) => {
        if (status) {
          nextTick(() => {
            formRef.value.resetFields()
          })
        }
      }
    )

    return () => (
      <el-dialog
        v-model={props.visible}
        class="form-dialog"
        title={props.data.id ? '编辑' : '新增'}
        onClose={close}
        append-to-body
        destroy-on-close
        close-on-click-modal={false}
        draggable={true}
        width="550px"
        {...attrs}
      >
        <Form
          ref={formRef}
          v-model={props.data}
          label-position="top"
          v-slots={slots}
          class={['lk-form-default']}
          {...props.formProps}
        />
        {props.formProps?.disabled ? null : (
          <div class="dialog-footer">
            <el-button type="primary" plain onClick={handleSubmit} disabled={submitting.value}>
              {submitting.value ? '提交中...' : props.submitText}
            </el-button>
          </div>
        )}
      </el-dialog>
    )
  }
})
</script>

<style lang="scss">
.form-dialog {
  :deep(.dialog-footer) {
    margin-top: 14px;
    text-align: center;
  }
}
</style>
