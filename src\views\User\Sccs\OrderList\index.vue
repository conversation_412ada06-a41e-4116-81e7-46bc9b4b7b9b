<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery" />
  </ContentWrap>

  <ContentWrap>
    <Table :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @handle-table="handleTable" />
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

</template>

<script setup lang="ts">
import businessConfig from './components/index.config'
import { OrderApi } from '@/api/user/order';

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as any[]
})

/** 运营端行业管理 列表 */
defineOptions({ name: 'SccsList' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  keyword = ''
  typeCode = null
  industryId = null
  status = null
  createTime = []
  updateTime = []
}
const queryParams = reactive(new DefaultQuery())

const handleQuery = (type: string) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'add':
      openForm('create')
      break;
    default:
      break;
  }
}

const handleTable = (type: string, row: any) => {
  switch (type) {
    case 'detail':
      window.open(import.meta.env.VITE_TRADE_BASE_URL + `/orderDetail?sccsName=mysccs5&sccsId=${row.sccsId}&templateId=${row.templateId}&orderId=${row.id}&orderMark=${row.orderMark}&coopTeamMark=2&accessToken=${row.accessToken}&refreshToken=${row.refreshToken}&expiresTime=${row.expiresTime}`, "_blank");
      break;
    default:
      break;

  }
}
/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await OrderApi.getOrderPage(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, true)
}

const designRef = ref()
const openDesign = (id: number, sccsName: string, sccsId: string) => {
  designRef.value.open(id, sccsName, sccsId)
}


/** 初始化 **/
onMounted(() => {
  getList()
})

</script>
