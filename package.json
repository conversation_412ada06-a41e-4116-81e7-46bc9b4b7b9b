{"name": "linkincrease-admin", "version": "2.0.0-snapshot", "description": "基于vue3、vite4、element-plus、typesScript", "author": "xingyu", "private": false, "scripts": {"i": "pnpm install", "dev": "vite --mode dev", "my": "vite --mode my", "dev-server": "vite --mode dev", "ts:check": "vue-tsc --noEmit", "build:local-dev": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode local-dev", "build:dev": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode local-dev", "build:test": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode test", "build:stage": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode stage", "build:prod": "node --max_old_space_size=8192 ./node_modules/vite/bin/vite.js build --mode prod", "serve:dev": "vite preview --mode dev", "serve:prod": "vite preview --mode prod", "preview": "pnpm build:local-dev && vite preview", "clean": "npx rimraf node_modules", "clean:cache": "npx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,scss,vue,html,md}\"", "lint:lint-staged": "lint-staged -c "}, "dependencies": {"@element-plus/icons-vue": "^2.1.0", "@form-create/component-wangeditor": "^3.1", "@form-create/designer": "^3.2.6", "@form-create/element-ui": "^3.2.12", "@form-create/utils": "^3.2.0", "@form-create/vant": "^3.2.12", "@iconify/iconify": "^3.1.1", "@videojs-player/vue": "^1.0.0", "@vueuse/core": "^10.6.1", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "array-move": "^4.0.0", "axios": "^1.6.1", "benz-amr-recorder": "^1.1.5", "bpmn-js-token-simulation": "^0.10.0", "camunda-bpmn-moddle": "^7.0.1", "codemirror": "^5.65.18", "codemirror-editor-vue3": "^2.8.0", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "diagram-js": "^12.8.0", "driver.js": "^1.3.1", "echarts": "^5.4.3", "echarts-wordcloud": "^2.1.0", "element-plus": "^2.8.0", "fast-xml-parser": "^4.3.2", "file-saver": "^2.0.5", "highlight.js": "^11.9.0", "jsencrypt": "^3.3.2", "jszip": "^3.10.1", "localforage": "^1.10.0", "lodash-es": "^4.17.21", "min-dash": "^4.1.1", "mitt": "^3.0.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "qrcode": "^1.5.3", "qs": "^6.11.2", "signature_pad": "^5.0.3", "steady-xml": "^0.1.0", "url": "^0.11.3", "vant": "^4.9.4", "video.js": "^7.21.5", "vue": "^3.3.8", "vue-dompurify-html": "^4.1.4", "vue-esign": "^1.1.4", "vue-i18n": "^9.6.5", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "vuedraggable": "^4.1.0", "web-storage-cache": "^1.1.1", "xml-js": "^1.6.11"}, "devDependencies": {"@commitlint/cli": "^18.4.1", "@commitlint/config-conventional": "^18.4.0", "@iconify/json": "^2.2.142", "@intlify/unplugin-vue-i18n": "^4.0.0", "@purge-icons/generated": "^0.9.0", "@types/lodash-es": "^4.17.11", "@types/node": "^20.9.0", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.10", "@typescript-eslint/eslint-plugin": "^6.11.0", "@typescript-eslint/parser": "^6.11.0", "@unocss/eslint-config": "^0.57.4", "@unocss/transformer-variant-group": "^0.57.4", "@vitejs/plugin-legacy": "^4.1.1", "@vitejs/plugin-vue": "^4.4.1", "@vitejs/plugin-vue-jsx": "^3.0.2", "autoprefixer": "^10.4.16", "bpmn-js": "8.9.0", "bpmn-js-properties-panel": "0.46.0", "consola": "^3.2.3", "eslint": "^8.53.0", "eslint-config-prettier": "^9.0.0", "eslint-define-config": "^1.24.1", "eslint-plugin-prettier": "^5.0.1", "eslint-plugin-vue": "^9.18.1", "lint-staged": "^15.1.0", "postcss": "^8.4.31", "postcss-html": "^1.5.0", "postcss-scss": "^4.0.9", "prettier": "^3.1.0", "rimraf": "^5.0.5", "rollup": "^4.4.1", "sass": "^1.69.5", "terser": "^5.24.0", "typescript": "^5.2.2", "unocss": "^0.57.4", "unplugin-auto-import": "^0.16.7", "unplugin-element-plus": "^0.8.0", "unplugin-vue-components": "^0.25.2", "vite": "^4.5.3", "vite-plugin-compression": "^0.5.1", "vite-plugin-ejs": "^1.6.4", "vite-plugin-eslint": "^1.8.1", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.9.2", "vite-plugin-svg-icons": "^2.0.1", "vite-plugin-top-level-await": "^1.3.1", "vite-plugin-vue-inspector": "^5.3.2", "vue-eslint-parser": "^9.3.2", "vue-tsc": "^1.8.22"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://gitee.com/yudaocode/yudao-ui-admin-vue3"}, "bugs": {"url": "https://gitee.com/yudaocode/yudao-ui-admin-vue3/issues"}, "homepage": "https://gitee.com/yudaocode/yudao-ui-admin-vue3", "packageManager": "pnpm@9.1.4", "engines": {"node": ">= 16.0.0", "pnpm": ">=8.6.0"}}