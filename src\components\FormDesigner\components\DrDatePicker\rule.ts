import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrDatePicker'
const label = '日期时间'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-date',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {
        defaultValueConfig: { type: 'none', content: '' },
        showType: 'date',
        placeholder: '请选择'
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(data, { t }) {
    return [
      {
        type: 'DrDefaultValue',
        field: 'defaultValueConfig',
        title: '默认内容',
        props: {
          currentField: data.field,
          componentName: name
        }
      },
      {
        type: 'select',
        title: '显示格式',
        field: 'showType',
        options: [
          { value: 'year', label: '年' },
          { value: 'month', label: '年-月' },
          { value: 'date', label: '年-月-日' },
          { value: 'datetime', label: '年-月-日-时-分' },
          { value: 'datetimesecond', label: '年-月-日-时-分-秒' }
        ]
      },
      {
        type: 'LanguageInput',
        title: '占位提示语',
        field: 'placeholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("placeholderEn", langValue);
            api.activeRule.props["placeholderEn"] = langValue;
          },
        },
      }
    ]
  }
}

export default Rule
