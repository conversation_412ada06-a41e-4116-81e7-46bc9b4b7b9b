<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery" />
  </ContentWrap>

  <ContentWrap>
    <Table ref="tableRef" :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @handle-table="handleTable" @SortChange="handleSortChange">
      <template #avatar>
        <el-table-column min-width="120" align="center" :label="t('admin_user_avatar')">
          <template #default="{ row }">
            <el-image v-if="row.avatar" :src="row.avatar" class="h-45px w-45px" @click="imagePreview(row.avatar)" />
            <span v-else>-</span>
          </template>
        </el-table-column>
      </template>
      <template #status>
        <el-table-column min-width="120" align="center" :label="t('admin_user_userStatus')">
          <template #default="{ row }">
            <el-switch :model-value="row.status" active-color="#409eff" :active-value="1" :inactive-value="0"
              :loading="statusLoading" @change="handleChangeStatus(row)" />
          </template>
        </el-table-column>
      </template>
    </Table>
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>
  <!-- 表单弹窗：添加/修改 -->
  <UpdateUserForm ref="formRef" @success="getList" />
</template>

<script setup lang="ts">
import { UserApi } from '@/api/user/userMsg'
import businessConfig from './components/index.config'
import { createImageViewer } from '@/components/ImageViewer'
import UpdateUserForm from './components/UpdateUserForm.vue'

const message = useMessage() // 消息弹窗
const { push } = useRouter() // 路由
const { t } = useI18n() // 国际化
const statusLoading = ref(false)

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as any[]
})

defineOptions({ name: 'UserList' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  keyword = ''
  status = null
  activate = null
  createTime = []
  latestLoginTime = []
  sortBy = ''
  descending: any = null
}
const queryParams = reactive(new DefaultQuery())
const tableRef = ref()
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id)
}
const handleChangeStatus = async (row) => {
  statusLoading.value = true
  try {
    // 确认的二次确认
    await message.confirm(row.status == 1 ? t('admin_common_sureOutContent') : t('admin_common_sureOnContent'), row.status == 1 ? t('admin_common_sureOutTip') : t('admin_common_sureOnTip'))
    const data = { userId: row.id }
    data['status'] = row.status == 1 ? 0 : 1
    await UserApi.setStatus(data)
    getList()
  } finally {
    statusLoading.value = false
  }
}
const handleSortChange = (data: any) => {
  if (data.order == null) {
    queryParams.sortBy = ''
    queryParams.descending = null
  } else {
    queryParams.sortBy = data.prop
    queryParams.descending = data.order == 'descending'
  }
  getList()
}

const imagePreview = (imgUrl: string) => {
  createImageViewer({
    urlList: [imgUrl]
  })
}

const handleQuery = (type: string, row: any) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      tableRef.value.clearSort()
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'export':
      console.log('export')
      break;
    case 'add':
      openForm('create')
      break;
    default:
      break;
  }
}

const handleTable = (type: string, row: any) => {
  switch (type) {
    case 'edit':
      openForm('update', row.id)
      break;
    case 'detail':
      push('/user/userDetail?id=' + row.id)
      break;
    default:
      break;
  }
}
/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await UserApi.getUserPage(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})
</script>
