<!-- 表格-->
<script lang="jsx">
import { getFormatValue } from '../render/utils'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'lk-table',
  props: {
    columns: { type: Array, default: () => [] }, //表格配置的列
    btnList: { type: Array, default: () => [] }, //表格配置操作按钮
    data: { type: Array, default: () => [] }, //表格数据
    loading: { type: Boolean, default: () => false }, //表格loading
    align: { type: String, default: '' } //列是否居中,不能传到attrs,否则会给div.el-table默认text-align=center,导致滚动条错位
  },
  emits: ['handleTable'],
  setup(props, { attrs, slots, emit }) {
    const handleTable = (type, row, index) => {
      emit('handleTable', type, row, index)
    }

    const getColumns = (list = []) =>
      list.map(c => {
        const { clSlots = {}, children, slotName, ...others } = c
        if (slotName) { //自定义插槽
          return (
            slots[slotName]?.()
          )
        } else {
          if (children) {
            clSlots.default = clSlots.default ?? (() => getColumns(children))
          } else {
            others.formatter = others.formatter ?? (row => getFormatValue(c, row)) //格式化
          }
          return <el-table-column min-width={props['min-width'] || 120} key={c.prop} align={props.align || 'center'} show-overflow-tooltip={true} {...others} v-slots={clSlots} />
        }
      })

    const getBtnLists = (btnList = []) => {
      if (!btnList.length) return
      return <el-table-column
        fixed="right"
        label={useI18n().t('admin_common_operate')}
        align='center'
        width={btnList.reduce((t, c) => (t += c.width ?? 56), 10)}
        v-slots={{
          default: ({ row, $index }) =>
            btnList.map(c =>
              c.render ? (
                c.render(row, $index)
              ) : (
                  <el-button {...c} onClick={() => handleTable(c.key, row, $index)}>
                  {c.label}
                </el-button>
              )
            )
        }}
      />
    }

    const handleSizeChange = (type, data, index) => {
      if (type === 'edit') {
        this.$emit('click', type, data, index)
      } else {
        this.$emit('click', type, data)
      }
    }

    return () => (
      <el-table ref='table' border data={props.data} v-loading={props.loading} {...attrs}>
        {slots.prefix?.()}
        {getColumns(props.columns)}
        {slots.default?.()}
        {getBtnLists(props.btnList)}
        {slots.suffix?.()}
      </el-table>
    )
  },
  methods: {
    doLayout() {
      this.$refs.table.doLayout()
    },
    clearSort() {
      this.$refs.table.clearSort()
    }
  }
})
</script>
