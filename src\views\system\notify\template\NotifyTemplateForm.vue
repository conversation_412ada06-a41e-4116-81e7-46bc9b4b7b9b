<template>
  <Dialog :title="dialogTitle" v-model="dialogVisible">
    <el-form ref="formRef" :model="formData" :rules="formRules" label-width="140px" v-loading="formLoading">
      <el-form-item label="模版编码" prop="code">
        <el-input v-model="formData.code" placeholder="请输入模版编码" />
      </el-form-item>
      <el-form-item label="模板名称" prop="name">
        <el-input v-model="formData.name" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="模板标题" prop="title">
        <el-input v-model="formData.title" placeholder="请输入模版名称" />
      </el-form-item>
      <el-form-item label="产品端" prop="notifyType" @change="handleTypeChange">
        <el-select v-model="formData.notifyType" placeholder="请选择产品端" clearable class="!w-240px">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE.SYSTEM_NOTIFY_TEMPLATE_TYPE)" :key="index"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="消息类型" prop="type" v-if="formData.notifyType">
        <el-select v-model="formData.type" placeholder="请选择消息类型" clearable class="!w-240px">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE[formData.notifyType.toUpperCase()])"
            :key="index" :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <el-form-item label="模板语言" prop="lan">
        <el-select v-model="formData.lan" placeholder="请选择语言" clearable class="!w-240px">
          <el-option v-for="(dict, index) in getStrDictOptions(DICT_TYPE.COMMON_LANGUAGE)" :key="index"
            :label="dict.label" :value="dict.value" />
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="发件人名称" prop="nickname">
        <el-input v-model="formData.nickname" placeholder="请输入发件人名称" />
      </el-form-item> -->
      <el-form-item label="模板内容" prop="content">
        <Editor v-model:model-value="formData.content" height="250px" style="width: 650px;" />
      </el-form-item>
      <el-form-item label="跳转链接" prop="link">
        <el-input v-model="formData.link" placeholder="请输入发件人名称" />
      </el-form-item>
      <el-form-item label="开启状态" prop="status">
        <el-radio-group v-model="formData.status">
          <el-radio v-for="dict in getIntDictOptions(DICT_TYPE.COMMON_STATUS)  " :key="dict.value" :label="dict.value">
            {{ dict.label }}
          </el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="formData.remark" placeholder="请输入备注" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="submitForm" type="primary" :disabled="formLoading">确 定</el-button>
      <el-button @click="dialogVisible = false">取 消</el-button>
    </template>
  </Dialog>
</template>
<script lang="ts" setup>
import { DICT_TYPE, getIntDictOptions, getStrDictOptions } from '@/utils/dict'
import * as NotifyTemplateApi from '@/api/system/notify/template'
import { CommonStatusEnum } from '@/utils/constants'
const message = useMessage() // 消息弹窗

const dialogVisible = ref(false) // 弹窗的是否展示
const dialogTitle = ref('') // 弹窗的标题
const formLoading = ref(false) // 表单的加载中：1）修改时的数据加载；2）提交的按钮禁用
const formType = ref('') // 表单的类型
const formData = ref<NotifyTemplateApi.NotifyTemplateVO>({
  id: undefined,
  name: '',
  nickname: '',
  code: '',
  content: '',
  notifyType: undefined,
  type: undefined,
  title: '',
  lan: undefined,
  params: '',
  status: CommonStatusEnum.ENABLE,
  remark: '',
  link: ''
})
const formRules = reactive({
  type: [{ required: true, message: '消息类型不能为空', trigger: 'change' }],
  status: [{ required: true, message: '开启状态不能为空', trigger: 'blur' }],
  code: [{ required: true, message: '模板编码不能为空', trigger: 'blur' }],
  name: [{ required: true, message: '模板名称不能为空', trigger: 'blur' }],
  lan: [{ required: true, message: '语言不能为空', trigger: 'blur' }],
  content: [{ required: true, message: '模板内容不能为空', trigger: 'blur' }]

})
const formRef = ref() // 表单 Ref

/** 打开弹窗 */
const open = async (type: string, id?: number) => {
  dialogVisible.value = true
  dialogTitle.value = type == 'create' ? '新增通知模板' : '编辑通知模板'
  formType.value = type
  resetForm()
  // 修改时，设置数据
  if (id) {
    formLoading.value = true
    try {
      formData.value = await NotifyTemplateApi.getNotifyTemplate(id)
    } finally {
      formLoading.value = false
    }
  }
}
defineExpose({ open }) // 提供 open 方法，用于打开弹窗

const handleTypeChange = () => {
  formData.value.type = undefined
}

/** 提交表单 */
const emit = defineEmits(['success']) // 定义 success 事件，用于操作成功后的回调
const submitForm = async () => {
  // 校验表单
  if (!formRef) return
  const valid = await formRef.value.validate()
  if (!valid) return
  formLoading.value = true
  try {
    const data = formData.value as unknown as NotifyTemplateApi.NotifyTemplateVO
    if (formType.value === 'create') {
      await NotifyTemplateApi.createNotifyTemplate(data)
      message.success('新增成功')
    } else {
      await NotifyTemplateApi.updateNotifyTemplate(data)
      message.success('修改成功')
    }
    dialogVisible.value = false
    // 发送操作成功的事件
    emit('success')
  } finally {
    formLoading.value = false
  }
}
/** 重置表单 */
const resetForm = () => {
  formData.value = {
    id: undefined,
    name: '',
    nickname: '',
    code: '',
    content: '',
    type: undefined,
    params: '',
    status: CommonStatusEnum.ENABLE,
    remark: ''
  }
  formRef.value?.resetFields()
}
</script>
