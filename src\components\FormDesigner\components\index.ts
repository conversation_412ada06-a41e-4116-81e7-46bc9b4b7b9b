import DrCard from './DrCard/index.vue'
import DrDivider from './DrDivider/index.vue'
import DrTabs from './DrTabs/index.vue'
import DrTableForm from './DrTableForm/TableForm.vue'
import DrTableFormColumnsEditor from './DrTableForm/TableFormColumnsEditor.vue'
import DrTableFormColumn from './DrTableFormColumn/index.vue'
import DrInput from './DrInput/index.vue'
import DrTextarea from './DrTextarea/index.vue'
import DrInputNumber from './DrInputNumber/index.vue'
import DrPercentage from './DrPercentage/index.vue'
import DrRadio from './DrRadio/index.vue'
import DrCheckbox from './DrCheckbox/index.vue'
import DrDatePicker from './DrDatePicker/index.vue'
import DrImagesUpload from './DrImagesUpload/index.vue'
import DrFilesUpload from './DrFilesUpload/index.vue'
import DrRate from './DrRate/index.vue'
import DrEditor from './DrEditor/index.vue'
import DrLocation from './DrLocation/index.vue'
import DrAddress from './DrAddress/index.vue'
import DrPlaceholder from './DrPlaceholder/index.vue'

// 高级组件
import DrRelateCard from './DrRelateCard/index.vue'
import DrRelateCardView from './DrRelateCard/view.vue'
import DrRelateRuleTable from './DrRelateCard/ruleTable.vue'
import DrFormulas from './DrFormulas/index.vue'
import DrFormulasEdit from './DrFormulas/formulasEdit.vue'
import DrSCCSMemberSelect from './DrSCCSMemberSelect/index.vue'
import DrSCCSGroupMemberSelect from './DrSCCSGroupMemberSelect/index.vue'
import DrSignature from './DrSignature/index.vue'
import DrExchangeRates from './DrExchangeRates/index.vue'

// common
import DrTableOptions from './common/DrTableOptions/index.vue'
import FormulasFormatForm from './DrFormulas/formatForm.vue'
import DrLayoutSelect from './common/DrLayoutSelect/index.vue'
import DrDefaultValue from './common/DrDefaultValue/index.vue'
import DrInputRadio from "./common/DrInputRadio/index.vue"


import DrCardRule from './DrCard/rule'
import DrDividerRule from './DrDivider/rule'
import DrTabsRule from './DrTabs/rule'
import DrTableFormRule from './DrTableForm/tableForm'
import DrTableFormColumnRule from './DrTableFormColumn/rule'
import DrInputRule from './DrInput/rule'
import DrTextareaRule from './DrTextarea/rule'
import DrInputNumberRule from './DrInputNumber/rule'
import DrPercentageRule from './DrPercentage/rule'
import DrRadioRule from './DrRadio/rule'
import DrCheckboxRule from './DrCheckbox/rule'
import DrDatePickerRule from './DrDatePicker/rule'
import DrImagesUploadRule from './DrImagesUpload/rule'
import DrFilesUploadRule from './DrFilesUpload/rule'
import DrRateRule from './DrRate/rule'
import DrEditorRule from './DrEditor/rule'
import DrLocationRule from './DrLocation/rule'
import DrAddressRule from './DrAddress/rule'
import DrPlaceholderRule from './DrPlaceholder/rule'
// 高级组件
import DrRelateCardRule from './DrRelateCard/rule'
import DrFormulasRule from './DrFormulas/rule'
import DrSCCSMemberSingleSelectRule from './DrSCCSMemberSelect/single'
import DrSCCSMemberMultipleSelectRule from './DrSCCSMemberSelect/multiple'
import DrSCCSGroupMemberSingleSelectRule from './DrSCCSGroupMemberSelect/single'
import DrSCCSGroupMemberMultipleSelectRule from './DrSCCSGroupMemberSelect/multiple'
import DrSignatureRule from './DrSignature/rule'
import DrExchangeRatesRule from './DrExchangeRates/rule'
// common rules
import ColRule from './common/rule/col'
import TabsRule from './common/rule/tabs'
import TabPaneRule from './common/rule/tabPane'
import TableFormRule from './common/rule/tableForm'
import TableFormColumnRule from './common/rule/tableFormColumn'
import DrFormVisibleSetting from './common/DrFormVisibleSetting/index.vue'

export const components = {
  DrCard,
  DrDivider,
  DrTabs,
  DrTableForm,
  DrTableFormColumnsEditor,
  DrFormVisibleSetting,
  DrTableFormColumn,
  DrInput,
  DrTextarea,
  DrInputNumber,
  DrPercentage,
  DrRadio,
  DrCheckbox,
  DrDatePicker,
  DrImagesUpload,
  DrFilesUpload,
  DrLocation,
  DrAddress,
  DrEditor,
  DrRate,
  DrPlaceholder,
  // 高级组件
  DrRelateCard,
  DrRelateCardView,
  DrRelateRuleTable,
  DrFormulas,
  DrFormulasEdit,
  DrSCCSMemberSelect,
  DrSCCSGroupMemberSelect,
  DrSignature,
  DrExchangeRates,
  // common
  DrTableOptions,
  FormulasFormatForm,
  DrLayoutSelect,
  DrDefaultValue,
  DrInputRadio
}

export const rules = [
  DrCardRule,
  DrDividerRule,
  DrTabsRule,
  DrTableFormRule,
  DrTableFormColumnRule,
  DrInputRule,
  DrTextareaRule,
  DrInputNumberRule,
  DrPercentageRule,
  DrRadioRule,
  DrCheckboxRule,
  DrDatePickerRule,
  DrImagesUploadRule,
  DrFilesUploadRule,
  DrLocationRule,
  DrAddressRule,
  DrEditorRule,
  DrRateRule,
  DrPlaceholderRule,
  // 高级组件
  DrRelateCardRule,
  DrFormulasRule,
  DrSCCSMemberSingleSelectRule,
  DrSCCSMemberMultipleSelectRule,
  DrSCCSGroupMemberSingleSelectRule,
  DrSCCSGroupMemberMultipleSelectRule,
  DrSignatureRule,
  DrExchangeRatesRule,
  DrInputRadio,
  // COMMON rule
  ColRule,
  TabsRule,
  TabPaneRule,
  TableFormRule,
  TableFormColumnRule
]

export default {
  components,
  rules
}
