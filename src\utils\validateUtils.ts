const { t } = useI18n() // 国际化


export class ValidUtil {
  /**
   * 确认密码验证
   */
  static validateConfirmPasswordFn(form: any, formRef: any, t: any) {
    return (rule: any, value: any, callback: any) => {
      if (value !== form.password) {
        callback(new Error(t('sys.login.diffPwd')))
      } else {
        callback()
      }
    }
  }
  //邮箱验证
  static validateEmailFn(t: any) {
    console.log('valid email');

    return (rule: any, value: any, callback: any) => {
      if (value == '' || value == undefined || value == null) {
        console.log('return l ');
        callback();
      } else {
        const valueStr = value as string;
        const number1 = valueStr.split(".").length - 1;
        const number2 = valueStr.split("@").length - 1;
        //邮件格式可以有多个".",一个"@"
        if (number1 < 1 || number2 != 1) {
          callback(new Error(t('common.emailWrong')));
        } else {
          callback();
        }
      }
    }
  }
  //字段不为空验证
  static validateTrimStringFn(t: any) {
    return (rule: any, value: any, callback: any) => {
      if (value == '' || value == undefined || value == null || value.trim() == '') {
        callback(new Error(t('admin_common_inputText')));
      } else {
        callback();
      }
    }
  }
  //验证评级控件不能为0
  static validateNotZeroFn(t: any) {
    return (rule: any, value: any, callback: any) => {
      if (value == 0) {
        callback(new Error(t('admin_common_inputText')));
      } else {
        callback();
      }
    }
  }
  //验证富文本控件不能为空
  static validateRichTextEditorFn(t: any) {
    return (rule: any, value: any, callback: any) => {
      if (value == "" || value == "<p><br></p>") {
        callback(new Error(t('admin_common_inputText')));
      } else {
        callback();
      }
    }
  }
  //电话验证
  static validateMobileFn(t: any) {
    return (rule: any, value: any, callback: any) => {
      const reg = RegExp(this.validMobileNumRegex());
      if (value == '' || value == undefined || value == null) {
        callback();
      } else {
        if (!reg.test(value)) {
          callback(new Error(t('admin_common_mobileWrong')));
        } else {
          callback();
        }
      }
    }
  }
  static validRequiredForm() {
    return { required: true, message: t('admin_common_inputText'), trigger: 'blur' }
  }

  static validMobileNumRegex() {
    return "^[0-9\*\(\)# -]*$"
  }
}



