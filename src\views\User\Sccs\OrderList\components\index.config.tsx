import { DICT_TYPE, getStrDictOptions } from '@/utils/dict'
// import { useI18n } from 'vue-i18n'

const { t } = useI18n() // 国际化

const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: t('admin_order_serialNumber'),
      prop: 'serialNumber',
      'min-width': 180
    },
    {
      label: t('admin_order_orderMark'),
      prop: 'orderMark'
    },
    {
      label: t('admin_order_sccsName'),
      prop: 'sccsName'
    },
    {
      label: 'SCCS CODE',
      prop: 'sccsCode'
    },
    {
      label: t('admin_home_inTeam'),
      prop: 'teamName'
    },
    {
      label: t('admin_order_teamCode'),
      prop: 'teamCode'
    },
    {
      label: t('admin_order_workOrderCount'),
      prop: 'workOrderCount'
    },
    {
      label: t('admin_order_orderState'),
      prop: 'orderStateStr'
    },
    {
      label: t('admin_common_creator'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.createUserName}</p>
              <p class={'my-2px'}>{row.createUserEmail}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'datetimerange',
      'min-width': 165
    },
    {
      label: t('admin_common_updater'),
      prop: 'content',
      clSlots: {
        default: ({ row }) => {
          return (
            <div>
              <p class={'my-2px'}>{row.updateUserName}</p>
              <p class={'my-2px'}>{row.updateUserEmail}</p>
            </div>
          )
        }
      }
    },
    {
      label: t('admin_common_updateTime'),
      prop: 'updateTime',
      type: 'datetimerange',
      'min-width': 165
    },
    ,
    {
      label: t('admin_common_completeTime'),
      prop: 'completeTime',
      type: 'datetimerange',
      'min-width': 165
    },
    ,
    {
      label: t('admin_common_cancelTime'),
      prop: 'cancelTime',
      type: 'datetimerange',
      'min-width': 165
    }
  ],
  tableBtnList: [
    { label: t('admin_common_detail'), key: "detail", type: "primary", link: true }
  ],
  searchConfig: [
    //头部筛选数据
    {
      label: t('admin_common_inputSearch'),
      prop: 'keyword',
      placeholder: t('admin_order_query_keyword_placeholder')
    },
    {
      label: t('admin_order_orderState'),
      prop: 'orderState',
      type: 'select',
      loading: false,
      options: getStrDictOptions(DICT_TYPE.ORDER_STATE)
    },
    {
      label: t('admin_common_createTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': '-',
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': 'YYYY-MM-DD HH:mm:ss'
    },
    {
      label: t('admin_common_updateTime'),
      prop: 'updateTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': '-',
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': 'YYYY-MM-DD HH:mm:ss'
    },
    {
      label: t('admin_common_completeTime'),
      prop: 'completeTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': '-',
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': 'YYYY-MM-DD HH:mm:ss'
    },
    {
      label: t('admin_common_cancelTime'),
      prop: 'cancelTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': '-',
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': 'YYYY-MM-DD HH:mm:ss'
    }
  ],
  searchBtnList: [
    // {
    //   label: t('admin_common_add'),
    //   key: 'add',
    //   icon: 'ep:plus',
    //   elProps: {
    //     type: 'primary'
    //   }
    // }
  ]
}

export default fixedParameter
