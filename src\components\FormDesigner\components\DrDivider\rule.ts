import { localeOptions } from '../../utils/index'

const label = '分割线'
const name = '<PERSON><PERSON><PERSON><PERSON>'

const Rule = {
  menu: 'extendLayout',
  icon: 'icon-divider',
  label,
  name,
  rule({ t }) {
    return {
      type: name,
      props: {
        title: label,
        contentPosition: 'center'
      }
    }
  },
  props(_, { t }) {
    return [
      {
        type: 'LanguageInput',
        field: 'title',
        title: '标题',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("titleEn", langValue);
            api.activeRule.props["titleEn"] = langValue;
          },
        },
      },
      {
        type: 'radio',
        field: 'contentPosition',
        title: '标题位置',
        props: {
          type: 'button'
        },
        options: localeOptions(t, [
          { label: '居左', value: 'left' },
          { label: '居中', value: 'center' },
          { label: '居右', value: 'right' }
        ])
      }
    ]
  }
}

export default Rule
