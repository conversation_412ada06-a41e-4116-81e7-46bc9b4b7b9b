<template>
  <div class="dr-address">
    <el-cascader
      v-model="modelValue.area"
      :props="cascaderProps"
      clearable
      :placeholder="areaPlaceholder"
      style="width: 400px"
    />
    <el-input
      v-if="formatType === 1"
      v-model="modelValue.address"
      style="width: 400px; margin-top: 10px"
      :rows="3"
      type="textarea"
      :placeholder="addressPlaceholder"
    />
  </div>
</template>

<script setup lang="ts">
import { CascaderNodeValue, CascaderProps } from 'element-plus'
import { ModelValueType } from '../common/DrDefaultValue/types'

type PropsType = {
  formatType: number
  areaPlaceholder?: string
  addressPlaceholder?: string
  defaultValueConfig?: ModelValueType
}

const props = withDefaults(defineProps<PropsType>(), {
  areaPlaceholder: '请选择',
  addressPlaceholder: '请输入'
})

const modelValue = defineModel<{ area: CascaderNodeValue[]; address: string }>({
  required: false,
  default: {
    area: undefined,
    address: ''
  }
})

// 初始化默认值
if (
  !modelValue.value.address &&
  props.defaultValueConfig &&
  props.defaultValueConfig.type !== 'none' &&
  props.defaultValueConfig.content
) {
  modelValue.value.address = props.defaultValueConfig.content
}

const requestAreasByCode = async (parentCode: CascaderNodeValue = '0') => {
  const URL =
    import.meta.env.VITE_BASE_URL +
    import.meta.env.VITE_API_URL +
    `/system/area/children?parentCode=${parentCode}`

  try {
    const response = await fetch(URL)
    if (!response.ok) {
      throw new Error(`Response status: ${response.status}`)
    }

    const json = await response.json()
    console.log(json)
    return json
  } catch (error: any) {
    console.error(error.message)
    return null
  }
}

const isLeaf = (item) => {
  if (item.leaf === 1) {
    return true
  }
  if (item.code.length === 2 && props.formatType === 5) {
    return true
  }

  if (item.code.length === 4 && props.formatType === 4) {
    return true
  }

  if (item.code.length >= 6 && props.formatType === 3) {
    return true
  }

  return false
}

const cascaderProps: CascaderProps = {
  value: 'code',
  label: 'name',
  lazy: true,
  lazyLoad(node, resolve) {
    requestAreasByCode(node.value).then((result) => {
      const nodes = result.data.map((item) => {
        return {
          ...item,
          leaf: isLeaf(item)
        }
      })

      resolve(nodes)
    })
  }
}
</script>

<style scoped lang="scss"></style>
