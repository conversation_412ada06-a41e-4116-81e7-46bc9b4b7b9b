import LinKinTip from '@/components/LinKinTip/index.vue'
import { DICT_TYPE, getIntDictOptions, getBoolDictOptions, getStrDictOptions } from '@/utils/dict'
import { DictTag } from '@/components/DictTag'

const { t } = useI18n() // 国际化


const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: 'ID',
      prop: "id",
    },
    {
      label: t('admin_user_userEmail'),
      prop: "email",
    },
    {
      label: t('admin_user_avatar'),
      slotName: 'avatar'
    },
    {
      label: t('admin_user_name'),
      prop: "username",
    },
    {
      label: t('admin_user_ownerTeamCount'),
      prop: "ownerTeamCount",
      'min-width': 90
    },
    {
      label: t('admin_user_joinTeamCount'),
      prop: "joinTeamCount",
      'min-width': 90
    },
    {
      label: t('admin_user_joinSccsCount'),
      prop: "joinSccsCount",
      'min-width': 90
    },
    {
      label: t('admin_user_registerTime'),
      prop: "createTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_latestLoginTime'),
      prop: "latestLoginTime",
      sortable: 'custom',
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_userAgent'),
      prop: "userAgent",
    },
    {
      label: t('admin_user_userOs'),
      prop: "userOs",
    },
    {
      label: t('admin_user_userDevice'),
      prop: "userDevice",
    },
    {
      label: t('admin_user_userIp'),
      prop: "userIp",
    },
    {
      label: t('admin_user_userArea'),
      prop: "userArea",
    },
    {
      label: t('admin_template_status'),
      slotName: 'status'
    },
    {
      label: t('admin_user_isActivate'),
      prop: "activate",
      filterOptions: getBoolDictOptions(DICT_TYPE.COMMON_BOOLEAN_STRING)
    },
  ],
  tableBtnList: [
    { label: t('admin_common_edit'), key: "edit", type: "primary", link: true },
    { label: t('admin_common_detail'), key: "detail", type: "primary", link: true }
  ],
  searchConfig: [
    //头部筛选数据
    {
      label: t('admin_user_searchInput'),
      placeholder: t('admin_user_userSearchTip'),
      prop: 'keyword'
    },
    {
      label: t('admin_template_status'),
      prop: 'status',
      type: 'select',
      options: getIntDictOptions(DICT_TYPE.COMMON_STATUS)
    },
    {
      label: t('admin_user_isActivate'),
      prop: 'activate',
      type: 'select',
      options: getIntDictOptions(DICT_TYPE.COMMON_ENABLED)
    },
    {
      label: t('admin_user_registerTime'),
      prop: 'createTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': "-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    },
    {
      label: t('admin_user_latestLoginTime'),
      prop: 'latestLoginTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': "-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    }
  ],
  searchBtnList: [ {
    label: t('admin_common_add'),
    key: 'add',
    icon: 'ep:plus',
    elProps: {
      type: 'primary',
    }
  }],
  ownerTeamListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_teamName'),
      prop: "teamName"
    },
    {
      label: t('admin_user_teamShortName'),
      prop: "teamShortName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_user_memberNum'),
      prop: "teamMemberCount",
    },
    {
      label: t('admin_user_memberInfo'),
      prop: "teamMemberList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.teamMemberList?.length > 0 ? (
                row.teamMemberList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.username + ' (' + data.email + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_user_inviteTeamCount'),
      prop: "inviteTeamCount",
    },
    {
      label: t('admin_user_inviteTeamList'),
      prop: "inviteTeamList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.inviteTeamList?.length > 0 ? (
                row.inviteTeamList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.teamName + ' (' + data.teamCode + ') '}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
  ],
  joinTeamTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_teamName'),
      prop: "teamName"
    },
    {
      label: t('admin_user_teamShortName'),
      prop: "teamShortName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_role'),
      prop: "roleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.roleList?.length > 0 ? (
                row.roleList.map((data: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', data.manager ? 'bg-#eef8ff' : 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color={data.manager ? '#0070d2' : '#808080'}
                      placement='top-start'
                      // tipClass='w-300px'
                      content={data.roleName}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_user_username'),
      prop: "account",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
              <LinKinTip
                color='#808080'
                placement='top-start'
                content={row.inviterName + ' (' + row.inviterEmail + ') '}
              ></LinKinTip>
            </div>
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ],
  sccsListTableThs: [
    // table ths固定参数
    {
      type: 'index',
      width: 50
    },
    {
      label: t('admin_user_sccsName'),
      prop: "sccsName"
    },
    {
      label: t('admin_user_sccsCode'),
      prop: "sccsCode",
    },
    {
      label: t('admin_user_inTeamName'),
      prop: "teamName",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <span>{row.type == 'COOP'}</span>
            <LinKinTip
              color='#808080'
              placement='top-start'
              // tipClass='w-300px'
              content={row.teamName + ' (' + row.teamCode + ')'}
            ></LinKinTip>
          </div>
        }
      },
    },
    {
      label: t('admin_user_isCoop'),
      prop: "type",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <el-tag class={['mr-3px', 'text-14px']} type={row.type == 'COOP' ? 'success' : 'info'}>{row.type == 'COOP' ? t('admin_common_yes') : t('admin_common_no')}</el-tag>
            {
              row.type == 'COOP' ?
                <LinKinTip
                  color='#808080'
                  class={'text-12px'}
                  placement='top-start'
                  // tipClass='w-300px'
                  content={'(' + row.coopTeamName + ')'}
                ></LinKinTip>
                : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_template_businessRole'),
      prop: "sccsRoleList",
      'show-overflow-tooltip': false,
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            {
              row.sccsRoleList?.length > 0 ? (
                row.sccsRoleList.map((name: any) => {
                  return <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
                    <LinKinTip
                      color='#808080'
                      placement='top-start'
                      content={name}
                    ></LinKinTip>
                  </div>
                })
              ) : ''
            }
          </div>
        }
      },
    },
    {
      label: t('admin_common_createTime'),
      prop: "createTime",
      type: "datetimerange",
      'min-width': 165
    },
    {
      label: t('admin_user_username'),
      prop: "account",
      clSlots: {
        default: ({ row }) => {
          return <div class={['flex', 'flex-wrap', 'w-100%']}>
            <div class={['mr-3px', 'mb-5px', 'max-w-100%', 'bg-#f2f2f2', 'px-7px', 'rounded-5px']}>
              <LinKinTip
                color='#808080'
                placement='top-start'
                content={row.username + ' (' + row.email + ') '}
              ></LinKinTip>
            </div>
          </div>
        }
      },
      'min-width': 180
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      'min-width': 165
    }
  ]
}

export default fixedParameter