<template>
  <div class="dr-table-form">
    <div class="dr-table-row" v-if="columns.length > 0">
      <slot name="default"></slot>
    </div>
    <el-table v-if="columns.length <= 0" :data="[{}]" border style="width: 100%" @emit-event="$emit">
      <el-table-column>
        <template #header>
          <div class="columns-empty-view">
            <span>请在右侧组件列表配置列数据</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <el-button link type="primary">
      <i class="fc-icon icon-add-circle" style="font-weight: 700"></i>
      {{ addButtonText }}
    </el-button>
  </div>
</template>

<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { markRaw } from 'vue'

type PropsType = {
  formCreateInject: any
  autoRelate: boolean
  columns?: Array<{ type: string; label: string; fieldId: string }>
  addButtonText: string
}

const props = withDefaults(defineProps<PropsType>(), {
  columns: () => [],
  rule: () => [],
  addButtonText: '添加'
})

const modelValue = defineModel<any[]>({
  required: false,
  default: []
})

watch(() => props, () => {
  //@ts-ignore
  const tableColumnLists = cloneDeep(props.formCreateInject.children.slice(1)).map(column => {
    return column.children[0]?.children[0]?.children[0]?.children[0];
  });
  for (let column of tableColumnLists) {
    if (!column) {
      continue;
    }
    const { field, title } = column;
    const index = props.columns.findIndex(columnConfig => columnConfig.fieldId === field);
    const columnConfig = props.columns.find(columnConfig => columnConfig.fieldId === field);
    //@ts-ignore
    props.columns.splice(index, 1, Object.assign(columnConfig, { label: title }));
  }
}, {
  deep: true
})

const FormInstance = markRaw(props.formCreateInject.form.$form())
console.log('FormInstance', FormInstance.props)
</script>

<style scoped lang="scss">
.dr-table-form {
  width: 100%;

  ::v-deep(._fd-drag-tool) {
    display: inline-block;
    width: 180px !important;
    height: 200px !important;

    .drag-r {
      display: none;
    }
  }

  ::v-deep(.fc-form-col) {
    display: inline-flex;

    &:first-child {
      // display: none;
    }
  }

  ::v-deep(.dr-table-col) {
    display: inline-flex;
    // height: 100px;
    border: 1px solid #e4e7ed;
    // border-right: 0 none;
    // padding: 10px;
    box-sizing: content-box;
    position: relative;
    flex: 1;

    .dr-table-col-mask {
      width: 100%;
      height: 100%;
      position: absolute;
      top: 0;
      left: 0;
      background: #999;
      opacity: 0.12;
      cursor: not-allowed;
      z-index: 1999;
    }
  }

  ::v-deep(.dr-table-row) {
    width: 100%;
    overflow-x: auto;

    ._fd-drag-tool {
      display: table-cell;
      height: 100%;

      &:first-child {
        display: none;
      }

      .dr-table-col {
        ._fd-drag-tool {
          &:first-child {
            display: block !important;
          }
        }
      }
    }

    ._fc-DrTableFormColumn-item {
      ._fd-drag-tool {
        display: block !important;
      }

      .fc-form-col {
        width: 96%;

        .el-form-item {
          width: 100%;

          .el-form-item__label {
            display: block;
            padding-left: 10px;
            padding-bottom: 8px;
            border-bottom: 1px solid #e4e7ed;
          }
        }
      }
    }
  }
}

.columns-empty-view {
  width: 100%;
  height: 50px;
  background-color: #f2f2f2;
  display: flex;
  justify-content: center;
  align-items: center;

  span {
    color: #999;
  }
}
</style>