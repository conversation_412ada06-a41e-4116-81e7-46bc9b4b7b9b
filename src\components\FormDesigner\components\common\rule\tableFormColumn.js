import { localeProps } from '../../../utils'
import unique from '@form-create/utils/lib/unique'

const name = 'tableFormColumn'

export default {
  icon: 'icon-cell',
  name,
  aide: true,
  drag: true,
  dragBtn: false,
  mask: false,
  style: false,
  advanced: false,
  variable: false,
  only: true,
  rule({ t }) {
    return {
      type: name,
      props: {
        label: t('com.tableFormColumn.label'),
        width: 'auto'
      },
      children: [
        // {
        //   type: "DrInput",
        //   field: unique(),
        //   title: "",
        //   $required: false,
        //   props: {
        //     placeholder: '请输入'
        //   },
        //   children: []
        // }
      ]
    }
  },
  props(_, { t }) {
    return []
  }
}
