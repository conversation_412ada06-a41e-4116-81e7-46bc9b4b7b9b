# 开发环境：本地只启动前端项目，依赖开发环境（后端、APP）
NODE_ENV=development

VITE_DEV=true

# 请求路径
VITE_BASE_URL=https://dev-admin-api.linkincrease.com.cn
# VITE_BASE_URL='http://dofast.demo.huizhizao.vip:20001'

#贸易端路径
VITE_TRADE_BASE_URL=https://test-trade.linkincrease.com.cn

# 文件上传类型：server - 后端上传， client - 前端直连上传，仅支持S3服务
VITE_UPLOAD_TYPE=server
# 上传路径
VITE_UPLOAD_URL='https://dev-admin-api.linkincrease.com.cn/admin-api/infra/file/upload'

# 接口前缀
VITE_API_BASEPATH=/dev-api

# 接口地址
VITE_API_URL=/admin-api

# 是否删除debugger
VITE_DROP_DEBUGGER=true

# 是否删除console.log
VITE_DROP_CONSOLE=true

# 是否sourcemap
VITE_SOURCEMAP=true

# 打包路径
VITE_BASE_PATH=/

# 输出路径
VITE_OUT_DIR=dist

# 验证码的开关
VITE_APP_CAPTCHA_ENABLE=false
