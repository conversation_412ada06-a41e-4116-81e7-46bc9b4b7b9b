<template>
  <el-date-picker
    v-model="modelValue"
    :type="datePickerType.type"
    :format="datePickerType.format"
  />
</template>
<script setup lang="ts">
import { IDatePickerType } from 'element-plus/es/components/date-picker/src/date-picker.type'
import { ModelValueType } from '../common/DrDefaultValue/types'
import dayjs from 'dayjs'

type PropsType = {
  showType?: string
  format?: string
  defaultValueConfig?: ModelValueType
}

const props = withDefaults(defineProps<PropsType>(), {
  showType: 'date',
  format: 'YYYY-MM-DD'
})

const modelValue = defineModel<string>({ required: false, default: undefined })

// 初始化默认值
if (
  !modelValue.value &&
  props.defaultValueConfig &&
  props.defaultValueConfig.type !== 'none' &&
  props.defaultValueConfig.content
) {
  modelValue.value = dayjs(props.defaultValueConfig.content).format(props.format)
}

const datePickerType = computed<{ type: IDatePickerType; format: string }>(() => {
  const DatePickerTypeMap = {
    year: {
      type: 'year',
      format: 'YYYY'
    },
    month: {
      type: 'month',
      format: 'YYYY-MM'
    },
    date: {
      type: 'date',
      format: 'YYYY-MM-DD'
    },
    datetime: {
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm'
    },
    datetimesecond: {
      type: 'datetime',
      format: 'YYYY-MM-DD HH:mm:ss'
    }
  }

  return DatePickerTypeMap[props.showType]
})

watch(
  () => datePickerType.value,
  (val) => {
    console.log('watch datePickerType', val)
  }
)
</script>
<style lang=""></style>
