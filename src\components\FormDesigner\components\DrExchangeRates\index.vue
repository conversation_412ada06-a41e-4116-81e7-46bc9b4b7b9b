<template>
  <div class="dr-formulas">
    <span class="label">{{ exchangeTypeLabel }}</span>
    <el-input-number
      :model-value="modelValue.fixedRate"
      :min="0"
      :max="100"
      :precision="2"
      :controls="false"
      style="width: 80px"
      :readonly="exchangeType === 'realTime'"
      @change="onFixedRateChange"
    />
    <el-input
      :model-value="modelValue.inputValue"
      type="number"
      placeholder="0.00"
      style="width: 200px"
      @input="onInputValue"
    >
      <template #append>
        <el-select
          :model-value="modelValue.inputCurrency"
          style="width: 80px"
          @change="onInputCurrencyChange"
        >
          <el-option v-for="item in currencyOptions" :key="item" :label="item" :value="item" />
        </el-select>
      </template>
    </el-input>
    <div class="icon">
      <el-icon><Sort /></el-icon>
    </div>
    <el-input :value="outputValue" readonly style="width: 200px">
      <template #append>{{ targetCurrency }}</template>
    </el-input>
  </div>
</template>

<script setup lang="ts">
import { Sort } from '@element-plus/icons-vue'
import { round } from 'lodash-es'
import { currencyOptions } from '../../utils'

type ModelValueType = {
  fixedRate: number
  inputValue: string
  inputCurrency: string
}

type PropsType = {
  modelValue: ModelValueType
  exchangeType: 'fixed' | 'realTime'
  targetCurrency: string
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => ({ inputValue: '', inputCurrency: 'CNY', fixedRate: 0 })
})

const emit = defineEmits(['update:modelValue'])

const exchangeTypeLabel = computed(() => {
  const exchangeTypeLabelMap = {
    fixed: '固定汇率',
    realTime: '实时汇率'
  }

  return exchangeTypeLabelMap[props.exchangeType]
})

const onFixedRateChange = (rate: number) => {
  emit('update:modelValue', {
    ...props.modelValue,
    fixedRate: rate
  })
}

const onInputValue = (value: string) => {
  console.log('onInputValue', value)
  // TODO 格式化数字与小数
  emit('update:modelValue', {
    ...props.modelValue,
    inputValue: value
  })
}

const onInputCurrencyChange = (currency: string) => {
  emit('update:modelValue', {
    ...props.modelValue,
    inputCurrency: currency
  })
}

const outputValue = computed(() => {
  if (props.exchangeType === 'fixed') {
    return round(Number(props.modelValue.inputValue) * Number(props.modelValue.fixedRate), 2)
  }
  return round(Number(props.modelValue.inputValue) * 7.01, 2)
})
</script>

<style scoped lang="scss">
.dr-formulas {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;

  .icon {
    transform: rotate(90deg);
  }
}
</style>
