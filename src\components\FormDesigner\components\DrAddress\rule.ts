import { props } from '@/plugins/fcDesignerPro/locale/en'
import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrAddress'
const label = '地址'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-input',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {
        defaultValueConfig: { type: 'none', content: '', area: [] },
        formatType: 1,
        areaPlaceholder: '请选择地址',
        addressPlaceholder: '请输入详细地址'
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(data, { t }) {
    return [
      {
        type: 'DrDefaultValue',
        field: 'defaultValueConfig',
        title: '默认内容',
        props: {
          currentField: data.field,
          componentName: name,
          formatType: data.props.formatType,
        }
      },
      {
        type: 'select',
        field: 'formatType',
        value: 1,
        title: '地址精度显示',
        options: [
          { value: 1, label: '省/市/区/街道/详细地址' },
          { value: 2, label: '省/市/区/街道' },
          { value: 3, label: '省/市/区' },
          { value: 4, label: '省/市' },
          { value: 5, label: '省' }
        ]
      },
      {
        type: 'LanguageInput',
        title: '地区选择占位提示语',
        field: 'areaPlaceholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("areaPlaceholderEn", langValue);
            api.activeRule.props["areaPlaceholderEn"] = langValue;
          },
        },
      },
      {
        type: 'LanguageInput',
        title: '详细地址占位提示语',
        field: 'addressPlaceholder',
        on: {
          updateI18n(api, data, langValue) {
            api.setData("addressPlaceholderEn", langValue);
            api.activeRule.props["addressPlaceholderEn"] = langValue;
          },
        },
      }
    ]
  }
}

export default Rule
