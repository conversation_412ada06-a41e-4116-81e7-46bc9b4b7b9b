<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery" />
  </ContentWrap>

  <ContentWrap>
    <Table :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @handle-table="handleTable" />
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>

  <!-- 表单弹窗：添加/修改 -->
  <BizTemplateForm ref="formRef" :industryOptions="industryOptions" @success="getList" />
  <!-- 表单设计 -->
  <TemplateDesign ref="designRef" />
</template>

<script setup lang="ts">
import { BizTemplateVO } from '@/api/template/bizTemplate'
import BizTemplateForm from '@/views/Template/Center/BizTemplate/components/BizTemplateForm.vue'
import TemplateDesign from '@/views/Template/Center/BizTemplate/components/TemplateDesign.vue'
import businessConfig from './components/index.config'
import { findJsonIndex } from '@/utils';
import { SccsBizTemplateApi } from '@/api/user/sccs';

const message = useMessage() // 消息弹窗
const { t } = useI18n() // 国际化
const { push } = useRouter() // 路由

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as BizTemplateVO[]
})

/** 运营端行业管理 列表 */
defineOptions({ name: 'SccsList' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  keyword = ''
  typeCode = null
  industryId = null
  status = null
  createTime = []
  updateTime = []
}
const queryParams = reactive(new DefaultQuery())

const industryOptions = ref([])
const statusLoading = ref(false)


const handleQuery = (type: string) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'add':
      openForm('create')
      break;
    default:
      break;
  }
}

const handleTable = (type: string, row: any) => {
  switch (type) {
    case 'edit':
      openForm('update', row.id)
      break;
    case 'delete':
      handleDelete(row.id)
      break;
    case 'copy':
      openForm('copy', row.templateId)
      break;
    case 'design':
      openDesign(row.templateId, row.sccsName, row.id)
      break;
    case 'detail':
      push('/user/sccsDetail?id=' + row.id)
      break;
    default:
      break;

  }
}
/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await SccsBizTemplateApi.getBizTemplatePage(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
const openForm = (type: string, id?: number) => {
  formRef.value.open(type, id, true)
}

const designRef = ref()
const openDesign = (id: number, sccsName: string, sccsId: string) => {
  designRef.value.open(id, sccsName, sccsId)
}

/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await SccsBizTemplateApi.deleteSccs(id)
    message.success(t('admin_common_delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}

const getIndustryOptions = async () => {
  const index = findJsonIndex(queryState.columns, 'industryId', 'prop')
  queryState.columns[index].loading = true
  try {
    const data = await SccsBizTemplateApi.getIndustryOptions()
    queryState.columns[index].options = data
    industryOptions.value = data || []
  } finally {
    queryState.columns[index].loading = false
  }
}

/** 初始化 **/
onMounted(() => {
  getList()
  getIndustryOptions()
})

onActivated(() => {
  getList()
  getIndustryOptions()
})
</script>
