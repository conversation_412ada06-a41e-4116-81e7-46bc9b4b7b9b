export const formulasTipsMap = {
  math: '数字处理',
  string: '文本处理',
  date: '时间处理',
  collection: '合集处理',
  condition: '逻辑处理',
  ADD: '获取两个数字相加的值',
  SUB: '获取两个数字相减的值',
  MUL: '获取两个数字相乘的值',
  DIV: '获取两个数字相除的值',
  SUM: '获取合集中数值的总和',
  MAX: '获取参数列表中的最大值',
  MIN: '获取参数列表中的最小值',
  ABS: '获取数字的绝对值',
  AVG: '获取参数列表的平均值',
  POWER: '获取指定数字的乘幂',
  RAND: '获取一个大于等于0且小于1的随机数',
  PI: '获取圆周率',
  ROUND: '将一个小数四舍五入到指定的位数',
  SQRT: '获取一个数字的正平方根',
  NOW: '获取当前的时间',
  TODAY: '获取今天的日期',
  YEAR: '获取指定日期的年份',
  MONTH: '获取指定日期的月份',
  DAY: '获取指定日期的天数',
  HOUR: '获取指定日期的小时数',
  MINUTE: '获取指定日期的分钟数',
  SECOND: '获取指定日期的秒数',
  DIFFDAYS: '获取两个日期之间的天数',
  DIFFHOURS: '获取两个时间之间的小时数,保留两位小数',
  DIFFMINUTES: '获取两个时间之间的分钟数',
  TIMESTAMP: '获取指定日期的时间戳',
  STARTSWITH: '检查字符串是否以指定字符串开头',
  EMPTY: '检查参数是否为空',
  NOTEMPTY: '检查参数是否不为空',
  LEN: '获取指定合集的长度',
  MOD: '获取两个数字的余数',
  FLOOR: '获取指定数字向下取整的值',
  CEIL: '获取指定数字向上取整的值',
  FIXED: '将一个小数保留指定位数的小数',
  ISNUMBER: '检查参数是否为数字',
  TONUMBER: '将参数转换为数字',
  SLICELEFT: '获取一个字符串从开头开始指定长度的字符串',
  SLICERIGHT: '获取一个字符串从结尾开始指定长度的字符串',
  TOLOWER: '将字符串中所有大写字母转换为小写字母',
  TOUPPER: '将字符串中所有小写字母转换为大写字母',
  INCLUDES: '检查字符串中是否包含指定字符串',
  REPLACE: '将字符串中的部分文本替换为不同的文本,只替换匹配到的第一个',
  REPLACEALL: '将字符串中的部分文本替换为不同的文本,替换所有匹配到的',
  TRIM: '将字符串前后的空格删除',
  TOCHINSESAMOUNT: '获取指定数字的中文大写金额',
  UNION: '将合集/参数中的值去重,返回去重后的合集',
  INTERSECTIONSET: '获取两个集合的交集',
  LIST: '获取所有参数组成的集合',
  AND: '将表达式用"并且"链接,当所有表达式均为true时返回true，否则返回false',
  OR: '将表达式用"或"链接,当有一个表达式为true时返回true，否则返回false',
  IF: '检查一个条件能否满足,如果满足返回第二个参数，否则返回第三个参数',
  IN: '检查第二个参数是否在合集中',
  DEFAULT: '检查第一个参数,如果为空返回第二个参数,否则返回第一个参数',
  CASE: '检查是否满足一个或多个条件，返回第一个满足条件的值',
  COLUMN: '获取子表单中指定字段并返回合集',
  VALUE: '获取分组表单中指定字段',
  CONCAT: '将所有参数拼接,返回拼接后的字符串',
  FALSE: '返回逻辑值 false',
  TRUE: '返回逻辑值 true',
  NOT: '获取某个逻辑值的相反值',
  EQ: '检查两个值是否相等',
  NE: '检查两个值是否不相等',
  GE: '检查第一个值是否大于等于另一个值',
  GT: '检查第一个值是否大于另一个值',
  LE: '检查第一个值是否小于等于另一个值',
  LT: '检查第一个值是否小于另一个值'
}
