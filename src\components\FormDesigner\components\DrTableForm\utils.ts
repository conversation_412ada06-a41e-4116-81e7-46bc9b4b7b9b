export const generateDataSourceList = (injectionData: any) => {
  try {
    const result: any[] = []

    if (injectionData) {
      const { currentFormId, templateTreeData, formType, msFormType } = unref(injectionData)
      console.log('templateTreeData', currentFormId.value, templateTreeData.value)
      let templateFormList: any[] = []

      const mainFormTemplate: any = templateTreeData.value.find(
        (templateForm) => templateForm.formType === 'MAIN_FORM'
      )
      if (currentFormId.value !== mainFormTemplate.id) {
        templateFormList.push(mainFormTemplate)
      }

      const templateList = templateTreeData.value.filter(
        (templateData) => templateData.formType !== 'MAIN_FORM'
      )
      templateFormList = templateFormList.concat(templateList)

      for (let i = 0; i < templateFormList.length; i++) {
        const item = templateFormList[i]
        // if (item.id === injectionData?.currentFormId.value) continue
        if (item.formType === 'MAIN_FORM') {
          result.push({
            formType: item.formType,
            value: item.id,
            label: item.name,
            id: item.id,
            templateId: item.templateId,
            formList: item.widgetJsonList || []
          })
        }
        if (item.formType === 'MILESTONE') {
          // 是否有工单或有工单批复
          if (
            (Array.isArray(item.formList) && item.formList.length > 0) ||
            item.replyType === 'REPLY_TO_ALL' ||
            item.replyType === 'REPLY_TO_WORK_ORDER'
          ) {
            const listItem = {
              formType: item.formType,
              value: item.id,
              label: item.name,
              id: item.id,
              templateId: item.templateId,
              formList: item.formList.filter(
                (item) => item.id !== injectionData?.currentFormId.value
              )
            }
            if (
              (item.replyType === 'REPLY_TO_ALL' || item.replyType === 'REPLY_TO_WORK_ORDER') &&
              msFormType.value !== 'REPLY_TO_WORK_ORDER'
            ) {
              listItem.formList.unshift({
                ...item.workOrderReplyForm,
                name: `工单批复-${item.workOrderReplyForm.name}`
              })
            }

            if (listItem.formList.length > 0) {
              result.push(listItem)
            }
          }
          // 是否开启批复
          if (item.needReply && item.replyType) {
            // 是否需要里程碑批复
            if (
              (item.replyType === 'REPLY_TO_ALL' || item.replyType === 'REPLY_TO_MILESTONE') &&
              item.replyForm.id !== injectionData.replyFormId.value
            ) {
              result.push({
                formType: item.replyForm.formType,
                value: item.replyForm.id,
                label: `里程碑批复-${item.replyForm.name}`,
                id: item.replyForm.id,
                templateId: item.replyForm.templateId,
                formList: item.replyForm.widgetJsonList || []
              })
            }
          }
        }
      }
    }

    return result
  } catch (error) {
    return []
  }
}

export const generateRelateFieldList = (widgetJsonList: any[], isDrTableForm: boolean) => {
  // 不能作为关联字段的黑名单列表
  const blackList = ['DrDivider', 'DrPlaceholder']

  // 布局组件列表，如果是布局组件，通过递归深入查找
  const layoutList = ['DrCard', 'col', 'elTabs', 'elTabPane', 'DrRelateCard']

  const result: any[] = []

  widgetJsonList.forEach((widget) => {
    if (blackList.includes(widget.type)) return
    if (layoutList.includes(widget.type)) {
      if (widget.type !== 'DrRelateCard') {
        result.push(...generateRelateFieldList(widget.children || [], isDrTableForm))
      } else {
        const field = {
          isDrTableForm,
          field: widget.field,
          title: widget.title,
          type: 'parent',
          children: [
            ...generateRelateFieldList(widget.props.relatedValue.rules || [], isDrTableForm)
          ]
        }
        result.push(field)
      }
      return
    }

    if (isDrTableForm === false) {
      if (widget.type === 'DrTableForm') return
      result.push(widget)
    } else {
      if (widget.type !== 'DrTableForm') return
      widget.props.columns.forEach((column: any) => {
        const field = {
          isDrTableForm,
          field: column.fieldId,
          title: column.label,
          type: column.type
        }

        result.push(field)
      })
    }
  })

  return result
}
