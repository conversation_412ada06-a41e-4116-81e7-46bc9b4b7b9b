<template>
  <div class="fc-dr-checkbox">
    <!-- 自定义阅读模式 -->
    <template v-if="formCreateInject?.preview">
      <template v-if="currentOptions.length > 0">
        <el-tag
          v-for="item in currentOptions"
          :key="item.value"
          :color="item.color || '#EFEFEF'"
          style="color: #000; margin-right: 10px"
          >{{ item.label }}</el-tag
        >
      </template>
      <span v-else>--</span>
    </template>
    <!-- 表单模式 -->
    <template v-else>
      <template v-if="layout !== 'select'">
        <el-checkbox-group v-model="modelValue">
          <div class="flex-layout" :class="direction">
            <div v-for="option in options" :key="option.value" class="flex-layout-item">
              <el-checkbox :value="option.value">
                <div
                  class="checkbox-label"
                  :style="`background-color: ${option.color || '#EFEFEF'};`"
                >
                  {{ option.label }}
                </div>
              </el-checkbox>
            </div>
          </div>
        </el-checkbox-group>
      </template>
      <template v-else>
        <el-select v-model="modelValue" multiple clearable :placeholder="placeholder">
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          >
            <div class="select-option">
              <el-tag class="color-tag" :color="item.color" size="small" />
              <span>{{ item.label }}</span>
            </div>
          </el-option>
          <!-- <template #label="{ label, value }">
          <span>{{ label }}: </span>
          <span style="font-weight: bold">{{ value }}</span>
        </template> -->
          <template #tag>
            <el-tag
              v-for="(item, index) in modelValue"
              :key="item"
              :color="getOptionColor(item)"
              closable
              style="color: #000"
              @close="remove(index)"
              >{{ getOptionLabel(item) }}</el-tag
            >
          </template>
        </el-select>
      </template>
    </template>
  </div>
</template>
<script setup lang="ts">
import { computed } from 'vue'

type PropsType = {
  formCreateInject?: any
  options: any[]
  disabled?: boolean
  layout: 'horizontal' | 'vertical' | 'select'
  placeholder?: string
}
const modelValue = defineModel<string[]>({ required: false, default: [] })

const props = withDefaults(defineProps<PropsType>(), {
  options: () => [],
  layout: 'horizontal',
  disabled: false
})

watch(
  () => props.options,
  (options) => {
    const defaultOptions = options.filter((item) => item.checked).map((item) => item.value)
    modelValue.value = defaultOptions
  },
  { immediate: true, deep: true }
)

const currentOptions = computed(() => {
  const options = props.options.filter((item) => modelValue.value.includes(item.value))
  return options
})

const remove = (index: number) => {
  modelValue.value.splice(index, 1)
}

const getOptionLabel = (value: string) => {
  const index = props.options.findIndex((item) => item.value === value)
  if (index === -1) {
    return ''
  }
  return props.options[index].label || ''
}

const getOptionColor = (value: string) => {
  const index = props.options.findIndex((item) => item.value === value)
  if (index === -1) {
    return '#EFEFEF'
  }
  return props.options[index].color || '#EFEFEF'
}

const direction = computed(() => {
  const directionMap = {
    horizontal: 'row',
    vertical: 'column',
    select: ''
  }

  return directionMap[props.layout]
})
</script>
<style scoped lang="scss">
.fc-dr-checkbox {
  width: 100%;

  .flex-layout {
    display: flex;

    &.row {
      flex-direction: row;
      flex-wrap: wrap;
      gap: 24px;
    }

    &.column {
      flex-direction: column;
    }

    .checkbox-label {
      height: 20px;
      line-height: 20px;
      padding: 0 10px;
      border-radius: 8px;
    }
  }
}

.select-option {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 10px;

  .color-tag {
    border: none;
    aspect-ratio: 1;
  }
}
</style>
