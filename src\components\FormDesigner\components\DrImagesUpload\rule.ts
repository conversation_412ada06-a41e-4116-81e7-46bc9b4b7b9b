import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrImagesUpload'
const label = '图片'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-upload',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {},
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'inputNumber',
        title: '最大上传数量',
        field: 'limit',
        props: {
          min: 1,
          precision: 0
        }
      },
      {
        type: 'inputNumber',
        title: '单个图片最大上传大小（MB）',
        field: 'maxFileSize',
        props: {
          min: 1,
          precision: 0,
          max: 50
        }
      }
    ]
  }
}

export default Rule
