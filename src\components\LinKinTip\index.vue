<template>
  <el-tooltip v-if="content" :popper-class="props.tipClass ? props.tipClass : 'lk-tooltip'" :show-after="500"
    :effect="effect" :content="content" :placement="placement" :disabled="disabled">
    <el-text v-bind="$attrs" class="cursor-pointer" :style="{
    'line-height': '20px',
    display,
    alignSelf: 'auto',
    color,
    ...(size ? { fontSize: size + 'px' } : {}),
    ...(weight ? { fontWeight: weight } : {})
  }" truncated @mouseenter="onTipDisabled">
      <slot>{{ content }}</slot>
    </el-text>
  </el-tooltip>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// eslint-disable-next-line no-unused-vars
const props = withDefaults(
  defineProps<{
    content?: string | null | undefined
    effect?: 'dark' | 'light'
    placement?:
    | 'top'
    | 'top-start'
    | 'top-end'
    | 'bottom'
    | 'bottom-start'
    | 'bottom-end'
    | 'left'
    | 'left-start'
    | 'left-end'
    | 'right'
    | 'right-start'
    | 'right-end'
    color?: string
    size?: number
    tipClass?: string
    weight?: string | number
    display?: string
  }>(),
  {
    content: '',
    effect: 'dark',
    placement: 'top',
    color: 'inherit',
    size: 0,
    weight: '',
    display: 'block'
  }
)

const disabled = ref(true)
const onTipDisabled = (e: MouseEvent) => {
  const { clientWidth, scrollWidth } = e.target as HTMLElement
  disabled.value = !(clientWidth < scrollWidth)
}
</script>

<style lang="scss">
.lk-tooltip {
  &.el-popper {
    max-width: 500px;
  }
}
</style>
