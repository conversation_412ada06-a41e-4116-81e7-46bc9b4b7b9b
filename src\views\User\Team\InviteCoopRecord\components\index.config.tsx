const { t } = useI18n() // 国际化


const fixedParameter = {
  tableThs: [
    // table ths固定参数
    {
      label: t('admin_user_teamName'),
      prop: "teamName",
    },
    {
      label: t('admin_user_teamCode'),
      prop: "teamCode",
    },
    {
      label: t('admin_user_invitedTeamName'),
      prop: "invitedName",
      clSlots: {
        default: ({ row }) => {
          return <div>
            <p class={['my-2px', 'single-ellipsis']}> {row.invitedTeamName || ''}</p>
            <p class={['my-2px', 'single-ellipsis']}> {row.invitedTeamCode || ''}</p>
          </div>
        }
      },
      'min-width': 150
    },
    {
      label: t('admin_user_username'),
      prop: "username",
      clSlots: {
        default: ({ row }) => {
          return <div>
            <p class={['my-2px', 'single-ellipsis']}> {row.username || ''}</p>
            <p class={['my-2px', 'single-ellipsis']}> {row.account || ''}</p>
          </div>
        }
      },
      'min-width': 150
    },
    {
      label: t('admin_user_joinTime'),
      prop: "joinTime",
      type: "datetimerange",
      sortable: 'custom',
      'min-width': 165
    },
    {
      label: t('admin_user_moveUser'),
      prop: "deleteName",
      clSlots: {
        default: ({ row }) => {
          return <div>
            <p class={['my-2px', 'single-ellipsis']}> {row.deleteName || ''}</p>
            <p class={['my-2px', 'single-ellipsis']}> {row.deleteAccount || ''}</p>
          </div>
        }
      },
      'min-width': 150
    },
    {
      label: t('admin_user_moveTime'),
      prop: "deleteTime",
      type: "datetimerange",
      sortable: 'custom',
      'min-width': 165
    }
  ],
  tableBtnList: [],
  searchConfig: [
    //头部筛选数据
    {
      label: t('admin_user_searchInput'),
      placeholder: t('admin_user_teamSearch'),
      prop: 'keyword'
    },
    {
      label: t('admin_user_joinTime'),
      prop: 'joinTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': "-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    },
    {
      label: t('admin_user_moveTime'),
      prop: 'deleteTime',
      type: 'daterange',
      style: { width: '240px' },
      'range-separator': "-",
      'start-placeholder': t('admin_common_startTimeText'),
      'end-placeholder': t('admin_common_endTimeText'),
      'default-time': [new Date('1 00:00:00'), new Date('1 23:59:59')],
      'value-format': "YYYY-MM-DD HH:mm:ss"
    }
  ],
  searchBtnList: []
}

export default fixedParameter