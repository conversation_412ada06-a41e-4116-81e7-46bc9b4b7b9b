<template>
  <el-radio-group :model-value="modelValue" @change="handleChangeExchangeRate">
    <div class="exchangeRateRow">
      <el-radio :value="'fixed'">
        <el-input v-model="fixedTitle" @click.stop>
          <template #append>
            <el-popover
              placement="bottom-end"
              :width="300"
              :hide-after="0"
              trigger="click"
              ref="pop"
              popper-class="_fd-language-popover"
            >
              <template #reference>
                <i class="fc-icon icon-language"></i>
              </template>
              <div class="_fd-language-list">
                <div class="_fd-language-row">
                  <span class="_fd_lanuage_title">中文</span>
                  <span class="_fd_lanuage_content">
                    <el-input :model-value="fixedTitle" :disabled="true" />
                  </span>
                </div>
                <div class="_fd-language-row">
                  <span class="_fd_lanuage_title">英文</span>
                  <span class="_fd_lanuage_content">
                    <el-input v-model="fixedEnLanguage" @change="handleChangeLanguage" />
                  </span>
                </div>
              </div>
            </el-popover>
          </template>
        </el-input>
      </el-radio>
    </div>
    <div class="exchangeRateRow">
      <el-radio :value="'realTime'">
        <el-input v-model="realTimeTitle">
          <template #append>
            <el-popover
              placement="bottom-end"
              :width="300"
              :hide-after="0"
              trigger="click"
              ref="pop"
              popper-class="_fd-language-popover"
            >
              <template #reference>
                <i class="fc-icon icon-language"></i>
              </template>
              <div class="_fd-language-list">
                <div class="_fd-language-row">
                  <span class="_fd_lanuage_title">中文</span>
                  <span class="_fd_lanuage_content">
                    <el-input :model-value="realTimeTitle" :disabled="true" />
                  </span>
                </div>
                <div class="_fd-language-row">
                  <span class="_fd_lanuage_title">英文</span>
                  <span class="_fd_lanuage_content">
                    <el-input v-model="realEnLanguage" @change="handleChangeLanguage" />
                  </span>
                </div>
              </div>
            </el-popover>
          </template>
        </el-input>
      </el-radio>
    </div>
  </el-radio-group>
</template>
<script setup lang="ts">
import { cloneDeep } from 'lodash-es'
import { ref } from 'vue'

const fixedTitle = ref<string>('固定汇率')
const realTimeTitle = ref<string>('实时汇率')
const fixedEnLanguage = ref<string>('')
const realEnLanguage = ref<string>('')

type PropsType = {
  formCreateInject?: any
  modelValue: any,
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => "fixed",
  formCreateInject: () => {}
})

const emit = defineEmits(['update:modelValue', 'change', 'handleExchangeRate'])

const handleChangeExchangeRate = (value) => {
  emit('update:modelValue', value)
  emit('change', value)
}

const handleChangeLanguage = () => {
  emit('handleExchangeRate', props.formCreateInject.api, {
    fixedCn: fixedTitle.value,
    fixedEn: fixedEnLanguage.value,
    realCn: realTimeTitle.value,
    realEn: realEnLanguage.value
  })
}

onMounted(() => {
  const rateObject = cloneDeep(props.formCreateInject.api.getValue("rateObject"));
  fixedTitle.value =  rateObject.fixedCn;
  fixedEnLanguage.value =  rateObject.fixedEn;
  realTimeTitle.value =  rateObject.realCn;
  realEnLanguage.value =  rateObject.realEn;
})
</script>
