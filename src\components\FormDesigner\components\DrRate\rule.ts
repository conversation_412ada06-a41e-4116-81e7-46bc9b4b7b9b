import uniqueId from '@form-create/utils/lib/unique'

const name = 'DrRate'
const label = '评级'

const Rule = {
  //插入菜单位置
  menu: 'extendBase',
  //图标
  icon: 'icon-rate',
  //名称
  label,
  //id,唯一!
  name,
  validate: [],
  drag: true,
  //是否可以操作, 除了容器类组件建议为true !
  mask: true,
  //定义组件的事件
  event: [],
  //定义组件的渲染规则
  rule({ t }) {
    //自定义组件的生成规则
    return {
      type: name,
      field: uniqueId(),
      title: label,
      $required: false,
      props: {
        starCount: 5
      },
      children: []
    }
  },
  //自定义组件的属性配置
  props(_, { t }) {
    return [
      {
        type: 'inputNumber',
        title: '最高星级',
        field: 'starCount',
        props: {
          min: 1,
          max: 10,
          precision: 0
        }
      }
    ]
  }
}

export default Rule
