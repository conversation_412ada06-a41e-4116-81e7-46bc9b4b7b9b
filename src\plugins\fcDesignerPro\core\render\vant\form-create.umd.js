/*!
 * FormCreate商业版可视化表单设计器-让表单设计更简单
 * fc-designer-pro v5.6.0
 * (c) 2018-2025 FormCreate Team  https://form-create.com/
 * license 仅限于被授权主体（个人、企业或组织）使用
 */
(function(){"use strict";try{if(typeof document!="undefined"){var e=document.createElement("style");e.appendChild(document.createTextNode('._fc-step-form{width:100%}._fc-step-form .van-step{line-height:1.4;text-align:left}._fc-popup.van-popup{display:flex;height:100%;padding-bottom:110px;padding-top:50px}._fc-popup-title{color:#333;font-size:16px;left:0;position:absolute;text-align:center;top:16px;width:100%}._fc-popup-content{display:flex;flex:1;overflow:auto}._fc-popup-footer{bottom:0;left:0;padding:10px;position:absolute;right:0}._fc-data-table{width:100%}._fc-data-table .el-table{--el-table-header-bg-color:#e8eefc}._fc-data-table .el-pagination{display:flex;margin-top:10px}._fc-data-table .el-pagination.left{justify-content:flex-start}._fc-data-table .el-pagination.center{justify-content:center}._fc-data-table .el-pagination.right{justify-content:flex-end}._fc-data-table ._fc-data-table-img-list .el-image{height:60px;max-width:150px}._fc-table{overflow:auto}._fc-table>table{border-bottom:1px solid #ebeef5;border-right:1px solid #ebeef5;border-color:#ebeef5 currentcolor currentcolor #ebeef5;border-style:solid none none solid;border-width:1px 0 0 1px;height:100%;overflow:hidden;table-layout:fixed;width:100%}._fc-table td,._fc-table tr{min-height:50px}._fc-table td{border-bottom:0;border-right:0;border-color:currentcolor #ebeef5 #ebeef5 currentcolor;border-style:none solid solid none;border-width:0 1px 1px 0;box-sizing:border-box;min-width:80px;overflow:hidden;overflow-wrap:break-word;padding:5px;position:relative}._fc-table-form{color:var(--fc-text-color-2);overflow:auto}._fc-table-form .form-create .el-form-item{margin-bottom:1px}._fc-table-form .form-create .el-form-item.is-error{margin-bottom:22px}._fc-table-form .el-form-item__label,._fc-table-form .van-field__label{display:none!important}._fc-table-form .el-form-item__content{display:flex;margin-left:0!important;width:100%!important}._fc-tf-head-idx,._fc-tf-idx{font-weight:500;min-width:40px;text-align:center;width:40px}._fc-tf-btn,._fc-tf-edit{min-width:70px;text-align:center;width:70px}._fc-tf-btn .fc-icon{cursor:pointer}._fc-table-form._fc-disabled ._fc-tf-btn .fc-icon,._fc-table-form._fc-disabled>.el-button{cursor:not-allowed}._fc-tf-table{border:1px solid #ebeef5;border-bottom:0;height:100%;overflow:hidden;table-layout:fixed;width:100%}._fc-table-form ._fc-tf-table>thead>tr>th{border:0;border-bottom:1px solid #ebeef5;font-weight:500;height:40px}._fc-table-form ._fc-tf-table>thead>tr>th+th{border-left:1px solid #ebeef5}._fc-table-form tr{min-height:50px}._fc-table-form ._fc-read-view{text-align:center;width:100%}._fc-table-form td{border:0;border-bottom:1px solid #ebeef5;box-sizing:border-box;min-height:50px;min-width:80px;overflow:hidden;overflow-wrap:break-word;padding:5px;position:relative}._fc-table-form td+td{border-left:1px solid #ebeef5}._fc-tf-table .el-cascader,._fc-tf-table .el-date-editor,._fc-tf-table .el-input-number,._fc-tf-table .el-select,._fc-tf-table .el-slider{width:100%}._fc-tf-head-required:before{color:#f56c6c;content:"*";margin-right:4px}._fc-infinite-table-form{color:var(--fc-text-color-2);overflow:auto}._fc-infinite-table-form .form-create .el-form-item{margin-bottom:1px}._fc-infinite-table-form .form-create .el-form-item.is-error{margin-bottom:22px}._fc-infinite-table-form .el-form-item__label,._fc-infinite-table-form .van-field__label{display:none!important}._fc-infinite-table-form .el-form-item__content{display:flex;margin-left:0!important;width:100%!important}._fc-itf-table ._fc-itf-head-idx,._fc-itf-table ._fc-itf-idx{font-weight:500;min-width:40px;padding:0;text-align:center;width:40px}._fc-itf-idx div{border:1px solid #bfbfbf;border-radius:6px;cursor:pointer;display:inline-flex;height:18px;justify-content:center;line-height:16px;width:18px}._fc-itf-sub-idx{width:30px}._fc-itf-btn,._fc-itf-edit{min-width:70px;text-align:center;width:70px}._fc-itf-btn .fc-icon{cursor:pointer}._fc-infinite-table-form._fc-disabled ._fc-itf-btn .fc-icon,._fc-infinite-table-form._fc-disabled>.el-button{cursor:not-allowed}._fc-itf-table{border:1px solid #ebeef5;border-bottom:0;height:100%;overflow:hidden;table-layout:fixed;width:100%}._fc-itf-table>thead>tr>th{border:0;border-bottom:1px solid #ebeef5;font-weight:500;height:40px}._fc-itf-table ._fc-itf-table>thead{display:none}._fc-itf-table ._fc-itf-table{border-right:0}._fc-itf-table>thead>tr>th+th{border-left:1px solid #ebeef5}._fc-itf-table tr{min-height:50px}._fc-itf-table ._fc-read-view{text-align:center;width:100%}._fc-itf-table td{border:0;border-bottom:1px solid #ebeef5;box-sizing:border-box;min-height:50px;min-width:80px;overflow:hidden;overflow-wrap:break-word;padding:10px;position:relative}._fc-itf-table td+td{border-left:1px solid #ebeef5}._fc-itf-table .el-cascader,._fc-itf-table .el-date-editor,._fc-itf-table .el-input-number,._fc-itf-table .el-select,._fc-itf-table .el-slider{width:100%}._fc-infinite-table-form ._fc-itf-sub{padding:5px 0 5px 10px}._fc-itf-sub ._fc-table-form{background-color:var(--fc-bg-color-1)}._fc-itf-sub ._fc-tf-table{border:0}._fc-itf-idx+._fc-itf-idx,._fc-itf-sub-idx+._fc-itf-head-idx{border-left:0}._fc-itf-head-required:before{color:#f56c6c;content:"*";margin-right:4px}._fc-nested-table-form{color:var(--fc-text-color-2);overflow:auto}._fc-nested-table-form .form-create .el-form-item{margin-bottom:1px}._fc-nested-table-form .form-create .el-form-item.is-error{margin-bottom:22px}._fc-nested-table-form .el-form-item__label,._fc-nested-table-form .van-field__label{display:none!important}._fc-nested-table-form .el-form-item__content{display:flex;margin-left:0!important;width:100%!important}._fc-ntf-table ._fc-ntf-head-idx,._fc-ntf-table ._fc-ntf-idx{font-weight:500;min-width:40px;padding:0;text-align:center;width:40px}._fc-ntf-idx div{border:1px solid #bfbfbf;border-radius:6px;cursor:pointer;display:inline-flex;height:18px;justify-content:center;line-height:16px;width:18px}._fc-ntf-sub-idx{width:30px}._fc-ntf-btn,._fc-ntf-edit{min-width:70px;text-align:center;width:70px}._fc-ntf-btn .fc-icon{cursor:pointer}._fc-nested-table-form._fc-disabled ._fc-ntf-btn .fc-icon,._fc-nested-table-form._fc-disabled>.el-button{cursor:not-allowed}._fc-ntf-table{border:1px solid #ebeef5;border-bottom:0;height:100%;overflow:hidden;table-layout:fixed;width:100%}._fc-ntf-table>thead>tr>th{border:0;border-bottom:1px solid #ebeef5;font-weight:500;height:40px}._fc-ntf-table>thead>tr>th+th{border-left:1px solid #ebeef5}._fc-ntf-table tr{min-height:50px}._fc-ntf-table ._fc-read-view{text-align:center;width:100%}._fc-ntf-table td{border:0;border-bottom:1px solid #ebeef5;box-sizing:border-box;min-height:50px;min-width:80px;overflow:hidden;overflow-wrap:break-word;padding:10px;position:relative}._fc-ntf-table td+td{border-left:1px solid #ebeef5}._fc-ntf-table .el-cascader,._fc-ntf-table .el-date-editor,._fc-ntf-table .el-input-number,._fc-ntf-table .el-select,._fc-ntf-table .el-slider{width:100%}._fc-nested-table-form ._fc-ntf-sub{background-color:#fafafa}._fc-ntf-sub ._fc-table-form{background-color:var(--fc-bg-color-1)}._fc-ntf-sub ._fc-tf-table{border:0}._fc-ntf-idx+._fc-ntf-idx,._fc-ntf-sub-idx+._fc-ntf-head-idx{border-left:0}._fc-ntf-head-required:before{color:#f56c6c;content:"*";margin-right:4px}._fc-cell{display:inline-block}._fc-cell .el-cascader,._fc-cell .el-date-editor,._fc-cell .el-input-number,._fc-cell .el-select,._fc-cell .el-slider{width:100%}._fc-line-form{align-items:flex-start;display:flex;flex-flow:wrap;width:100%}.form-create-m ._fc-line-form{display:flex;flex-wrap:wrap}.form-create ._fc-line-form ._fc-line-form,.form-create ._fc-line-form ._fd-drag-item,.form-create ._fc-line-form ._fd-drag-tool,.form-create ._fc-line-form .el-col-24{display:inline-flex;flex:initial;flex:unset!important;flex-wrap:wrap;max-width:100%;width:auto!important}._fc-m-con .form-create ._fc-line-form>.el-col-24{width:100%!important}._fc-line-form .el-form-item{display:inline-flex;vertical-align:middle}._fc-line-form .el-select,._fc-line-form .el-slider{width:220px}._fc-echarts{height:300px;width:100%}._fc-m-signature{width:100%}._fc-m-signature-btn,._fc-m-signature-preview{background:#fff;border:1px dashed #d4d7e0;border-radius:4px;box-sizing:border-box;color:#c9ccd8;font-size:14px;height:88px;line-height:88px;min-width:160px;position:relative;text-align:center;width:100%}._fc-m-signature-btn{cursor:pointer}._fc-m-signature-preview>img{display:inline-block;height:88px}._fc-m-signature-preview .icon-delete2{cursor:pointer;display:inline-block;font-size:14px;line-height:14px;position:absolute;right:9px;top:9px}._fc-m-signature-btn i{font-size:14px}._fc-m-signature-dialog .van-dialog__header{padding:15px 0;position:relative}._fc-m-signature-dialog .icon-add2{color:var(--fc-text-color-3);display:inline-block;position:absolute;right:18px;transform:rotate(45deg)}._fc-m-signature-pad{background-image:linear-gradient(#fff 14px,transparent 0),linear-gradient(90deg,#fff 14px,#d4d7e0 0);background-size:15px 15px;border:1px dashed #d4d7e0;border-radius:4px;box-sizing:border-box;width:100%}._fc-title{font-size:16px;font-weight:600;margin-bottom:16px;margin-top:1em;width:100%}._fc-title.h1,._fc-title.h2{border-bottom:1px solid #eee;padding-bottom:.3em}._fc-title.h1{font-size:32px;line-height:1.2}._fc-title.h2{font-size:24px;line-height:1.225}._fc-title.h3{font-size:20px;line-height:1.43}._fc-title.h4{font-size:16px}._fc-title.h5{font-size:14px}._fc-title.h6{font-size:12px}._fc-iframe-box,._fc-video-box{width:100%}.form-create-m .van-checkbox--horizontal,.form-create-m .van-radio--horizontal{margin-bottom:5px}.form-create-m .el-input__wrapper,.form-create-m .el-select__wrapper{border-radius:0;box-shadow:none;padding-right:0}.form-create-m .el-input__wrapper:before,.form-create-m .el-select__wrapper:before{border-bottom:1px solid var(--van-cell-border-color);bottom:0;box-sizing:border-box;content:" ";left:var(--van-padding-md);pointer-events:none;position:absolute;right:var(--van-padding-md);transform:scaleY(.5)}.form-create-m .el-select__suffix{display:none}.form-create-m .el-input__wrapper:after,.form-create-m .el-select__wrapper:after{color:var(--van-cell-right-icon-color);content:"\\e660";font:normal normal normal 14px/1 var(--van-icon-font-family,"vant-icon")}.form-create-m .el-date-editor.el-input,.form-create-m .el-date-editor.el-input__wrapper,.form-create-m .el-tree{width:100%}.form-create-m .el-upload--picture-card,.form-create-m .el-upload-list{--el-upload-list-picture-card-size:64px;--el-upload-picture-card-size:64px}.form-create-m.is-preview ._fc-upload .el-icon--close,.form-create-m.is-preview ._fc-upload .el-icon--close-tip,.form-create-m.is-preview ._fc-upload .el-upload{display:none!important}@font-face{font-family:fc-icon;src:url(data:font/woff;base64,d09GRgABAAAAAE6gAAsAAAAAm8AAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAABHU1VCAAABCAAAADsAAABUIIslek9TLzIAAAFEAAAARAAAAGA8V0nTY21hcAAAAYgAAAPZAAAJyrF9uXtnbHlmAAAFZAAAQeoAAIOYSQ6LLmhlYWQAAEdQAAAALwAAADYquA6laGhlYQAAR4AAAAAeAAAAJAfeBJlobXR4AABHoAAAAFsAAAJkexAAAGxvY2EAAEf8AAABNAAAATSML64ebWF4cAAASTAAAAAfAAAAIAG9APxuYW1lAABJUAAAAUUAAAJb1Yvg7HBvc3QAAEqYAAAEBwAABnl9aCF1eJxjYGRgYOBiMGCwY2BycfMJYeDLSSzJY5BiYGGAAJA8MpsxJzM9kYEDxgPKsYBpDiBmg4gCACY7BUgAeJxjYGFRZ5zAwMrAwNTJdIaBgaEfQjO+ZjBi5ACKMrAyM2AFAWmuKQwHnjk9u8Pc8L+BgYH5DgOQZGBEUcQEAJFWDaV4nO3WZXOVZxSF4TshhZYCpe7uTt0N0qRJg7s7BHd3dw+QUnd3d3d34/P6G3Q9WfyMZuZiTs7wIfPO3vd+gUOAVnaJVUHlPir8iYomf1vR8n0r2rZ8X1XR6N/bciiVVKpatarX/gMHQJ1Vozo1lM8Hfyr8/zrQkaP8uZEJTGQSk5nCVKYxnRnMZBazmcNc5jGfBSxkEYtZwlKWsZwVrGQVq1nDqazlNNaxng1sZBOb2cJWTuF0zmAb2zmTk9nBWexkF03sZg97aeZCLuJi/+UncCmXcTaX04kruJKruJprOIdruY7ruYEbuYmbOZdbuJXbuJ076EwXqrmTGmq5izrquZsGzqMr3Tif7vSgJ73oTR/60o/+DGAggxjMEIYyjOGMYCSjGM0FjGEs4xjPSZzI0RzDsRzH8X4eHTnSz68DR/i5VvnpV9CaNn6mh/mJHU472vvhteb/n3blnzadDv7WXKYsPE+oS3iyUHV4xlBNeNpQbXjuUF14AlF9eBZRQ3gqUdfwfKJu4UlF3cMzi3qEpxf1DM8x6hWeaNQ7PNuoT3jKUd/wvKN+4clH/cM7gAaEtwENDO8FGhTeEDQ4vCtoSHhr0NDw/qBh4U1Cw8M7hUaEtwuNDO8ZGhXeODQ6vHtoTHgL0djwPqJx4c1E48M7ihrD24omhPcWTQxvMJoU3mU0ObzVaEp4v9HU8KajaeGdR9PD249mhDuAZoaLgGaF24BmhyuB5oR7geaGy4HmhRuC5odrghaEu4IWhguDFoVbgxaHq4OWRMusLg2XCC0LNwktD9cJrYhSXq0MFwutCrcLrQ5XDK0J9wytDZcNrQs3Dq0P1w5tCHcPbQwXEG0KtxBtDlcRbQn3EW0NlxJtCzcTbQ/XE+0IdxTtDBcV7Qq3FTWFK4t2h3uL9oTLi/aGG4yawzVG94S7jPaFC43uDUoP7gtKD+4PSg8eCJccPRiUNjwUlDY8HO48eiQonXg0KJ14LHwF0ONBacYT4cuAngxKP54KSj+eDt8N9ExQWvJsUFryXFBa8nxQWvJCUFryYlBa8lJQWvJyUFrySlBa8mpQWvJaUFryelBa8kZQWvJmUFryVlBa8nZQWvJOUFryblBa8l5QWvJ+UFryQVBa8mH4JqKPgtKVj4PSlU+C0pVPg9KVz8JXFH0evqfoi/BlRV+Gbyz6Knxt0dfhu4u+CV9g9G2UdxN9F77K6PvwfUY/hC81+jF8s9FP4euNfo7y9qRforxB6dcob1b6LcqblH4P33v0R/jyoz/D7wDor/DbAPo7ytuX/gm/IaB/o1xI7Q/a/wfUbSluAAAAeJy9vQlgJEXZMNxVPd0903MfPT2TzN2ZmSSTTDJ37mQ3CbvZK1lg73sXgV3OPeS+Dw/kUlgEkQUEF1BYQFhYFFC5fEXBAwTEj1cB5VBfQV9Q1J3e/6nqnmQmm2yin/+X1NRd1V3nc1Y1Y2CYQ6+xZ7CNjIWRmDwzj2FQzIY8IZTtQ/k0EmKJfDHr9fBszIYlj+yF/1Kx+r+QT2M2kYT/Qr4PZUNI8gi8wONgaiSVGllOrJSSyRyVySjDF2zKrxo4Ppewu6RAfTSkBCJyndWe33ThUD4zr7kw79wvnTO/2DS/HZ+amr9ipLl5ZMX81P9mh7Ng0H2FjRcOf+7SdWNNXpPF7pEDISWeSmWyxRJUWygLSnpoZMPGhRcsWHDBwo0bRobSTB207SP2StbC2Bk342EiTDu0sMDMYZYyy5jlzGZmC3MaczpzHnM+83nmCuYG5svMHcydzAPMg8wT0A9Zr+T08Eo0lig488WcrEi5ZDfKBpEnhWK9KB+fKcPMNUAAckAA0rvxREK1X6guUVPcXR2oLoK+4QkGPepBKRiUPn7nnRvEvAgGLyNh9SBJg9jdYk4Eo340VV4UEVtEMNdVnFYRzHXi90Qw14nfFcHUprGWSt3IIAXnVCr64pSx6m8qj0QGT3Cg8i5fFNX7RBEdI2oP0MyXtPq/pD1N/a1YEscNw4+PsZtRmAYY4S5miIwdzEOnx4ag79MIOrcP5bhojIc4by6aLUJMgstlSOcrGdL5USgA2VPIKSvOnDb38d8ahxvBqC7NRX9SXTCTFfQnsMvlEPwhS3F1Ecy11y5bFh+IxwdYS00BsOZklIlil4fUB6DUWKiw5uQ1BXRlj/rHeP+i/jhjqmlHkmnUZ+pcZsksWxOVoiXSogxtUSbfjaYLHLld5+DXyo2r4/B3LrHOI9YMrdo3cCBe9cfAn6GmPR2zHRNB5r1hJCSSgg0n07gPeWWBD4FbKqYRf8QX/0lJTMaxPR/pNqd7Qoks1zh/Ud2NnMkfi/k4vj6fnqERu2yJITMq2VreCDbZjGLPksGwIzjXGfObTH5nXdpWx4jjbZKYOJNgskyO6WcGmEWwk+yY1D5M2ocPax8iCz5DF3wmX5g2AFXhoDYfoSpY0NBVshLjIc4L+YoQk8DX16fr6tL16qA7Xu9w1Mfd6Al10OaBUk/YPR7105H3IsTsoWY9MdhJskKx5zwNAXt9a/1zz9kkyfYcFMJn0qrA0moiGQY89okK1YeikUj03Eg0Gjk3An+ohWR8DiqxBxo8z0HBOR5bpTqmqq8q6zLD9DJ9zAJmBbPm31ifdAJXAjKkQFdAADpC9nrsiAcw1Y+KWS4nKTMs22AoFHwvFAyG7iC+rNHYb/Q5iaV+9r77Zrt8K+7u4OOh0ONBo9FvHDA6faZ+k6eHzH2GgbaThWABqANuCRpF1l+Ujm4xG0Zej4B4ePE0hvcumZBQQtsj+FeRSDkeQf+0KNbLLRb1PJPlNtFj55Efi+gO9Vr32ei0EdL7+CcWy2esikU938hGMW9zm++2mdCQev3FfnQqeTwH/f8X9guslYlBry+GXWQFw7gJPCoVw7gE0KiU06ATvEMbEvhEkoAoNAHlYR1OBvFVAD6peLzZYj4R479idd9lsxn++aLDwlndovEDh+MfP7CFWDf6dGppb9TrUQIN9QGvHKiLBxS3HO0bayk1DaRGVi5IDTR1LEj1tbT0pdDbbqv6yGpAG27YiswWF3bZ1DdNdu5so+mGk412J4cusrpRc6x3aapUSKdiEdltt7vlSKyltVCCx8TU5kR29JjWRS0ti1qPGc1+J5wiFWv9oM1DMhYKrNhFk0cD1tpMy5brQ7I3W+pDIQyoTSwJe5FgQ2n0YQT9JhJRYTw69ZVV3ldZjfjo8j6yePDRYJf/q/vq4eyaosHusPKYm3fjUKwjHxBkiS+QFYVfJSXUQaiArCb0BKkE1pM6CCvZjp6wef6yBW2eP8IL2GR38Js2ZcKhdjNv9TGCNtfwb3W4kWLaoI2l2a4wp+Ke9Dvy6vn7GWfcMjZ2ZiYzms2mFyzAv20calS3gQUG3QDWQDambqP5b4hl16u70JW1v8NhQxLed9YQjpM8Xlgv+TaUODI0KK9mfQffQ30+862ieKvZN9PCnnPRK1ar8STex59ktOrr53V2L5sAvDEOO303M5+8JVkXFL2NEYQ3TxaHrKPLxJ8nsYKGSkA7oBVyjNcWClv0ykoaxXhB6UOY2bl5Q0dXV8eGze9UPDs33Ll+/Z3fPXqsrb29bezojSmC/y4nVirZ0bGkoyN5wqPdS3NX3HBFbmn3o2yipjD1DK+/88k715PC3yO1dKRGlhHsG6yB0mgJjPortPHYjp6ejmM3knHAdO704ifo2mDcZFU46apw5p/Zg3r27FGf2YMfJzYJ0XF7Cx/PhiC/CXZ4L/TI+DYgV+/WODCS6kmBqSONwb8IEX/q+XbV396O3m2H8oj27wbo3zrar8hrw20ItiOCKpO1hYslVMSM7EBNdhRKOX4abgn/VAwEPIJRRE0ONuFUX3WkQsj+s/It3nDYi7f8TDQKnkAAUp1a/T+n40dnlxfABQ/AFIiZbli3NhZwClwq5Iv9kCbYWEA1WK8MYXYjWbP/Tawrclff9JljpfaTr/nKxWOC08rZJGRc/5Vv77u013fc7Qfu7ZfYhJY14OnqNC++6MvXnZJ3HX3JYtbiQchrX7fneF/PJQ8+evNG2+BRVwJQZGl/34H/izFCfzvgvdhcKSrnWIV159zImXOiS/+5sxu5u3f+45yWEwwnINvJ+AflHXd+HV+rnoLC6pvoS+Ud+GpGGy+oayd+lvqtjB/6MUHXUh4wkjmw4zPuauLCWYNe/BspV7WtbQOj7FLAIO/0Ifxsm7ohnUa3t5UfV+APDx05zJCemKEtS5jVgDWccKQ2/WdTrmpLw1+bMqyA+WEaQm3pKQPpNghqgX+14Yd1hHHafqhtd7Q6UINRRmvb8G4bGKifuuoG5ZACBt2uuROvW/sa2pgYDnuX2v3BWQ1Cr2rDn2trK5/VpuDbFKW88bCuqMzXHfhXR56v4xSss4aO/jdSrslsyWa3ZDQb2bKbs+MGQpUEsPHrmV9nwKhXaS7aeeQwhQ3QlrfwIxR+tQJeu2KWEMyGBKlYknJOnXVAdiSNMwBwIQfIlQZcBNhNCwohegBuLEvMSYAp707MicfnJPBJ5d2wnYbwSWCX33MkvMm6NNrni0Z9b8rRqPzpZBtqRN6U5DB7m61+dPeZxgVWXyveD2XVfVod6GhwB1MhdR+pCR0dSn3KIfOtZvktVNcRldV3SEWoTo4uyDUFg+5Gl9kRAARFcWVsdf1N+tx4V2//GLN2ttgGoJqSJwehvELAYi5bknklRgApZTEpsQIJwi6clQWAKCQroXTY1UfoAnUg4UbY6Aidmp+/pNQmxXKdkV45u+WypQMRqS5Y15DuszTZ/ajO43PN0AlfP8FoZs3p0PFzTh1w1NfLcvqytYuPHw75mwMNJZ8j6E4lnQabk9LqT7J/ZAeBDmxihpllzDpmO3MecyHzBegJabzZ0kRfRMf7J1PQekPJJEmbU7Dta64Gm5wKcXOsBrEI5wh6gHCOhFwpJ+dKipQrlvpwIZ9IUh6Fjm7AlEmhQlJWStmcFkwqBfxVdSVt293qSq0H8Ula20ms1+712hsaiH2jXZLszc3EHiUTSX2cTIANyaMS9hFH45r5fTHRKlncDovN7BQdst9uFI28O7bbn/mhmzOJFpPV6sP79b4crPRtpa8Htd79qRSWyjawwOC/gKW+G5XfhMfJb8nRJY80Nz/yCELI7rXa/QZO8rktGJ/AWZwWhNp7BkeR2QE+1ESmHqEt/4e9hnUz9UBX9gFGfxxzKozAudD/11BoLwOaKEfpGrPhZJSneH2RktCCkyTB2oIxSEahAwEJFxQwyQKYUjSMBKUNKXakJPtRNEvRPTsi1JC2NlMIchFGnpfQRYmkQMckS3E/96RRIxgrussdYqEVueZOgtqX7ySUdVfTiy9aXBCNjzO7scOifi+WYSMc18O1wo9DXYbFXMZgHbFybdxiDhlSLtHMGrCBNxhMoj1gd27muE2ZdrPJIv2P1PTDVLtZ5I2WK52y7FRfITaqVz9CFtYdkD4LD+hsVs8ij4XuhVfAG5u65jot5a+a3SyCXr0KcrBZ5Wkuwd3HgUlw7GuCoanJILzG+u3NNgOLWAPH2ZqcPp7lXuMubUMG3mk19QysSHAGLyQ/KDs3OHw+xwanXJpD93rA7b4PuNfhvKxRgOOn/Mc5Wu5quBfvQzCIFIXsR0CqldgYZUWnyn+mOHUK28t/jhcKcWwHuzzKmg7+bWNjMtl4WbKxMXkp8Y1GLgyHL4yovzfViTfeKDjtZlbg16wxGTg2kZqfKn8IFhjsAGs4Hy9/GM/n49gRzz82/AyppPJTTwtHIuHlIms851yOFaxOOz8yInjNDKHbqmigQWYI9tGlsI/MFo5Q4sIrU5hBbEpyJJIK0B1kTlYBZ6k6cESaSd1hCkckKRI2zRfkOlmYXxu8M4I2ArF7W6TizkBTvWi3GOtjdSar3WK1WuxWU12s3mixm20286mEh3EFsSgoodbX8ftMgOlklmsrOFdhyNIWRTVqKgcLF7ZGWefWKtGZExUtsRsB1aU6Q6ZRo91kshtHTaFYyFh+VwthvzH0sitoQnktrP7YFFRCxLGZwEB06FdQFke05PJvSGnTPC00zxTC7zvtJjv8bC6XjXrsQJ447Ua7C37OcY/62njyeAFtLrzOvnDYevkPrBJWSS9pbV2SVvn04jQYpJYPNXZ2NmIEdvkYlj1Y3jg+62c/txmd734PzF8W3t8IVLLERAmO6FQQ+U2Lr34pk8G/y2bL92nYF2rRcDLWcvA9QrHjSzPqrkwGXZmpuJROPfQP9nv4E5gfhLNBNuMQsuHEBMGo8/9kRHdeyqUKomih4iNcLuzmzMYreI6zOl3CqCcgjYpBj3iFaFcvtXlMDpfDhJ82Odx2ESg9/AnCwhWCC7wcP6Z+ljJBzx4TPUHx82ZUZj12yGoCc/vtmvu0FUg+fS5Dv+zV+U2BSVgzR+CREk9yWQDhzmuuR6uvv17dez0rh0sjSfV0hL4oNYadrGU3RELi7ufUXcmRUhitQs5wo4Su1PridfZ7MFfatBlSy5EoEBxCwycoD4+y76hwDuYDIBYCgVAydB5qgy2EAClct2OLxkTY8rvNGzs6PbbTSGsXmor5o4/9zpJFzS18pm106XqbJPE2z26rRayz3m22eSYxIHZSMnj9ogWNTZZ8duzox0eXtKY9Qc83PLbfFDl/vYXrZd3mTwhrmPbR6+zPoA0+JgZ7H+PW0KXcJHzJj2Bp83QcExQhGt8TJDLsvWh8bUOzn+9oROaPVYM239E/YLLvhbnegZ/q7LZ5dolkmMRd8I67tBHb5UHvq9fE88OVuU7m/gdoxyf5+OrV8by6zWPbRWaFuIt0B5SHCbKLvr4+95/Qx5jQMkEy96ef8/HKqnDo5Mh52uRX/dksfgcWxJmTJ/1ubTVo4/0x+wyL4ClZhonnioT9lYjZkZNwXSRnEmwPICfcpJlf1HARQcFe3ixcIXoC4qgU8IyJATLp1S/ZPaLdTeY8dInDyv0ZG68gSaPoLMLDVz83SkpcIR702J7W+u622wgv8mmLh7KN3OPwy8rITBqwsH5mIWBiK5lVzKeYy5gvMrcwe5hvMN9kHmUOMM8QvJhM/SgVZzQhDT8rFtiKoENPqTBfiaCD7HAE/eeqAVgNhVsaFwLABshqJEQyQbjZQGloiHSWbBZJsjXESCzZOKD7dPKjH1ZBIl5dozRtgL2SsGg19i5+tbzNacXLbZ7YDGzf2/xotd+v7vWjJ/xb/GC2+H0+f0M4kQy5/E47Yq1mwecKJxJhl8/u5A1Oq18y2nhBMAN0sFruMFss1CdY1fv89/l89/mrbTTwfuWx779vdWN46v4aqYzORq6IZU7x+cnj/b6sf4vPt8WvXtDY02Q3WxG2CAZ7Y28jACfOYDM50iLLc0bBhOwem9VrFa1mg7jQD38LSekFxII5YNb4WWycCTFhJsW0MB0Av4eZ25k3mHeYDzT4FUQKLF9Fh1rdSFLGF3ktmjJtz8eLBPkm/HeY+310CCkhCThCCMkS7H6SDWJsMKkgHxl6mAKw49EB1zY6GP8EcZOFRL5U6EOkHshTLMmC5IXNRMgIZBFlqGxDogoOZGPNlApkRy1lSoTjnIH3htLJjL7ZymTCkUk2zuPF32giHHBF3UlwoP37NczIGUGLAV96KBL5GTUbiEHbLUEREHqz1cwbsBi0NNosHMIuK7ZLmGWxZMdWF0acxdZoDZoRx1msFiAMzEFrgmTkXc6JjE4XTzIONhgSAYQxCiQMUcQLclIWBMUQD5K4YNyg8EYp6RWEYrilpbelJSTabJLNxjY0DjX94AdKFl7boWTmEFTuQ2jG/UTc9wBB0ah1hcDC6xqMRoMBm1heFkJ2zPFWD8aSjXVG4W08GCHJyvHYHhJkwQDvTHJzSDQIXpLbEQT6Dd7WoTjg1cEftLP2kDHkQWZLIGVAFp8VGZqDFjNyI55zOjnBXUnxW4AECpAUnrPbOX64pY+Id7I2yW6XbGQ/pvjju7W4CNJ33Wj1NKvhW6H4Sy8h6aWX1GMU/PNYrJxWKi7+WvlxPFR+PKYoMfx5JRZTmApP7Of4Wcak65PAMwiRqQBxGR/3oZpHbN0mhV8Ko5atXnDUYIw1xmIHP4nhRNirXoO2e8N7gCz+EDukMDwN/sbh4/d02UNptnIHKlYsEU4KkYfC9M7XwvZSV1dpIxEzbNgwIWZQnxMD4o43CSr0D5Fs+LMQLKgPi+KOtwgy9HcRSgQqMh3tnQkOS6RQs8NeWcrWMCOwgUzToPA4BjqBe+bVe+4bbXqyafS+HfPYeTMgqhepPTt2oGfU3z/3HOUVfMg+yA5QmX0T08wUoUcHmQ3MmcxFs5PZEwzbXS1xjlYH2FrOHexCuYJC0a58IqeBmWIpJ1HQpHgp8yshVHYO1qVTph9Eulp9vtauCHaWP0jkYStxarSp+eDHz5Pph9JkKn6Q6YRdLFQKeCyRvMNu/9mNYqbVGSo939qTb5WTFhwPp02tlpW5vq5CfSzaGY2yZqheVYDw9bV0h9Eb4e4W/3AhriqEAEZvxAsXDqM+ZVsstk3RbPWy/lw8UZfzWbxxl7PJ2+SPhF3RJbnu1Z0eN9fS03LMupHhsURDtxLrjlNeDEzYC1jxML7ufCJpreFOy7MLIErTIi+hZ836Bl5ywyDgxaGPwuGPQv2h/eHw/tC80Knh8KmhuaGWcLglpO4xAI3FIjdnkmXub/80eMwGi4k7DZ2jfgY/HQqHQ/uJws7D4z58tFEUjeVlFtkkmvEJ5ZtEEWNOhk2md3w+f0efz59irpql/gpdqTZMQAVgGRQREaQc5UgBTs4SQEWYdoRgl8ls6EcCAUtEHE/0AIruLJW7g58sY6hM8kK5Ck5DiByC4MDMCWi8jCl5Gm82yBKLeJsD6EvRajFbMM8VzA7stAwNCK0Bg1IwshZZEgSr02gz9AyaYVe2LLWzbq+v3eSENhntvNVtSreZMHJbeYcp3mCiMZkWI0auGZZfX70/abSLyGgzWnxeT5MgSchm5iwu9WAwgTCbjlkk3iyZPQ7zxwjZRM7iRJ3inEBEfdlkbGmxsgi56k3FktHM21ysrb1dj+nK0RiGcen6FdEZ5UUnMrt0CmxK/JGdFjLUrPeaSVyqWfw1eEp1yvYougCA/iVRvCZyYTR6YeRQBF0QjaqXRCrurwlYLT8LSZDhNhK4LXp3JHJ3FK2lGh7PRPQ/9WPdJooCOyIHImDUm0jc2vEAJK2NPhqJPBrV5+6H7M2smXHoekp07hKZdBX0INsV1fSogiA1rCLbtgMnnXTglXWr88VifvW6k9csXtTS2tqyaPH+ikf9bRTNhVd7EraZkw784sC2YmHVutfWri4U57S2LFyyf/HClnS6ZeHi/UsWtrSqL5O3rPBvtbVVGb94lX4QU/MWNThivGr1cVHCyKXrDo3LnWEEctUBdFwMBWMx9e3YxzFWAMD79xh+C+ar+jSZuXnV2RYzol6g9n5LQO9VxGITZKO9mnhR+df6HI8Oqw1Kjs3H0Q5SWaVS4jIV/s23a9qTpdT5rFoia1uItoHkaIBq31J9sc0KCiiK+lvlY0VrgILfJjv6M7Ds/woEWdDjuVAn5TxTv/r9Hpv6NmVgBImtvq3RvySk6zm8xt6tv3sA8KbOydLGyaIqhQiqgbiMVrjdsLURbIPsWbsLPyqAwfcSkfzzxPrON79RfgPjzwkmk1A+y8J92WD4ssHIJgrqCfk8uqkAaNAJUjgsoZu84WH1s+js8tssu0Dk44JpgcCxp7Bm9mQW67LRJ/ETh+mTzQ7PmKRoO6WfDU/HJwaIXH4wX4C//Es1Dn68WR8McFEPuEMAXJ+hwLUnXugvqJ8UCshYmOxSHvlH+A+Uh0B0KQg+GQQ6inD/MlTfpEq3ghv3sbOMQxsWtPSmUr0t6MoFYIP/kxkj8PdDlDAIa456G9GhSqXCmgNBjWzQHGYcH64ek1nKAas4YjP1OfoV9Fl81v2sw+2fw3x2ALY3wmyl2LMmEiN6W3m69/GVzQ8WWrGy/aUR0deuZCyUKFO9Ok9RniEdp3Zu3rh9+8bNv9uyYceODVt2LL6y0Ch7PHJj4cqbweuVJC94UdHm0TMhLNW7qtI9tumT2ESdf8WJK/x1ujNcJ7skVwh+cl2VV/2+x5bXcuU5qxN7AhM5gp73jpSo6QQd+gj2Zgv0pInwWoF8R7KQLOGzH1BbHsAn3L/y/pXEn/oWPv6BFfevGKdX7oZV2gyYP6FXCINRySkT496L+jCZGgWpdm7E5YxXQHwykyihInrd6kIuK3qOEKPoQyWDCIvcSIQMjiaiYtaEj5dlWSWWjG724o+Qu86l0a0Z5QdGu9HkMOIf6PIIQsTa9OyATt4s67jqfvz7KfGGWjzBOW2ghpwsVUObWyL4VQDpTZFxFy0C6PhwBP00ggTw/T3yDAXiv6eg/xRCYaORCpi/LKJ+AiWMkUl7XYpJUx3jZQSXmc36QoqgADmlAM7IKYWcJsIUYK4KVCxMhZUFmPAKIVFSQK9kOU3sXqLsMlkh3GKyRDwUNcjxR1yjqv8YR3ZpcWk+EAgh+4mnNTvNZt7sdIn2erurs8NutNvNFp/Jb8t22JGjLpntiARSnfV21M6arH6LV7BHMlaBa+NnWOLqC+m3316+atXyt332Jgs0pb7R4ePY43oNRskpGFJ8yJQ7y9ff0R0JJiSvzWw3i9LihNPoitP5+Sq7C/aEFFB+K6kEgfz30/MZmsIFINdJyidvQ07CY/KSFU00igkCD6ks5UGRdS44aUYW9gDCXiLsKI29lOAFHHIpLX3zGuIukxlxNmfd+odZm23/Rr/TxiNkMTqjjUf1tcctvMNo7pw3MN+MzGHT0Td96ei0HZnnD8zralxusHCyARnONFgMPwfXy1kMbMTZ4FcsAkJmo3qGwe0Lhk5bgA2GhaeFgj6PAV1pFBFCgkWpa7Twy8VMwMcj9VuhhLh2sG95exNCo4jzBzLqp0+ACt1Q8dUcMvweXChr0HTxYL4dwPMABjHuNCxVr2xCmrIrgs3uYhPPOb2yEa1EK41er4PnjeoVVsWC/mn0yg5eMKp3QwLPO7xekmDVeeRQ53dwGLAjESAbgWqLCUwTFHfOrc1NZ/XctKGayXmEuRkbn5jIero09+T5Jw3HE0l0wuevK/lsQO/IstUUckryooVui8djtlhirsGFbrQtVpizMNXQtUjxoH5edA41Ns9xmYQ+I2rsQ2n15zvOPHMHSkfcRadZMMbzXrvHYLh8jBMDsonrEprMg7eFjx0ZTcWzgYBkcZeOz3ktckaHga/j3zA+ppXpouenshQoePgKv0aDOhTntmFPSFMlxhPKGxO6olUHr3D+mKUjvZ2DQ519I2N3jo30dQ4NdvaOLD2mf27vCT29J/bMPW9O74m9PSf0zu2nsr21i9OtS9L16fr6dJFY+DcNkY7CuSecU+yIKkq0o3jOCecWOiINw/V1y49bftyKukCgbgV4ltfV76ClqTWslwZLb9uzsK8HACMZ0VYOYbNqtCs0SYYWKloTgYQNIy2RcDwgH2X0Qsu17pA8QpIokiOyuYA3XszKmhQvCbgjimeHlkNz5vR1dJeyma6580eOHhvp7Rra8MuMS4JG+Zpt7b7BeOuS1vDnjx1b0NM1N52ph46YO+DydLerB8WAiM/WhXvlz4n4ymzL2YXUFmilLCWy6bpgQxQ6I9e/+9h2W7MPKpRcmUAYOi4x+DfaT/ne+e3pZAvpDtxl9Yli+bO8y2XlOAGfJQZ1GfV36DgnAV4caZSJCv34+bnqAcaSPqZdvTCmS+fDmM7t6lkwdmysr61OG4MlrZURrJt2BB+ta+uLpacYsPF1dyVO6/i8duaNqdGTUybUYXXlIimHpoq7OXNiBsxV5PjWieSw1qoFC/Aa4jmRRJFQa+aTDJig5xMSA9bA66iuOjSuZ/wAfoaxUz1CTUgJ+2mOkhDcOOy60e/6wGVZybJouct/8H2/C3/f5Ud/vcPlt67EflePol7trKtzoh0MgZGAp5wxxfmS2eOgU4seamjgNzW8Q+3TVdyfUvuoesNTgKKopQhaCFB9fyTyfDT6PMBuPxD2786kFnEaQQEe1BjrGkFc0eX99fiYRWEvScNusoBZwqxnNk/SjYxOMVbuabUiZzvaV7VtSqc3taE73fX17s3EOvaoo74EMRCv/jG9OQ3mMhK9yVNf7znmqKNwgXg26TnR8ZqaacD9Hol5zx3o/TEy6SqoZ2sujaYZen6MjNUhHW//JuW3haH13VOPI4pSlmqUUDlJKsXr1hdaqZJCQ6xVI3/VsOaik9WXKQ3cAjTzlteI9zVKB1+k3krjN9HQ4Zyt8o8JiR30PO4JSFQGDB5NW6DCx3iajdA5mKvS4jmFOX+2/MLJA1F7yIurnpo1GsrxGUqyDUdA2k6OtrVF90TT6eiePUar1bjHZLWqEWWlMm7QG5G2tsgemo+kmkg+NkJ65K9694g64++vlPEnxvPb09ECKVWIprsspgKpuGCyWIgCsPoJsefRNJqrS0sjuXT64ec6r3Vwdj3HSgS3paeTnDDgKVTISQplalH+SY7NTTBIST/UMEgf+R+7x2LkDCbJbSdeE8cKsnsGvmb5KYtF4MxGBTUrbrMVkFazoKivKK7KeZGP2GdgP/LAbtQL0PJo2g48K46xlEaEMwwwBJpQKHrlPhzXtZhqBIxsItzV6ptORrJjsK+7remqW84PRBY0LEs3Jhfe2OvfdJH6Tl2L399Sl+zoSFrd7jq3m7X4W7oiakLrGfQ6WEQmkKC49uvxwtt+X8vJg/NsGxes6AgELfNc0bbBT3VcX9eSa/F3LOnoJ3XUufUzMl+u4nsFZ9SCGCjsKYBBo5qrxoeH8eP58of5PHbkK+62gw8zTOUMzu7a+qfXhE9C/Q/lSbV59LpW/ZXT1q7tN9dC3USWJzP1hP6rcLqI1FDjdlU0N/6yh/Cy9qDH9xCPKkG9e+D3ubBXXe0Nk1EIe9Feb3jPwYfZRaR+Qkf/ln0Q9oYg3csow19IYyqSDhHNRLlyyoUi2iQacKMSTQJ8lMQkUZuydvOyQMbZM2+obmfIK3WPjQXXtK1JbT99WTDYhHY279x5bCjSvKsq266gJHWPjobWtKFXlKgp29gebjTJ/l3BsbEuSQqtbVvbHAwdu317M9qZioSP3bmzaVdNttGxbo8Hso3D7e+gr1WdD6pAxtMa8cHGxrKhEd1OFcJctD9/QsfKytigPwNU2lZiOphh5ijmGOZYZhNAMnLeHvZGtzKOIQHSyx0hhP5/yPnzUSneHJeohfbWBMa8DakGL7XKX/tPZsOvSnq0pJvJXvX6//scOv73U/TTmnH4F/r7pelbUx24aMY3qeBot8GeKAP91wv03/HMmcwXmJu1nZHo/eCJA87dSJgEwpR49TFoNPUx6MNrwlPXhGas6SaiI030Zvbpx6PvR/uI5sz9FEP4GomsS9fv045d79tH1KGIpS6pKUc8tQX1ApPLsRaiq3P/eBntCDUtA9YcmggvQd6jNkndQ/NXVH9oDdUl9RLwPFK+KoXOCbJOn6W6BQrTRs81LqPr8jzm8zAydzEPTaNv4Ba0Q8FUDSeZsCNdLR4yxAmHRItOlhRJT4BCcUXSxZkkl6LF0hS3plUvSzlI4UggV6BSTvITpJxeJqqxZGJ6bgD4SWG8+v5xF3Lh8PiRyt9VlB5KZmTzuG3Y6pHsyNz9WXM8auTauxysZEFW1uIxOLraOSHW8OJ3WYG15dosXL+5rD5p6eOs6ZwN4hB6EmGOtc6bZ2U5/JClLWcz8Ox3kblfvQEi0JNYwJD4PNdngYy8wVJsR4vJM62s9kyP25NM3u/ozBjgMSJrkZ41QJq5ISbAe0ylalG+ELHFhGJA8USHARn9rNVl4OIezticNpnSzUbOkzAYXNYXXLms0+CsNwqjx/H8caOCsR6C2ZzL3dzoRqIJg4tkEulrbfUKW0bnkARHutWKTaYFwpItRm+6VWZdwaqHsUg4k/PEOaieJY8yJrIm/fGazuPr7Gkwbyp3qRAa5bA7UrioM4qmZZ2yJXKupPw+sTG48Hswh6O5XPnXuYrLJiBHA83xf+RoR7mB9eSy2VzZn83lstp73MYuYjf+X72H3a8ofjVCbPQbNYJ+U/5tEf2xWFQ9xYrLblT8Bw+QHOx8v9Jz8AB7WRH+Dt5L7Ar+8BTFWeOAs24al7Hq2qnkNfqRfqqHqmOSFxQUSnqVdJhfUQzAFDssxgkuXzm5ABgIEfR3Y4oL8+jLmtzuDSBEiIaykWwjb2jKmwf/bvM8JTgJt4J/+j6qCHiIszolViLxmhbzU/dCPPLYIcHhYRNU0/kNuj8x5LjqwYeByKHhQxDGaMhju+8pgeOsLhf/FDn76sEeC2JsnmdqEiw8TTjEkM2FnLl9hv0l20txeG9OJqoLUo5yhqhSElHZlXJZPURUlEjfCJJH09PTRIhB2Amg7QQLwg0Incfxwso/WLkbDIYbOOsfVmIWG9jzDKIBEowr/4ARazLs5rjdvPX3K4w8dx4rsp3oBt7yh5UChLR8UAEi+bQ6/ohIJSxiJ1KhehbputVPUT7xCJViwIDVSjHyZOCkpFyN0+McRy5zqBrLCUVcVMyi39lcHit6qUJ8idDzJoQqpBlRy8FfF0X1rImBo+Jc9DkxwCaQVO9GFSrkJRFKmpwm/FIF+yfkgCgGobSDjpHbekjT1r0XijMab/eQyj6E/wEQmKmgmlG6vxPykRKPQHVEZapUSYaklCihPIyJA/r3NHTO8gXzmlt8EV//KXPUK04WsYm1RM37rBhb8R+NHGeXJOG0luZ5C5a/Rc4dvTXnlP6TxJCZM5uhOW6rxjczMHg/4NlWxqmv1GhljRLdKnarXzn4keLH15dPxtcffIX93sEBvF/xlw9iA6w8taO5R5Mxqew3WMyEAGoNaGdECNJQwdltAPQBgVbGpdTasBHNItkJCVGaxVkskZMgVExya+NRsm9e8htE2vRgJGfpt+Qi3yKBb35TtFtMhm92tkIM2vcNIZI3N+Uf4kwWO4sbwuGGfEP5Lw15nAiFEjjfgG0N+T67ufwXkx0rOAxxkFJ+M8JiuwnbzHZ9r3gS5pUL8LH0hEafpBTTmLx4H+YkQr7CaGg6GoWch956k7PheIEQujBaWN5JlPiG0VHn3fLVCxsWRs68Wf3otge8IfT1HURzf+ioc/d89YKGRRCPzBAf1uDLzqXXHpdQNo2MnjVn+d1DIe+Bskhg4w6Ijsc2zx89c+7yu4fD3gNUn1fXyxeoRjvRl2tlMkyB6QTsbQ7DlMhLwusQsZJ2NUiOUN2aBEVAkFbSf3H9F530e75/Wx3q2Xrm9k0be/us7DCLnPPnn7LjEvXQMPljFw2XN29FMTDbNPusVcOFrXuG2VJhzdrTt261XbjthJ4+Fq3Ytu3A1q2Pbd1a2LZtdHj8r0KzvIF/zngJ3ktWoRzCNiwQ/T7o0D7sJsrzFa35LNGmT2JD32ljzq51eUPnpi41mF3ZaY3NzwhLzv2FOWQ+u1kMS+ZBc1i8zmzGP8ut7eHnfGrX5m42PXbcukHrvJ1z1R+YIZfZG4Jc5uvEsHlcJvodimcFmf7pcKpEhbAHDEfKUSmHV6qcASZbZ+UClgT27CSD3NlBpcPEs2HLqU6fL+b3GRc0NRa9/nxLWjK4SzYrKlrN3hZz3ZIpEYxvIbmBFEO3p5tHssFYe2PQz7sz4UDY4Qh43e6kVUn3zp845/B9nadRBExxBHDFWXE1DtdFjROnck6HgoAa3hYbIxqQ0woZB2GP27CBHFA4UwyYzxRRkxggEUESEYSfWlaujsWuVmZibajHiaScJyCeaRbPEgMP1wZ7dRVc/Yz31WyU6o2WdOmHzpfC49qiU3CWNb09j9Zq0laZzDQqTdVvLoklYbcFhJPqNfYhGX/SNNQoN5VCqiNUapIbh5rQh6pDF4Jns+ppojhvnuiuN20wgan/uDZ4HAB0XMTeOmpb2QjhVztCxWZZbi6GqBh8oCIjJ/W9KtaJ8+eb6t3iepHWMSl8AsKSVb3Y6mC9VnQh1K3zOZ/X+XMVXvtSwHk+zVxe0fI/Mm0lTys0n03pWVFvtwczgUAm+O0AcQLf/jY52PxthyRdGUPzFUU9EHuPjK163jTZ0P3B9iCYb+uOlgAWmyBBPTt4hyVHpdAGopl7Z0x9NBZDIzF1jp5Dzz+kVUxyD02bMnH+Uduz0hpsO2zXIrJBO8CKEDnmyKZRG6AZ7OEK39NtZPPya7p4rr2taB3JoF8n5yZYpT+RsblPe0oMi81nm0NeunENTr3BjWSW5bNW69GbBnDj3CVDLRzXNniM+n2zGUpKdF8cNIc0ev919heUp0ZgvZepA2gdYxLQImcUIEXUqRRYWUgq8IvDL1cAywk/N2AEBB1gXy6vG8Z1J37h7KavbT216cvoxluadkfvabpoT9On2fI6vKH8Dq7D55fX3XsCrhtSTWc13X5K0w0ZyPRHyISskA0thHx3DJffqexfN7FzKa0gM2EmSfTS3UTyqPVZki5RAZVQSbazYQSUpJscEgJX1t13HSkHXmG3H+9osZfvcTi+MIoHxsrPbm8/vdixdWtH7rStf4j198dCg/1/8rW0+KS2FtZiJzlboAgp2WJPjZW/O4bOPT1z+lVbOztP3NzfiTf0x2L93YnY5hbZ19JU79Nl8F/CF5CTVW4ysFUCeHjL7ZYGy/+iuyri926LYlU/sKDHLZaP0F0mjqOy926rFTkt5KgCheX/y97GMvRMhFnvgXroA+2WKDIitMuTRLmn6gfj04a0X7KSh7314Ef4BXL1zFn3xu7N7Y3dFd0TuzVxq7Lnqrtid111a+zWz9yl7MWZ8rU4o9rQX1jm4Eespb+8407I/t29yl0/26Pc+gJkU+sh0ACeCLh4DeQ/vV+1kfd11pwfOPzM+FrmRGYr7DdnMJfM/lzs9Fr7NSq9RAew4hemSyjVQKvokbTWxsiJ8ueJLiZqpXvOFbEFMTDfmcr5tjKigMF/JtovcV1a9Std+yVOsH70K6ql3xsj+vm6UUdI9eR3QY1HX4PfPKwfl/8neo2lJ0cr5627Eb24g7UcQRO9vFTrC6KS2kLe03uc0WBym9WHNGq23Cx6gv9q28c+JXjdZkSlcCnACZhxHdxvQbtl/Ra0YZg12yewlQmYffgJD3e0ALO8EEWZEj0XVoUgyrmsfHgsISqmyltkPWHtIEdYjVd86Ffjbcnnzz74PiuTX/nVHg6xLGfn29o5h8j1cIKB52YZxyZIxeUPK4/QCMEaaZWqDqnfHkKlXoOVM3BceztntnO9BlSujeDKPZyFZ3m+rY0323l4FtL1Pm7DZ8GuEWZaiEYwqpyp14/aQw9SXZWKvDU+vR7L3YM75s7dMai+Orh9EAxqUl/NHJsBg5qyxFW/PkQSPktTacoWYuEz5+74DCkJ1kDm2ONIHFgDU8ZqfE3tPGpKP4V4FHM0cwyzgdnInMzsYi5gPke0j2rHnQgND58e3axTawohJGtOpkx/RrEmgMjNL0Rsp90AQ3pk3IdueW78dKjt/fHjmxPnR5+7NGIkFwO4V0dOiUxp1DtQVP01oa6Jq91BMO7DP/LYJg6gzqmtGx9NfOVboABkxnNt7ks0zcOKdSlVSZw34K53XTqH3k9w8YDR7rKZqu5R9AI8J3euLJ/g4h/p9K5QtU7SyIa92kLhpj4O3HTk87R3bhYwz5r8xsWLebvDbDDwmwUzZ0ZoqrO5Gn99urOwP9oseE0GI9TEcQaL3cFtNiJ0YPJ52kn3mA4xW5jjmNNhXzkfZtQVs9xTJx1dt2lIrDLOGnRTkkG/0UAnkejWSrG70uwm3ZFv9lhvc2W1mZKlswtQP7ct415vMikKbzBYHU5eUQgNoCiEsgC7bnPkjsgkM4Nmy5jLlqEau5VHuerdGZtLfUEkNfJOh4Xj+IkHuME2/ZJMu9eIdQa9DmTiPmcHEwXcUTtVPNX5lcm3LtbcRDgOsjCz7cC2bQd+sW4VOcWyat1Jq25ateqmhxYvJCdYFi5enQk2BsE4ycNZy7YDLz+6jWR7Zd2qQnHOyq9866ZVJNvDJP9tUjDYFAzuiLwfATMu83+y5s7m2UPZGnwicoSjj+VR1njwk0tiD8fAEGibIuQt1ZKl5G0+USFvE/mrh9W36WlVjf/wIx0PKM5SG4VIqQV6fppqgYYR23qEFzvdFJSwSX1NjIqf4TFvdzn5k2fQodgjYlfArN5lQZb7BLfTjHnjF3T6cmrcb/3s3pzI6d2z3J3phcE2pBOS3iPicurW4eGLYk/HJhn1H70838tZzYJgts7Q5NTB9/AiggDtGbfQ8b28i+81sJyZP0cQOVzhF+2HPggCBbi1QlXrTGUlHiNDUiwR1WSiU0nODLE2TUaQRgT70LfbFOKTSoyqDumiz2iFTqYn7zXiOaoT1BggIHoi0BZ4M15iS43q2VbbjS6r1RVcAltPo8Vcj3i7ILq4DqPfJuxGOwW3WziEWRzwoYsIDVy+htixmEba4u3B9oAjmJT+Gy+BXfepjuTnUz1G653uXN4dZOvRD5Kd7DCQKf8lsEahYGRtUeFWv+likx+hxl+ZpI1e+58IVfwnQi+3B/+kEep/kpJBx9Cke94r99zOerWR/7hTof+zuDeW3kTTNOfAnBl2vH8MXDDw3+oguXgbPaHf/VK5H3zy202De5YoAqKQG+GihRnu9P7M+LXAM17X/QvtQmFydfAc/cwd1Qv94WzOPkx7pWRNoEa+tiuKX4lGy83R6OWRyOVR1BT5TAQMuJeTqPK+Sjr+IT3N+Gqk6k99jeAdOu3yoi5jJKNcYrqZhUD/TSHfgx10ijh02GE1KSdPle9tcuZMfYlo9aCg+r/Ieg+N6KIRj6ir0V7UQvyXE+uM44+3E1d9k55UcxwCch1tDkGYZg97Q0OHWIGke0MhEg4PH0JDteGLarOT4bDR9n5Xp1lqKd61zDpmG3MSc9bsKJhafdZpA2yNqod8hBAbiXS1+HwtXZHynyu+GspO3RFDLkVR/xSb7G4t2HxBn41ahWn8syBg7iRw7NaJPbPORsoG/Dab/14bsSd7dRk1ufNXoueQ+qEnj2WY+PiZo16UkyqYKVtzNwn1wd5BtAphyhRy8JOJlSsoXK5EfIqQpCkl4i+weyub/rD2uqsm9n1ypZBteT6/PO/rLHTehep6E2x2TTi0Jsv+9urVu4MapAgGSUuD+IsTfpKivrhs2Rvr1r2R+ta3Um888sgbX/vaG48k32h+COaLXLUHxoHGmbhv53hmB7OTuZC5iLmSuXaKu3e0G6HGUU8imY5qyrMSwZoInGhDKEvPXBS0CUBgBkCVKvm1do1WvHo+FWrOtM7u2wZETS9Zg/xUBzLVyBu5ymyCZVNHSJ5PUaoEMEm8lAbd9W4IlO8/Qto1lUvaKy7qPRy1fX3OnLbI+mh0fSQXeTZCTES3WQvU8ymNZLrc4sKIBEn9NHDwvSOlPkoqOGn8srsTxy2v+qpCv6LAEx3wSOOEoedBp/t2wrH/hnY7Ny0mNEHnHhkkfhBBddBx70RQo7a7a/YaiuvPdLE6ad9ztAcobSmrz2mdhboq30nA10I7q7BnSiYV9G+wgE9QNFxfx2A0VbEaLf3zRs5fsOD8kYULOzZ0gFm4eWFpUwnMwoV6wu7KSTx87cj5158/MtCx8fSNHQO6Q6M+1G4jNFXBcE0Hv5uZByttdQUno31P36/CQhBqLnzVN+hoDRCtCchEp1QuyaVkKSmUFDkphHF1SGabNO2tLVsINN/ynPoqhepN6qvjWIEOXjUzPDCg/ijodgcDjZYGvoFTLI2n1AbxPYCPqXuhTkAS0GrACQgWsdfuGaCfG1jtjgfs5Su1LxGQYcqWF6E2SVGkoBBzJpPOmBCsCVX0Xx4bvx9/OgnnTLfjV3OKXBMSzopnx3pyW82Tx2i31Ryzcf7Z8+adfQ2x5uVXFwqrtxJrFlfWrJp39rWkFFjD+TUnrsnnV29bnWeqv0VAZJyNQG1OgbVNQW8AxhbXcclkkiwAInA7jOy+agBlJz4JlPQSgdxhS2Tg4O9ZLyI8wfGzIHvxX/QzBSU6+9ZQPtZWwAh2/gs8h0kHPKQp4g4/RSDlJs6TyxNnzI1HupvnWoLmPEaQm4f27aMOsfbdcw9aVB26Pq9d1fJQIUzcsMb3Xa3Tsnt1vu9qSsvujRdKYe82gm9t84a7NpwW9m4l1WwlgZuqA6jkJZWFvZqjn2H+Jfs3NqWfOaTXRYYrWtZh7VMDhCGlfXxFo3bpnWDkZ6eq+EQfBhNyCycTxX6cTKMkPsZh3MhJ1lfsskkw32wy4jg2+0K275rrLE/aQj4zhI3iV8yCSba/YpH4jaZmw1Jbe2j/pR43i8Jf8xf9pjvCnN0lXPpIfdrGRgTn9y12HMbuYNB8g8lvfdkecGHhOAEMUMn2l61+0w3mYNANWeyW73ts6+6sV7i6i9z5etP+ZVZsNizbX1/wCBfVGcK+Cg/8LfZdtgmwnxV0vuxgzmUuYxgullQSyfH/ErlZvPKNF8lLjwUnBBtpM/hy3hI5gEnWbk72lujKTRb0ovSYpqzdxyZ7WVi7xTTmSx4vrGCYJuCBtUxmTrWHziBsjEtsJtMYD/ic7FyTMdlwxYr1605cfVS734X9w62jqWTQK3KciIwNtxt2Bnmj6Az3AYhyB7z1rna31dvcu3D5kiXzt4cUnu+0e7yhaOMdwRRgcc2hrlBTCMyN8EOhVPAezQ0tCzcFg83BkIGv98b8AQ9/3Mmb1iy7IBC2O0c6lrXmePba4RTMbc5rCZh9rfYlI+5BEWrOuxs5zCLB5Gw/FSWTC9u7bZY63/GDC5euW+WWPQGXjLqh6aFm+H0GaG94RrDfEwqBM5c4odAt4MCzK3T9kzpvY7bfZaGsP11ZS5PsHplD9I4YFB9/XNf0exkCvxBn4Eq8LYpPPEHOS7qcwsuiCGUqusEEfyZSW6Id3M700LMs6wGvvIn5GvNw5f01enWqO/1mf2kzKxNNF7LxlHKyFKJyfHIkWxaUpH5FI51nVJsSsni1Q6dkctL7/fSzpR4KbvKliVMypTAi+SVykTe92LtEt6/x9sd1UdM5hK+2tvbm5+t50T4W4OMc4r0c/8VIGnEJXjLb7BZX1GtxpxanuQSHDUbJarTxFosUsngb303JoZC8nFgpT11dk99/FTeXi3Nco2GIOHQoCI1JHl4hELTTZcNP1VwXfTF3WZ2jrlnhOM4g2czWyzkuFuU4myjaBYORE+0d8YYIz5l5E+YsAsuzBsFoDwcakC9wUtDnC54U8LVEeiJgEuzO7Rz3JIfP2EmcKv59hbKe5TkrzplzypLiBKKHnLhyEhoIIB8QRgIhjgpHRh1/0vHKdT/pWQE/dOYDu+BvBnTxeDWHXsjcoP51fewGJK6PdagNPfeh3nN7mHF+6+1VsHryjSPTQGsTkhGLZBMGx6Kf6HQFS81ecqRz0sfvHmktL1yMHkiro8hCnLGZwHd5J96fLi9EC9GDkFv9CD3Qqo5R2n5CXlJ9N9VxzC7m08zFzCXM1bOToMzy/lN3haf3b1U1k+RlmptMszoHatalNKHMbiqUAVdnaO0mJcA6VrueFOrVcgPK2lq/u1L1boKmVueuXGcKc8Mzq/6+hrmZ+SpzN3MPs///fd9Pq1lQXROq/oyS8z84QureykETtJpg/hod4FH3+K/3g/lv/7AfzCjpzvJ95H680AFi/pPjtaoyXo3awxATUn8XDKJ6cMgH+So6h5r+gqfqG4uzpS1qvgs40dkoiw0z0hY3EH7Te7E7FOWO2Cwoik/FPomB+URXGOQOo9m7/g1aXZ4dRa52VijyF+jF8rBFkR1NI0XQn2CHq9mhLiYkXWcV4c1M/v5AhceweJY3zNTwgGp4wvSaQyhITzGGZviE6gcRdD4049JIhFxdF8GCxbiUFw3YsNRgMQszgAr1EKHYzyNEq3rNUqNFMFugHDaIlKb7C/siS7jc5C6tvupWJQVy1IAgLMmattEbCUpEPXyKNrLLoqUomB/221OOuc9FilEc6Yji1eW93mBI+onL1tVntvMvSaGQaqRKP+pLMdYa7YiqjWChPjtrmxPOR9CvIXRUWFIbpbC1t9eMwxJ6zRtSVe2C2Qn8KzyJ73MGcwHzWeZa5n69JX70H0HANFxLu+7Ho13Sod9tpWFbXsBB9bs9SrKGSR1+6TGkUFxL8sg5eBVZolfp9CKSO/evoF5fbEizhnYDYgWnTTQDEp1a6TWbDRkDMhi9ZhOPzKlIffMvDJDH8EimvX1zJpNx0xOH9xj8HOdHCFlNnEFusIqSwSDpn+r4AJmQ8TDE65Phpxur/tSzgw0GA88bjSJvEoJysMFtqrNxBjNnRBxrFkSjX65Pdhv+ZgDT1XtMd/cxvZn6fD2YtYY2Q8ywVoCZ12Ag11m6T4Zwm2H8Lo5fAC1fkWf0a1+KOvxCvanjqo7NaPQ55cSyhHIl+kYAVnIVqV8BZuyZhFqmZPg9N92EO4nnMRIFIfVnLOrDuA8LNxoEwXAjuxkCCE2E8Z/DUvmz9EN6Z0vhofJd+JbJETJkN+BelmUbeEPEICiYVR+gcX1Yj+MbMKvxVH7NvsRGYQ9PU/4ZY0Ljs1VG9EpcVMzHKaOM3PVKT9poX/pAZPpNwSPKUTX5e9XnWzNqubTY4wbCvVTf7kZssiwIbo8VsTaPCRVFQRDVsslT7zGpz4vCL5WuWKxrmFgxOwFLdl8shp5BxeTiklqG8urzSaiLTZx2yOpxk7LPa2XLpCJUNHlsb+vlwRpy1Ifr7bH2mD62n8Yfw6qtA3qbaBwdwzDuCaotkZ/QzQV4NX6hknbqu+p+pUQ+jkhGzV+T8MWzeKfTCjPzLLHeg7uJLYLlNpVfRNdrSYJ6iu5B3qo4yKR+jEKkgPqmKeBG16OQ4HQSDQz1LVLZ9rOA+rM4XfxZJnd9+bWzRHc9VO2pF6Ga6wSDwep08urJvO55vyoO8ltQmGSFmtwBdB0K06qcAg0zOu57h/7d9Wo53hiVaxE+1hk1X2WczI+q+YKSPMu4WtRp+ku/I+Rumi30OpvRUbyGfv18F/1C+SXq5xD0AYl4lUaE1T8g6bCI8laKH6Fe2O6fHnfJ98+36FX3jH6hOrAnKG0mzwOre2yP/nBI7h7FAyGVAxTon+AQFAg1kZrH8aDnq/QZK191nVEWSPiSUfghXauRTVUuly7/eVzeRkR5hBVAlRx2DaN5Q2iedncBu2gWwrnhSl71MaJmqOl+v8m+wXpq5MtdRH+jRrZco6ThrB6xqRAPmUrmYrwg6bIqmW54ZP8o6LteclUHznZ2ll/suCR3IJs9kCOnRfEXw7kwGPX+cD4Uyofvdsgn2r1YdpzotnPOit9jYz0EFVS95Gwp+n1OlXM59H7uEFNVFC2FwPGHleS1GnU++I+q7qiobW/N3bQXjl+a8AKBeuhuAnvQBSSGpDA67/JD9kZ6d2/rlHpPWo9Uye5yFbldApu3PbZt22NUeYlcxXuyzXUx+SLRRe5698VEKHWxe/JFvT8mkfXui6qy6XKZ7+NDtE0exjf5i8lVl7Z/ugG2/oaD5Qa0/dUtuT/kN796zSrDyoXxhoY4frj8wlVX4Zy6969/1ffLGwEmOGrx+n/5XuKa0yOVFpOmFAur1568eol25/CSh5doKl5L1N9FtEuKI29ScV5k22MvP7aNZKYdMFCTmXjS6ksk4+2kCCmqv/uH+JGauV1700m85pucNb2V6NjeAQY1aq4aamC74/GDzzbgTR3qvo4OdHTHsR0qD55/dKgfAooUZ5j/D9nCYecAAHicY2BkYGAA4kkhh/Pj+W2+MnCzMIDA4zOpIQj6/wIWBuYEIJeDgQkkCgA2tQqpAHicY2BkYGBu+N/AEMNSzwAELAwMjAyoYCYAVAYDgQAAeJxjYWBgYHEAYgYyMEyfPQE1DkhqHbCI48LYzEAXR1djj8MOGzQ1NgTcR8iNxkT6wZ4INdjcAcIGOPyFK3zR48EeT9wgm1WPxW57HGrR3cvAgOlPNHUA2pwZdQAAAAAAAG4BYAHEAiwClgMsA6oD7ARwBOAFOgWMBf4GFgY8BmwGtgbcB0IHxggMCDQImgkaCZoKWgsqC7wMQgzCDRwNUg2oDdgOOg6aDtAPHBAeEWIRlhHKEiQScBMgE4wUWhTqFToVohYAFlQWvBceF2gYCBgiGHYYyhlyGgIaLBqgGw4blBvoHDIcXBzCHUQdnh5CHqQfHh9OH3wfrCAOICQg6CEuIfgjFCNYI5wkJiSGJPAlMCVUJbImBiZoJqwnDieMKBIowikmKW4pxCnsKkQq6CtkLAosZC0ULZAuSC6eLuwvQi/EMFQwpjD0MUYxvjJmMtYzyjQ+NIg1HDWANcg2ajbmN7A4Bjj0OVY5rDpkO0g7pDv8PGo8xj2uPio+nD8qP8xAJECgQMhBCkE0QZJBzHicY2BkYGCYyfCBQYoBBJiAmAsIGRj+g/kMADIzAroAeJxdkc1OwkAUhc/IjxESF5q4cDVuiImhBZbsDAnsWeC6lCk/aTvNdCBh5dP4BD6BT+AT6Ev4Ah7K1QTazJ3vnLn3zk0L4AbfUDg+t1xHVrikOvIF+V64Rn4QrpMfhRtoIxRu0h8Kt/CEZ+E2O6bsoOpXVB28Citc4034gvwuXCN/CNfJn8IN3OFLuEn/R7iFmVLCbXTUy8iZyJuFnu/1OrZ5YnOfxN0DTs1ym0ZOlGwz48q1zXU/6IkzMblxfy3K3XLgfaITZzM9Zi+TplYXzm5M7IOV98UwDBPxg9hmGMHBIIJnXEBjjj3jGjEsciRV9NxjdP/dKXOX2PJDRaw+PTtVM2Y6lNQHpdFHgN5ZzoQ5eZV3PkWJHe8Z0D1MoLkcKzLSWOYynCElaxTV2YZOTD/Aqqoq+JNDvslZflDdnf0CPF1o6QAAAHicbVOHcuM2EPWzSIqSbF98vvTeK5OcfOm99957IGIlwQYJGgAlK7333i9fmwUlJfZMNCOCAJbA21dWVlfmv+7K//9OYhUtRIiRoI0UHXTRwxrWsYEjOAWbOIotHMOpOA2n4wycibNwNs7BuTgP5+MCXIiLcDEuwaW4DJfjClyJq3A1rkGGa3Edrsdx9LGNE7gBN+Im3IxbcCtuw+24A3fiLtyNe3Av7sP9eAAP4iE8jEfwKB7D43gCT+IpPI1n8Cyew/N4AS/iJbyMV/AqXsPreANv4i28DYEBckgQhhhhDIUd7EKjQAmDCnuwcPCoMcEU+5jhHbyL9/A+PsCH+Agf4xN8is/wOb7Al/gKX+MbfIvv8D1+wI/4CT/jF/yK3/A7/sCf+Asn8fdKqk0uvDJlsmdzIylVZVX7TMlEDa0oKBa1VKY7NLbICt7XsVdeU+TUqGwLKS0513Gej3Be5e2BaE6JJ0qSScREeGHbrnYVlbI9FbZU5ShyPEktDfnbcX9VqHZudF2U24vxxGLsL8bjiRYzU/uOo1FBpSfZDgNj7szBejFqeTFYy005VKOMJrzLmKjKAuzEaQZjE6mENqOtnZqBDmeZq0ROwpq6lEldaSNklJtq1vWqoMyKckSpF2434x6PHfpmQH5KVCaqqIz1Pb5YU3PRdm8BYCAcHRWaGVKeCpc5b8nn44NLQ037zEKcjynfjUTtTZoLzSvCLttwfqbpyGIi5ESUOcnlZmVN5TZYDvKU5crmmvrrh6bJfHZMKkt5ICuzZmqZG+toTSpXMakNjEhaMVpfrgzYD7uRZN1iksr3o/AMQmcsxtaySpValdTULhFOuDv+jCJppuXG4cIWzajD/yzXxlG7YhiKpr1we1YyKlElQ6NZpF7js3kDnWY7bEZhtdNsBfYixVfGqhAj2lwqE2rZhdZvLFfy4BR75F/p5hrEDGe3HxeqrF3MLqr7UWEsReE11ax6zae2KiGTwgyUprTieVaIfS6bLGeqTLmFLDhstcqjsNhe2DmxQqra9ZyYsCmU1iTTvZpckzCXW6N1GFTl2468b9LApYkbC+YtnQirgp9aM3Jr8zWmkCuDItQNj4U3WQ0tKsdfThX3xRkbBIJ63CYjY+lzH7MkNQdYk/VRLqxMG7cNzD57zeUihGJQe2/KcLDIGifHI45E1ZYqJNhGgdc1SXPI3IOLxr7QSTCFsXETvyhQysw4NzVWxoEA02KvJXPtI8ugY4ZrbDLHFe04JqOsiwHZuIlU978QHchTPwpZjLiiTD3ts7ok2t4Y7VUVOW186pkLNyTL6R91pnyPLYTdjQIHcXNOpzHuWGkZ8Vu/xY+tAykMQQ0Yu01Z47rNA9tzC62s/APehQ0CAA==) format("woff")}.fc-icon{-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale;font-family:fc-icon!important;font-size:16px;font-style:normal}.icon-location:before{content:"\\e6d4"}.icon-qrcode:before{content:"\\e6ce"}.icon-input-id:before{content:"\\e6d1"}.icon-iframe:before{content:"\\e6d2"}.icon-audio:before{content:"\\e6d3"}.icon-form-model:before{content:"\\e6d5"}.icon-title:before{content:"\\e6d6"}.icon-sign:before{content:"\\e6d7"}.icon-address:before{content:"\\e6d8"}.icon-statistic:before{content:"\\e6d9"}.icon-barcode:before{content:"\\e6da"}.icon-video:before{content:"\\e6db"}.icon-avatar:before{content:"\\e6dc"}.icon-suspend:before{content:"\\e6cf"}.icon-warning:before{content:"\\e6d0"}.icon-send:before{content:"\\e6cc"}.icon-refresh2:before{content:"\\e6cd"}.icon-ai:before{content:"\\e6cb"}.icon-ai.bright{-webkit-text-fill-color:transparent;background:linear-gradient(90deg,#328ff7,#62e3a3);-webkit-background-clip:text}.icon-column4:before{content:"\\e6c7"}.icon-column3:before{content:"\\e6c6"}.icon-column2:before{content:"\\e6c8"}.icon-column1:before{content:"\\e6c9"}.icon-layout:before{content:"\\e6ca"}.icon-segmented:before{content:"\\e682"}.icon-mention:before{content:"\\e6c5"}.icon-input-tag:before{content:"\\e6c4"}.icon-up:before{content:"\\e697";display:inline-block;transform:rotate(180deg)}.icon-alignitems-flexstart:before{content:"\\e67f";display:inline-block;transform:rotate(180deg)}.icon-align-center:before{content:"\\e6a5";display:inline-block;transform:rotate(90deg)}.icon-align-flexstart:before{content:"\\e6a4";display:inline-block;transform:rotate(90deg)}.icon-align-spacearound:before{content:"\\e670";display:inline-block;transform:rotate(-90deg)}.icon-align-spacebetween:before{content:"\\e695";display:inline-block;transform:rotate(-90deg)}.icon-align-stretch:before{content:"\\e6a7";display:inline-block;transform:rotate(-90deg)}.icon-align-flexend:before{content:"\\e6a4";display:inline-block;transform:rotate(-90deg)}.icon-justify-flexend:before{content:"\\e6a4";display:inline-block;transform:rotate(180deg)}.icon-direction-row:before{content:"\\e68b";display:inline-block;transform:rotate(180deg)}.icon-direction-column:before{content:"\\e68b";display:inline-block;transform:rotate(-90deg)}.icon-direction-columnreverse:before{content:"\\e68b";display:inline-block;transform:rotate(90deg)}.icon-arrow:before{content:"\\e697";display:inline-block;transform:rotate(180deg)}.icon-cell:before{content:"\\e654"}.icon-table:before{content:"\\eb0a"}.icon-next-step:before{content:"\\e6b4";display:inline-block;transform:rotateY(180deg)}.icon-grid:before{content:"\\e65c";display:inline-block;transform:rotate(90deg)}.icon-alignitems-stretch:before{content:"\\e67e"}.icon-alignitems-flexend:before{content:"\\e67f"}.icon-check:before{content:"\\e680"}.icon-auto:before{content:"\\e681"}.icon-config-event:before{content:"\\e66e"}.icon-calendar:before{content:"\\e683"}.icon-config-style:before{content:"\\e684"}.icon-copy:before{content:"\\e676"}.icon-config-advanced:before{content:"\\e686"}.icon-config-props:before{content:"\\e687"}.icon-delete-circle2:before{content:"\\e688"}.icon-delete-circle:before,.icon-delete2:before{content:"\\e689"}.icon-delete:before{content:"\\e68a"}.icon-direction-rowreverse:before{content:"\\e68b"}.icon-display-flex:before{content:"\\e68c"}.icon-dialog:before{content:"\\e66f"}.icon-drag:before{content:"\\e68e"}.icon-display-block:before{content:"\\e68f"}.icon-data:before{content:"\\e690"}.icon-edit2:before{content:"\\e691"}.icon-edit:before{content:"\\e692"}.icon-add-col:before{content:"\\e693"}.icon-display-inlineblock:before{content:"\\e694"}.icon-config-base:before{content:"\\e6bf"}.icon-config-validate:before{content:"\\e696"}.icon-down:before{content:"\\e697"}.icon-display-inline:before{content:"\\e698"}.icon-eye:before{content:"\\e699"}.icon-eye-close:before{content:"\\e69a"}.icon-import:before{content:"\\e6a6"}.icon-preview:before{content:"\\e69b"}.icon-flex-nowrap:before{content:"\\e69c"}.icon-folder:before{content:"\\e69d"}.icon-form-circle:before{content:"\\e69e"}.icon-flex-wrap:before{content:"\\e69f"}.icon-form:before{content:"\\e6a0"}.icon-form-item:before{content:"\\e6a1"}.icon-icon:before{content:"\\e6a2"}.icon-image:before{content:"\\e6a3"}.icon-justify-flexstart:before{content:"\\e6a4"}.icon-justify-center:before{content:"\\e6a5"}.icon-justify-spacearound:before{content:"\\e670"}.icon-justify-stretch:before{content:"\\e6a7"}.icon-link2:before{content:"\\e6a8"}.icon-justify-spacebetween:before{content:"\\e695"}.icon-minus:before{content:"\\e6aa"}.icon-menu2:before{content:"\\e6ab"}.icon-more:before{content:"\\e6ac"}.icon-menu:before{content:"\\e6ad"}.icon-language:before{content:"\\e6ae"}.icon-pad:before{content:"\\e6af"}.icon-mobile:before{content:"\\e6b0"}.icon-page-max:before{content:"\\e6b1"}.icon-move:before{content:"\\e6b2"}.icon-page-min:before{content:"\\e6b3"}.icon-pre-step:before{content:"\\e6b4"}.icon-pc:before{content:"\\e6b5"}.icon-page:before{content:"\\e6b6"}.icon-refresh:before{content:"\\e6b7"}.icon-radius:before{content:"\\e6b8"}.icon-save-filled:before{content:"\\e6b9"}.icon-question:before{content:"\\e6ba"}.icon-scroll:before{content:"\\e6bb"}.icon-script:before{content:"\\e6bc"}.icon-setting:before{content:"\\e6bd"}.icon-save-online:before,.icon-save:before{content:"\\e6be"}.icon-task-add:before{content:"\\e68d"}.icon-shadow:before{content:"\\e6c0"}.icon-variable:before{content:"\\e6c1"}.icon-yes:before{content:"\\e6c2"}.icon-shadow-inset:before{content:"\\e6c3"}.icon-date:before{content:"\\e642"}.icon-date-range:before{content:"\\e643"}.icon-collapse:before{content:"\\e644"}.icon-slider:before{content:"\\e665"}.icon-switch:before{content:"\\e646"}.icon-subform:before{content:"\\e647"}.icon-time-range:before{content:"\\e685"}.icon-tree-select:before{content:"\\e649"}.icon-value:before{content:"\\e64a"}.icon-table-form3:before{content:"\\e6a9"}.icon-alert:before{content:"\\e64c"}.icon-card:before{content:"\\e64d"}.icon-checkbox:before{content:"\\e64e"}.icon-cascader:before{content:"\\e64f"}.icon-button:before{content:"\\e650"}.icon-data-table:before{content:"\\e651"}.icon-group:before{content:"\\e652"}.icon-divider:before{content:"\\e653"}.icon-flex:before{content:"\\e654"}.icon-descriptions:before{content:"\\e655"}.icon-html:before{content:"\\e656"}.icon-editor:before{content:"\\e657"}.icon-input:before{content:"\\e658"}.icon-link:before{content:"\\e659"}.icon-password:before{content:"\\e65a"}.icon-radio:before{content:"\\e65b"}.icon-row:before{content:"\\e65c"}.icon-inline:before{content:"\\e65d"}.icon-rate:before{content:"\\e65e"}.icon-color:before{content:"\\e65f"}.icon-select:before{content:"\\e660"}.icon-json:before{content:"\\e661"}.icon-number:before{content:"\\e662"}.icon-space:before{content:"\\e664"}.icon-step-form:before{content:"\\e663"}.icon-table-form:before{content:"\\e666"}.icon-table-form2:before{content:"\\e667"}.icon-time:before{content:"\\e668"}.icon-span:before{content:"\\e669"}.icon-textarea:before{content:"\\e66a"}.icon-tooltip:before{content:"\\e66b"}.icon-slot:before{content:"\\e66c"}.icon-transfer:before{content:"\\e66d"}.icon-upload:before{content:"\\e673"}.icon-tag:before{content:"\\e671"}.icon-watermark:before{content:"\\e672"}.icon-tab:before{content:"\\e674"}.icon-tree:before{content:"\\e675"}.icon-table:before{content:"\\e677"}.icon-add-child:before{content:"\\e678"}.icon-add2:before{content:"\\e679"}.icon-add:before{content:"\\e67a"}.icon-alignitems-baseline:before{content:"\\e67b"}.icon-add-circle:before{content:"\\e67c"}.icon-alignitems-center:before{content:"\\e67d"}')),document.head.appendChild(e)}}catch(t){console.error("vite-plugin-css-injected-by-js",t)}})();
(function(Ue,l){typeof exports=="object"&&typeof module<"u"?l(exports,require("vue"),require("vant"),require("element-plus")):typeof define=="function"&&define.amd?define(["exports","vue","vant","element-plus"],l):(Ue=typeof globalThis<"u"?globalThis:Ue||self,l(Ue.formCreateMobile={},Ue.Vue,Ue.vant,Ue.ElementPlus))})(this,function(Ue,l,dn,Sr){"use strict";/*!
 * @form-create/vant v3.2.18
 * (c) 2018-2025 xaboy
 * Github https://github.com/xaboy/form-create
 * Released under the MIT License.
 */var co="fcSubForm",fo=l.defineComponent({name:co,props:{rule:Array,options:{type:Object,default:function(){return l.reactive({submitBtn:!1,resetBtn:!1})}},modelValue:{type:Object,default:function(){return{}}},disabled:{type:Boolean,default:!1},syncDisabled:{type:Boolean,default:!0},formCreateInject:Object},data:function(){return{cacheValue:{},subApi:{},form:l.markRaw(this.formCreateInject.form.$form())}},emits:["fc:subform","update:modelValue","change","itemMounted"],watch:{modelValue:function(e){this.setValue(e)}},methods:{formData:function(e){this.cacheValue=JSON.stringify(e),this.$emit("update:modelValue",e),this.$emit("change",e)},setValue:function(e){var n=JSON.stringify(e);this.cacheValue!==n&&(this.cacheValue=n,this.subApi.coverValue(e||{}))},add$f:function(e){var n=this;this.subApi=e,l.nextTick(function(){n.$emit("itemMounted",e)})}},render:function(){var e=this.form;return l.createVNode(e,{disabled:this.disabled,"onUpdate:modelValue":this.formData,modelValue:this.modelValue,"onEmit-event":this.$emit,"onUpdate:api":this.add$f,rule:this.rule,option:this.options,extendOption:!0},null)}});function Er(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function G(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?Er(Object(n),!0).forEach(function(r){Se(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):Er(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function pe(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?pe=function(e){return typeof e}:pe=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},pe(t)}function ho(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Se(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function po(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&hn(t,e)}function Mt(t){return Mt=Object.setPrototypeOf?Object.getPrototypeOf:function(n){return n.__proto__||Object.getPrototypeOf(n)},Mt(t)}function hn(t,e){return hn=Object.setPrototypeOf||function(r,i){return r.__proto__=i,r},hn(t,e)}function mo(){if(typeof Reflect>"u"||!Reflect.construct||Reflect.construct.sham)return!1;if(typeof Proxy=="function")return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],function(){})),!0}catch{return!1}}function go(t){if(t===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function vo(t,e){if(e&&(typeof e=="object"||typeof e=="function"))return e;if(e!==void 0)throw new TypeError("Derived constructors may only return object or undefined");return go(t)}function yo(t){var e=mo();return function(){var r=Mt(t),i;if(e){var a=Mt(this).constructor;i=Reflect.construct(r,arguments,a)}else i=r.apply(this,arguments);return vo(this,i)}}function oe(t){return _o(t)||bo(t)||wo(t)||$o()}function _o(t){if(Array.isArray(t))return pn(t)}function bo(t){if(typeof Symbol<"u"&&t[Symbol.iterator]!=null||t["@@iterator"]!=null)return Array.from(t)}function wo(t,e){if(!!t){if(typeof t=="string")return pn(t,e);var n=Object.prototype.toString.call(t).slice(8,-1);if(n==="Object"&&t.constructor&&(n=t.constructor.name),n==="Map"||n==="Set")return Array.from(t);if(n==="Arguments"||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n))return pn(t,e)}}function pn(t,e){(e==null||e>t.length)&&(e=t.length);for(var n=0,r=new Array(e);n<e;n++)r[n]=t[n];return r}function $o(){throw new TypeError(`Invalid attempt to spread non-iterable instance.
In order to be iterable, non-array objects must have a [Symbol.iterator]() method.`)}var j={type:function(e,n){return Object.prototype.toString.call(e)==="[object "+n+"]"},Undef:function(e){return e==null},Element:function(e){return pe(e)==="object"&&e!==null&&e.nodeType===1&&!j.Object(e)},trueArray:function(e){return Array.isArray(e)&&e.length>0},Function:function(e){var n=this.getType(e);return n==="Function"||n==="AsyncFunction"},getType:function(e){var n=Object.prototype.toString.call(e);return/^\[object (.*)\]$/.exec(n)[1]},empty:function(e){return e==null||Array.isArray(e)&&Array.isArray(e)&&!e.length?!0:typeof e=="string"&&!e}};["Date","Object","String","Boolean","Array","Number"].forEach(function(t){j[t]=function(e){return j.type(e,t)}});function me(t,e){return{}.hasOwnProperty.call(t,e)}var Oo=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},Cr={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(Oo,function(){var n=1e3,r=6e4,i=36e5,a="millisecond",o="second",s="minute",u="hour",d="day",h="week",_="month",y="quarter",x="year",S="date",C="Invalid Date",F=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,q=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a|A|m{1,2}|s{1,2}|Z{1,2}|SSS/g,U={name:"en",weekdays:"Sunday_Monday_Tuesday_Wednesday_Thursday_Friday_Saturday".split("_"),months:"January_February_March_April_May_June_July_August_September_October_November_December".split("_"),ordinal:function(O){var b=["th","st","nd","rd"],f=O%100;return"["+O+(b[(f-20)%10]||b[f]||b[0])+"]"}},L=function(O,b,f){var c=String(O);return!c||c.length>=b?O:""+Array(b+1-c.length).join(f)+O},Y={s:L,z:function(O){var b=-O.utcOffset(),f=Math.abs(b),c=Math.floor(f/60),m=f%60;return(b<=0?"+":"-")+L(c,2,"0")+":"+L(m,2,"0")},m:function k(O,b){if(O.date()<b.date())return-k(b,O);var f=12*(b.year()-O.year())+(b.month()-O.month()),c=O.clone().add(f,_),m=b-c<0,v=O.clone().add(f+(m?-1:1),_);return+(-(f+(b-c)/(m?c-v:v-c))||0)},a:function(O){return O<0?Math.ceil(O)||0:Math.floor(O)},p:function(O){return{M:_,y:x,w:h,d,D:S,h:u,m:s,s:o,ms:a,Q:y}[O]||String(O||"").toLowerCase().replace(/s$/,"")},u:function(O){return O===void 0}},ne="en",Z={};Z[ne]=U;var K="$isDayjsObject",X=function(O){return O instanceof $e||!(!O||!O[K])},ce=function k(O,b,f){var c;if(!O)return ne;if(typeof O=="string"){var m=O.toLowerCase();Z[m]&&(c=m),b&&(Z[m]=b,c=m);var v=O.split("-");if(!c&&v.length>1)return k(v[0])}else{var p=O.name;Z[p]=O,c=p}return!f&&c&&(ne=c),c||!f&&ne},se=function(O,b){if(X(O))return O.clone();var f=pe(b)=="object"?b:{};return f.date=O,f.args=arguments,new $e(f)},re=Y;re.l=ce,re.i=X,re.w=function(k,O){return se(k,{locale:O.$L,utc:O.$u,x:O.$x,$offset:O.$offset})};var $e=function(){function k(b){this.$L=ce(b.locale,null,!0),this.parse(b),this.$x=this.$x||b.x||{},this[K]=!0}var O=k.prototype;return O.parse=function(b){this.$d=function(f){var c=f.date,m=f.utc;if(c===null)return new Date(NaN);if(re.u(c))return new Date;if(c instanceof Date)return new Date(c);if(typeof c=="string"&&!/Z$/i.test(c)){var v=c.match(F);if(v){var p=v[2]-1||0,E=(v[7]||"0").substring(0,3);return m?new Date(Date.UTC(v[1],p,v[3]||1,v[4]||0,v[5]||0,v[6]||0,E)):new Date(v[1],p,v[3]||1,v[4]||0,v[5]||0,v[6]||0,E)}}return new Date(c)}(b),this.init()},O.init=function(){var b=this.$d;this.$y=b.getFullYear(),this.$M=b.getMonth(),this.$D=b.getDate(),this.$W=b.getDay(),this.$H=b.getHours(),this.$m=b.getMinutes(),this.$s=b.getSeconds(),this.$ms=b.getMilliseconds()},O.$utils=function(){return re},O.isValid=function(){return this.$d.toString()!==C},O.isSame=function(b,f){var c=se(b);return this.startOf(f)<=c&&c<=this.endOf(f)},O.isAfter=function(b,f){return se(b)<this.startOf(f)},O.isBefore=function(b,f){return this.endOf(f)<se(b)},O.$g=function(b,f,c){return re.u(b)?this[f]:this.set(c,b)},O.unix=function(){return Math.floor(this.valueOf()/1e3)},O.valueOf=function(){return this.$d.getTime()},O.startOf=function(b,f){var c=this,m=!!re.u(f)||f,v=re.p(b),p=function(A,R){var g=re.w(c.$u?Date.UTC(c.$y,R,A):new Date(c.$y,R,A),c);return m?g:g.endOf(d)},E=function(A,R){return re.w(c.toDate()[A].apply(c.toDate("s"),(m?[0,0,0,0]:[23,59,59,999]).slice(R)),c)},T=this.$W,M=this.$M,z=this.$D,I="set"+(this.$u?"UTC":"");switch(v){case x:return m?p(1,0):p(31,11);case _:return m?p(1,M):p(0,M+1);case h:var B=this.$locale().weekStart||0,N=(T<B?T+7:T)-B;return p(m?z-N:z+(6-N),M);case d:case S:return E(I+"Hours",0);case u:return E(I+"Minutes",1);case s:return E(I+"Seconds",2);case o:return E(I+"Milliseconds",3);default:return this.clone()}},O.endOf=function(b){return this.startOf(b,!1)},O.$set=function(b,f){var c,m=re.p(b),v="set"+(this.$u?"UTC":""),p=(c={},c[d]=v+"Date",c[S]=v+"Date",c[_]=v+"Month",c[x]=v+"FullYear",c[u]=v+"Hours",c[s]=v+"Minutes",c[o]=v+"Seconds",c[a]=v+"Milliseconds",c)[m],E=m===d?this.$D+(f-this.$W):f;if(m===_||m===x){var T=this.clone().set(S,1);T.$d[p](E),T.init(),this.$d=T.set(S,Math.min(this.$D,T.daysInMonth())).$d}else p&&this.$d[p](E);return this.init(),this},O.set=function(b,f){return this.clone().$set(b,f)},O.get=function(b){return this[re.p(b)]()},O.add=function(b,f){var c,m=this;b=Number(b);var v=re.p(f),p=function(z){var I=se(m);return re.w(I.date(I.date()+Math.round(z*b)),m)};if(v===_)return this.set(_,this.$M+b);if(v===x)return this.set(x,this.$y+b);if(v===d)return p(1);if(v===h)return p(7);var E=(c={},c[s]=r,c[u]=i,c[o]=n,c)[v]||1,T=this.$d.getTime()+b*E;return re.w(T,this)},O.subtract=function(b,f){return this.add(-1*b,f)},O.format=function(b){var f=this,c=this.$locale();if(!this.isValid())return c.invalidDate||C;var m=b||"YYYY-MM-DDTHH:mm:ssZ",v=re.z(this),p=this.$H,E=this.$m,T=this.$M,M=c.weekdays,z=c.months,I=c.meridiem,B=function(R,g,$,w){return R&&(R[g]||R(f,m))||$[g].slice(0,w)},N=function(R){return re.s(p%12||12,R,"0")},D=I||function(A,R,g){var $=A<12?"AM":"PM";return g?$.toLowerCase():$};return m.replace(q,function(A,R){return R||function(g){switch(g){case"YY":return String(f.$y).slice(-2);case"YYYY":return re.s(f.$y,4,"0");case"M":return T+1;case"MM":return re.s(T+1,2,"0");case"MMM":return B(c.monthsShort,T,z,3);case"MMMM":return B(z,T);case"D":return f.$D;case"DD":return re.s(f.$D,2,"0");case"d":return String(f.$W);case"dd":return B(c.weekdaysMin,f.$W,M,2);case"ddd":return B(c.weekdaysShort,f.$W,M,3);case"dddd":return M[f.$W];case"H":return String(p);case"HH":return re.s(p,2,"0");case"h":return N(1);case"hh":return N(2);case"a":return D(p,E,!0);case"A":return D(p,E,!1);case"m":return String(E);case"mm":return re.s(E,2,"0");case"s":return String(f.$s);case"ss":return re.s(f.$s,2,"0");case"SSS":return re.s(f.$ms,3,"0");case"Z":return v}return null}(A)||v.replace(":","")})},O.utcOffset=function(){return 15*-Math.round(this.$d.getTimezoneOffset()/15)},O.diff=function(b,f,c){var m,v=this,p=re.p(f),E=se(b),T=(E.utcOffset()-this.utcOffset())*r,M=this-E,z=function(){return re.m(v,E)};switch(p){case x:m=z()/12;break;case _:m=z();break;case y:m=z()/3;break;case h:m=(M-T)/6048e5;break;case d:m=(M-T)/864e5;break;case u:m=M/i;break;case s:m=M/r;break;case o:m=M/n;break;default:m=M}return c?m:re.a(m)},O.daysInMonth=function(){return this.endOf(_).$D},O.$locale=function(){return Z[this.$L]},O.locale=function(b,f){if(!b)return this.$L;var c=this.clone(),m=ce(b,f,!0);return m&&(c.$L=m),c},O.clone=function(){return re.w(this.$d,this)},O.toDate=function(){return new Date(this.valueOf())},O.toJSON=function(){return this.isValid()?this.toISOString():null},O.toISOString=function(){return this.$d.toISOString()},O.toString=function(){return this.$d.toUTCString()},k}(),Ne=$e.prototype;return se.prototype=Ne,[["$ms",a],["$s",o],["$m",s],["$H",u],["$W",d],["$M",_],["$y",x],["$D",S]].forEach(function(k){Ne[k[1]]=function(O){return this.$g(O,k[0],k[1])}}),se.extend=function(k,O){return k.$i||(k(O,$e,se),k.$i=!0),se},se.locale=ce,se.isDayjs=X,se.unix=function(k){return se(1e3*k)},se.en=Z[ne],se.Ls=Z,se.p={},se})})(Cr);var ct=Cr.exports,So="fcCalendar",Eo=l.defineComponent({name:So,inheritAttrs:!1,props:{placeholder:String,formCreateInject:Object,disabled:Boolean,clearable:Boolean,type:String,modelValue:[String,Array],minDate:[String,Date],maxDate:[String,Date]},emits:["update:modelValue","change","fc.el"],setup:function(e,n){var r=l.ref(!1),i=l.toRef(e,"modelValue"),a=l.toRef(e,"formCreateInject"),o=l.ref(i.value);l.watch(function(){return i.value},function(S){o.value=S});var s=function(C){return ct(C).format("YYYY-MM-DD")},u=function(C){return ct(C).toDate()},d=l.computed(function(){var S=i.value;return Array.isArray(S)?S.map(u):S?u(S):null}),h=l.computed(function(){return{minDate:e.minDate?ct(e.minDate).toDate():void 0,maxDate:e.maxDate?ct(e.maxDate).toDate():void 0}}),_=function(C){Array.isArray(C)?o.value=C.map(s):C?o.value=s(C):o.value=C},y=function(){n.emit("update:modelValue",o.value),n.emit("change",o.value)},x={range:function(){return o.value.length?o.value.join(" - "):""},multiple:function(){return o.value.length?a.value.t("selectedData",{length:o.value.length})||"\u9009\u62E9\u4E86 ".concat(o.value.length," \u4E2A\u65E5\u671F"):""}};return{show:r,inputValue:o,defaultDate:d,dateRange:h,open:function(){e.disabled||(r.value=!0)},confirm:function(C){_(C),r.value=!1,y()},getStrValue:function(){return o.value?me(x,e.type)?x[e.type]():o.value||"":""},clear:function(C){C.stopPropagation();var F=Array.isArray(o.value)?[]:"";_(F),y()}}},render:function(){var e=this,n=function(){return e.$props.clearable&&!j.empty(e.inputValue)?l.createVNode("i",{class:"van-badge__wrapper van-icon van-icon-clear van-field__clear",onClick:e.clear},null):void 0};return l.createVNode(l.Fragment,null,[l.createVNode(l.resolveComponent("van-field"),{ref:"el",placeholder:this.placeholder,readonly:!0,disabled:this.$props.disabled,onClick:this.open,"model-value":this.getStrValue(),isLink:!0,border:!1},{"right-icon":n}),l.createVNode(l.resolveComponent("van-calendar"),l.mergeProps(G(G({},this.$attrs),this.dateRange),{show:this.show,"onUpdate:show":function(i){return e.show=i},type:this.type,onConfirm:this.confirm,defaultDate:this.defaultDate}),null)])},mounted:function(){this.$emit("fc.el",this.$refs.el)}}),Co="fcCascader",Ao=l.defineComponent({name:Co,inheritAttrs:!1,props:{placeholder:String,disabled:Boolean,clearable:Boolean,fieldNames:Object,modelValue:[String,Number],options:Array,minDate:[String,Date],maxDate:[String,Date]},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=l.ref(!1),i=l.toRef(e,"modelValue"),a=l.toRef(e,"options"),o=l.toRef(e,"fieldNames",{}),s=function _(y,x,S){for(var C=0;C<y.length;C++){if(y[C][o.value.value||"value"]===x)return[].concat(oe(S),[y[C]]);if(y[C][o.value.children||"children"]){var F=_(y[C][o.value.children||"children"],x,[].concat(oe(S),[y[C]]));if(F)return F}}},u=function(y){if(y==null||y==="")return"";var x=s(a.value,y,[]);return x?x.map(function(S){return S[o.value.text||"text"]}).join(" / "):y},d=l.ref(u(i.value));l.watch(function(){return i.value},function(_){d.value=u(_)}),l.watch(function(){return a.value},function(){i.value!=null&&i.value!==""&&(d.value=u(i.value))},{deep:!0});var h=function(y){n.emit("update:modelValue",y)};return{show:r,inputValue:d,options:a,open:function(){e.disabled||(r.value=!0)},confirm:function(y){var x=y.selectedOptions,S=y.value;d.value=x.map(function(C){return C[o.value.text||"text"]}).join(" / "),r.value=!1,h(S)},clear:function(y){y.stopPropagation(),d.value="",h("")}}},render:function(){var e=this,n=function(){return e.$props.clearable&&e.inputValue?l.createVNode("i",{class:"van-badge__wrapper van-icon van-icon-clear van-field__clear",onClick:e.clear},null):void 0};return l.createVNode(l.Fragment,null,[l.createVNode(l.resolveComponent("van-field"),{ref:"el",placeholder:this.placeholder,readonly:!0,disabled:this.$props.disabled,onClick:this.open,"model-value":this.inputValue,border:!1,isLink:!0},{"right-icon":n}),l.createVNode(l.resolveComponent("van-popup"),{show:this.show,"onUpdate:show":function(i){return e.show=i},round:!0,position:"bottom"},{default:function(){return[l.createVNode(l.resolveComponent("van-cascader"),l.mergeProps(e.$attrs,{modelValue:e.modelValue,fieldNames:e.fieldNames,options:e.options,onClose:function(){return e.show=!1},onFinish:e.confirm}),null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.el)}});function xo(t){return typeof t=="function"||Object.prototype.toString.call(t)==="[object Object]"&&!l.isVNode(t)}var Do="fcCheckbox",Po=l.defineComponent({name:Do,inheritAttrs:!1,props:{modelValue:Array,options:Array},emits:["update:modelValue"],setup:function(e,n){var r=l.toRef(e,"modelValue",[]),i=l.toRef(e,"options");return{options:i,modelValue:r,onInput:function(o){n.emit("update:modelValue",o)}}},render:function(){var e;return l.createVNode(l.resolveComponent("van-checkbox-group"),l.mergeProps({direction:"horizontal"},this.$attrs,{modelValue:Array.isArray(this.modelValue)?this.modelValue:[],"onUpdate:modelValue":this.onInput}),xo(e=(this.options||[]).map(function(n){var r=G({},n),i=n.text,a=n.value;return delete r.text,delete r.value,l.createVNode(l.resolveComponent("van-checkbox"),l.mergeProps({name:a,shape:"square"},r),{default:function(){return[i||n.label||a]}})}))?e:{default:function(){return[e]}})}}),Ro="fcSelect",ko=l.defineComponent({name:Ro,inheritAttrs:!1,props:{disabled:Boolean,placeholder:String,columnsFieldNames:Object,modelValue:[String,Number],options:Array},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=l.ref(!1),i=l.toRef(e,"modelValue"),a=l.toRef(e,"options"),o=l.toRef(e,"columnsFieldNames",{}),s=l.computed(function(){if(i.value==null||i.value==="")return"";for(var d=0;d<(a.value||[]).length;d++)if(a.value[d][o.value.value||"value"]===i.value)return a.value[d][o.value.text||"text"];return i.value}),u=function(h){n.emit("update:modelValue",h)};return{show:r,inputValue:s,options:a,open:function(){e.disabled||(r.value=!0)},confirm:function(h){var _=h.selectedValues;u(_[0]),r.value=!1}}},render:function(){var e=this;return l.createVNode(l.Fragment,null,[l.createVNode(l.resolveComponent("van-field"),{ref:"el",placeholder:this.placeholder,readonly:!0,disabled:this.$props.disabled,onClick:this.open,"model-value":this.inputValue,isLink:!0},null),l.createVNode(l.resolveComponent("van-popup"),{show:this.show,"onUpdate:show":function(r){return e.show=r},round:!0,position:"bottom"},{default:function(){return[l.createVNode(l.resolveComponent("van-picker"),l.mergeProps(e.$attrs,{modelValue:[e.modelValue],columnsFieldNames:e.columnsFieldNames,columns:e.options,onCancel:function(){return e.show=!1},onConfirm:e.confirm}),null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.el)}});function Ze(t){return Array.isArray(t)?t:[null,void 0,""].indexOf(t)>-1?[]:[t]}var Vo="fcUploader";function Ar(t,e){return pe(t)==="object"?t:{url:t,is_string:!0,name:Mo(t),uid:e}}function xr(t){return G(G({},t),{},{file:t,value:t})}function Mo(t){return(""+t).split("/").pop()}var To=l.defineComponent({name:Vo,inheritAttrs:!1,props:{formCreateInject:Object,modelValue:[Array,String,Object],afterRead:Function,action:String,headers:Object,method:String,data:Object,uploadName:String,onSuccess:Function,onError:Function,maxCount:Number},emits:["update:modelValue","delete"],setup:function(e,n){var r=l.toRef(e,"afterRead"),i=l.toRef(e,"modelValue",[]),a=l.ref(Ze(i.value).map(Ar).map(xr));l.watch(function(){return i.value},function(s){a.value=Ze(s).map(Ar).map(xr)});var o=function(){var u=a.value.map(function(d){return d.is_string?d.url:d.value||d.url}).filter(function(d){return d!==void 0});n.emit("update:modelValue",e.maxCount===1?u[0]||"":u)};return{fileList:a,modelValue:i,onDelete:function(u){o(),n.emit("delete",u)},uploadFile:function(u){var d=this;if(u.status="uploading",r.value)return r.value(u);var h=e.data||{};h[e.uploadName||"file"]=u.file,e.formCreateInject.api.fetch({action:e.action,dataType:"formData",source:"upload",headers:e.headers||{},method:e.method||"post",data:h}).then(function(_){u.status="success",e.onSuccess&&e.onSuccess(_,u),o()}).catch(function(_){u.status="failed",u.message=d.formCreateInject.t("uploadFail")||"\u4E0A\u4F20\u5931\u8D25",e.onError&&e.onError(_,u)})}}},render:function(){var e=this;return l.createVNode(l.resolveComponent("van-uploader"),l.mergeProps(this.$attrs,{"model-value":this.fileList,"onUpdate:model-value":function(r){return e.fileList=r},afterRead:this.uploadFile,onDelete:this.onDelete}),null)}});function Fo(t){return typeof t=="function"||Object.prototype.toString.call(t)==="[object Object]"&&!l.isVNode(t)}var jo="fcRadio",Io=l.defineComponent({name:jo,inheritAttrs:!1,props:{modelValue:[String,Number],options:Array},emits:["update:modelValue"],setup:function(e,n){var r=l.toRef(e,"modelValue",[]),i=l.toRef(e,"options");return{options:i,modelValue:r,onInput:function(o){n.emit("update:modelValue",o)}}},render:function(){var e;return l.createVNode(l.resolveComponent("van-radio-group"),l.mergeProps({direction:"horizontal"},this.$attrs,{modelValue:this.modelValue,"onUpdate:modelValue":this.onInput}),Fo(e=(this.options||[]).map(function(n){var r=G({},n),i=n.text,a=n.value;return delete r.text,delete r.value,l.createVNode(l.resolveComponent("van-radio"),l.mergeProps({name:a},r),{default:function(){return[i||n.label||a]}})}))?e:{default:function(){return[e]}})}}),Bo="fcDatePicker",No=l.defineComponent({name:Bo,inheritAttrs:!1,props:{disabled:Boolean,clearable:Boolean,placeholder:String,modelValue:[String,Number],minDate:[String,Date],maxDate:[String,Date]},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=l.ref(!1),i=l.toRef(e,"modelValue"),a=l.computed(function(){return i.value==null||i.value===""?[]:i.value.split("-")}),o=l.computed(function(){return{minDate:e.minDate?ct(e.minDate).toDate():void 0,maxDate:e.maxDate?ct(e.maxDate).toDate():void 0}}),s=function(d){n.emit("update:modelValue",d)};return{show:r,formValue:a,dateRange:o,open:function(){e.disabled||(r.value=!0)},confirm:function(d){var h=d.selectedValues;s(h.join("-")),r.value=!1},clear:function(d){d.stopPropagation(),s("")}}},render:function(){var e=this,n=function(){return e.$props.clearable&&e.modelValue?l.createVNode("i",{class:"van-badge__wrapper van-icon van-icon-clear van-field__clear",onClick:e.clear},null):void 0};return l.createVNode(l.Fragment,null,[l.createVNode(l.resolveComponent("van-field"),{ref:"el",placeholder:this.placeholder,readonly:!0,disabled:this.$props.disabled,onClick:this.open,"model-value":this.modelValue,border:!1,isLink:!0},{"right-icon":n}),l.createVNode(l.resolveComponent("van-popup"),{show:this.show,"onUpdate:show":function(i){return e.show=i},round:!0,position:"bottom"},{default:function(){return[l.createVNode(l.resolveComponent("van-date-picker"),l.mergeProps({columnsType:["year","month","day"]},G(G({},e.$attrs),e.dateRange),{modelValue:e.formValue,onConfirm:e.confirm,onCancel:function(){return e.show=!1}}),null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.el)}}),Lo="fcTimePicker",Uo=l.defineComponent({name:Lo,inheritAttrs:!1,props:{disabled:Boolean,clearable:Boolean,placeholder:String,modelValue:[String,Number]},emits:["update:modelValue","fc.el"],setup:function(e,n){var r=l.ref(!1),i=l.toRef(e,"modelValue"),a=l.computed(function(){return i.value==null||i.value===""?[]:i.value.split(":")}),o=function(u){n.emit("update:modelValue",u)};return{show:r,formValue:a,open:function(){e.disabled||(r.value=!0)},confirm:function(u){var d=u.selectedValues;o(d.join(":")),r.value=!1},clear:function(u){u.stopPropagation(),o("")}}},render:function(){var e=this,n=function(){return e.$props.clearable&&e.modelValue?l.createVNode("i",{class:"van-badge__wrapper van-icon van-icon-clear van-field__clear",onClick:e.clear},null):void 0};return l.createVNode(l.Fragment,null,[l.createVNode(l.resolveComponent("van-field"),{ref:"el",placeholder:this.placeholder,readonly:!0,disabled:this.$props.disabled,onClick:this.open,"model-value":this.modelValue,border:!1,isLink:!0},{"right-icon":n}),l.createVNode(l.resolveComponent("van-popup"),{show:this.show,"onUpdate:show":function(i){return e.show=i},round:!0,position:"bottom"},{default:function(){return[l.createVNode(l.resolveComponent("van-time-picker"),l.mergeProps({columnsType:["hour","minute"]},e.$attrs,{modelValue:e.formValue,onConfirm:e.confirm,onCancel:function(){return e.show=!1}}),null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.el)}});function ke(t,e,n){t[e]=n}function et(t,e){delete t[e]}function wt(t){var e=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},n=arguments.length>2?arguments[2]:void 0,r=!1;for(var i in e)if(Object.prototype.hasOwnProperty.call(e,i)){var a=e[i];if((r=Array.isArray(a))||j.Object(a)){var o=t[i]===void 0;if(r)r=!1,o&&ke(t,i,[]);else if(a._clone&&n!==void 0)if(n)a=a.getRule(),o&&ke(t,i,{});else{ke(t,i,a._clone());continue}else o&&ke(t,i,{});t[i]=wt(t[i],a,n)}else ke(t,i,a),j.Undef(a)||(j.Undef(a.__json)||(t[i].__json=a.__json),j.Undef(a.__origin)||(t[i].__origin=a.__origin))}return n!==void 0&&Array.isArray(t)?t.filter(function(s){return!s||!s.__ctrl}):t}function Ie(t){return wt({},{value:t}).value}var zo=Object.assign||function(t){for(var e,n=1;n<arguments.length;n++)for(var r in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,r)&&ke(t,r,e[r]);return t};function J(){return zo.apply(this,arguments)}function mn(t){return pe(t)!=="object"||t===null?t:t instanceof Array?oe(t):G({},t)}var qo="fcGroup",Go=l.defineComponent({name:qo,props:{field:String,rule:Array,expand:Number,options:Object,button:{type:Boolean,default:!0},max:{type:Number,default:0},min:{type:Number,default:0},modelValue:{type:Array,default:function(){return[]}},defaultValue:Object,sortBtn:{type:Boolean,default:!1},disabled:{type:Boolean,default:void 0},onBeforeRemove:{type:Function,default:function(){}},onBeforeAdd:{type:Function,default:function(){}},formCreateInject:Object,parse:Function},data:function(){return{len:0,cacheRule:{},cacheValue:{},sort:[],form:l.markRaw(this.formCreateInject.form.$form())}},emits:["update:modelValue","change","itemMounted","remove","add"],watch:{rule:{handler:function(e,n){var r=this;Object.keys(this.cacheRule).forEach(function(i){var a=r.cacheRule[i];if(a.$f){var o=a.$f.formData();if(e===n)a.$f.deferSyncValue(function(){wt(a.rule,e),a.$f.setValue(o)},!0);else{var s=a.$f.formData();a.$f.once("reloading",function(){a.$f.setValue(s)}),a.rule=Ie(e)}}})},deep:!0},expand:function(e){var n=e-this.modelValue.length;n>0&&this.expandRule(n)},modelValue:{handler:function(e){var n=this;e=e||[];var r=this.sort,i=r.length,a=i-e.length;if(a<0){for(var o=a;o<0;o++)this.addRule(e.length+o,!0);for(var s=0;s<i;s++)this.setValue(r[s],e[s])}else{if(a>0)for(var u=0;u<a;u++)this.removeRule(r[i-u-1]);e.forEach(function(d,h){n.setValue(r[h],e[h])})}},deep:!0}},methods:{_value:function(e){return e&&me(e,this.field)?e[this.field]:e},cache:function(e,n){this.cacheValue[e]=JSON.stringify(n)},input:function(e){this.$emit("update:modelValue",e),this.$emit("change",e)},formData:function(e,n){var r=this,i=this.cacheRule,a=this.sort;if(a.filter(function(s){return i[s].$f}).length===a.length){var o=a.map(function(s){var u=e===s?n:G({},r.cacheRule[s].$f.form),d=r.field?u[r.field]||null:u;return r.cache(s,d),d});this.input(o)}},setValue:function(e,n){var r=this.field;r&&(n=Se({},r,this._value(n))),this.cacheValue[e]!==JSON.stringify(r?n[r]:n)&&(this.cacheRule[e].$f&&this.cacheRule[e].$f.setValue(n),this.cache(e,n))},addRule:function(e,n){var r=this,i=this.formCreateInject.form.copyRules(this.rule||[]),a=this.options?G({},this.options):{submitBtn:!1,resetBtn:!1};if(this.defaultValue){a.formData||(a.formData={});var o=Ie(this.defaultValue);J(a.formData,this.field?Se({},this.field,o):o)}this.parse&&this.parse({rule:i,options:a,index:this.sort.length}),this.cacheRule[++this.len]={rule:i,options:a},n&&l.nextTick(function(){return r.$emit("add",i,Object.keys(r.cacheRule).length-1)})},add$f:function(e,n,r){var i=this;this.cacheRule[n].$f=r,l.nextTick(function(){i.$emit("itemMounted",r,Object.keys(i.cacheRule).indexOf(n))})},removeRule:function(e,n){var r=this,i=Object.keys(this.cacheRule).indexOf(e);delete this.cacheRule[e],delete this.cacheValue[e],n&&l.nextTick(function(){return r.$emit("remove",i)})},add:function(e){if(!(this.disabled||this.onBeforeAdd(this.modelValue)===!1)){var n=oe(this.modelValue);n.push(this.defaultValue?Ie(this.defaultValue):this.field?null:{}),this.input(n)}},del:function(e,n){if(!(this.disabled||this.onBeforeRemove(this.modelValue,e)===!1)){this.removeRule(n,!0);var r=oe(this.modelValue);r.splice(e,1),this.input(r)}},addIcon:function(e){return l.createVNode("div",{class:"_fc-m-group-btn _fc-m-group-plus-minus",onClick:this.add},null)},delIcon:function(e,n){var r=this;return l.createVNode("div",{class:"_fc-m-group-btn _fc-m-group-plus-minus _fc-m-group-minus",onClick:function(){return r.del(e,n)}},null)},sortUpIcon:function(e){var n=this;return l.createVNode("div",{class:"_fc-m-group-btn _fc-m-group-arrow _fc-m-group-up",onClick:function(){return n.changeSort(e,-1)}},null)},sortDownIcon:function(e){var n=this;return l.createVNode("div",{class:"_fc-m-group-btn _fc-m-group-arrow _fc-m-group-down",onClick:function(){return n.changeSort(e,1)}},null)},changeSort:function(e,n){var r=this,i=this.sort[e];this.sort[e]=this.sort[e+n],this.sort[e+n]=i,this.formCreateInject.subForm(this.sort.map(function(a){return r.cacheRule[a].$f})),this.formData(0)},makeIcon:function(e,n,r){var i=this;if(this.$slots.button)return this.$slots.button({total:e,index:n,vm:this,key:r,del:function(){return i.del(n,r)},add:this.add});var a=[];return(!this.max||e<this.max)&&e===n+1&&a.push(this.addIcon(r)),e>this.min&&a.push(this.delIcon(n,r)),this.sortBtn&&n&&a.push(this.sortUpIcon(n)),this.sortBtn&&n!==e-1&&a.push(this.sortDownIcon(n)),a},emitEvent:function(e,n,r,i){this.$emit.apply(this,[e].concat(oe(n),[this.cacheRule[i].$f,r]))},expandRule:function(e){for(var n=0;n<e;n++)this.addRule(n)}},created:function(){var e=this;l.watch(function(){return G({},e.cacheRule)},function(i){e.sort=Object.keys(i)},{immediate:!0});for(var n=(this.expand||0)-this.modelValue.length,r=0;r<this.modelValue.length;r++)this.addRule(r);n>0&&this.expandRule(n)},render:function(){var e=this,n=this.sort,r=this.button,i=this.form,a=this.disabled,o=n.length===0?this.$slots.default?this.$slots.default({vm:this,add:this.add}):l.createVNode("div",{key:"a_def",class:"_fc-m-group-plus-minus _fc-m-group-add fc-clock",onClick:this.add},null):n.map(function(s,u){var d=e.cacheRule[s],h=d.rule,_=d.options,y=r&&!a?e.makeIcon(n.length,u,s):[];return l.createVNode("div",{class:"_fc-m-group-container",key:s},[l.createVNode(i,l.mergeProps({key:s},{disabled:a,"onUpdate:modelValue":function(S){return e.formData(s,S)},"onEmit-event":function(S){for(var C=arguments.length,F=new Array(C>1?C-1:0),q=1;q<C;q++)F[q-1]=arguments[q];return e.emitEvent(S,F,u,s)},"onUpdate:api":function(S){return e.add$f(u,s,S)},inFor:!0,modelValue:e.field?Se({},e.field,e._value(e.modelValue[u])):e.modelValue[u],rule:h,option:_,extendOption:!0}),null),l.createVNode("div",{class:"_fc-m-group-idx"},[u+1]),y.length?l.createVNode("div",{class:"_fc-m-group-handle fc-clock"},[y]):null])});return l.createVNode("div",{key:"con",class:"_fc-m-group "+(a?"_fc-m-group-disabled":"")},[o])}}),Dr={name:"IconWarning"},Ho={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},Wo=l.createElementVNode("path",{fill:"currentColor",d:"M512 64a448 448 0 110 896 448 448 0 010-896zm0 832a384 384 0 000-768 384 384 0 000 768zm48-176a48 48 0 11-96 0 48 48 0 0196 0zm-48-464a32 32 0 0132 32v288a32 32 0 01-64 0V288a32 32 0 0132-32z"},null,-1),Xo=[Wo];function Yo(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("svg",Ho,Xo)}Dr.render=Yo;var Jo=[fo,Dr,Eo,Ao,Po,Io,ko,No,Uo,Go,To];function tt(t,e){var n=null;return function(){for(var r=this,i=arguments.length,a=new Array(i),o=0;o<i;o++)a[o]=arguments[o];n!==null&&clearTimeout(n),n=setTimeout(function(){return t.call.apply(t,[r].concat(a))},e)}}function $t(t){var e=t.replace(/([A-Z])/g,"-$1").toLocaleLowerCase();return e.indexOf("-")===0&&(e=e.substr(1)),e}function Ko(t){return t.replace(t[0],t[0].toLocaleUpperCase())}var Tt=function(e,n){if(!(!e||e===n)){if(e.props.formCreateInject)return e.props.formCreateInject;if(e.parent)return Tt(e.parent,n)}};function Qo(t,e,n){return l.defineComponent({name:"FormCreate"+(t.isMobile?"Mobile":""),components:e,directives:n,props:{rule:{type:Array,required:!0,default:function(){return[]}},option:{type:Object,default:function(){return{}}},extendOption:Boolean,driver:[String,Object],modelValue:Object,disabled:{type:Boolean,default:void 0},preview:{type:Boolean,default:void 0},index:[String,Number],api:Object,locale:[String,Object],name:String,subForm:{type:Boolean,default:!0},inFor:Boolean},emits:["update:api","update:modelValue","mounted","submit","reset","change","emit-event","control","remove-rule","remove-field","sync","reload","repeat-field","update","validate-field-fail","validate-fail","created"],render:function(){return this.fc.render()},setup:function(i){var a=l.getCurrentInstance();l.provide("parentFC",a);var o=l.inject("parentFC",null),s=o;if(o)for(;s.setupState.parent;)s=s.setupState.parent;else s=a;var u=l.toRefs(i),d=u.rule,h=u.modelValue,_=u.subForm,y=u.inFor,x=l.reactive({ctxInject:{},destroyed:!1,isShow:!0,unique:1,renderRule:oe(d.value||[]),updateValue:JSON.stringify(h.value||{})}),S=new t(a),C=S.api(),F=y.value,q=function(){if(o){var X=Tt(a,o);if(X){var ce;F?(ce=Ze(X.getSubForm()),ce.push(C)):ce=C,X.subForm(ce)}}},U=function(){var X=Tt(a,o);if(X)if(F){var ce=Ze(X.getSubForm()),se=ce.indexOf(C);se>-1&&ce.splice(se,1)}else X.subForm()},L=null;l.onBeforeMount(function(){l.watchEffect(function(){var K="",X=i.option&&i.option.globalClass||{};Object.keys(X).forEach(function(ce){var se="";X[ce].style&&Object.keys(X[ce].style).forEach(function(re){se+=$t(re)+":"+X[ce].style[re]+";"}),X[ce].content&&(se+=X[ce].content+";"),se&&(K+=".".concat(ce,"{").concat(se,"}"))}),i.option&&i.option.style&&(K+=i.option.style),L||(L=document.createElement("style"),L.type="text/css",document.head.appendChild(L)),L.innerHTML=K||""})});var Y=tt(function(){S.bus.$emit("$loadData.$topForm")},100),ne=tt(function(){S.bus.$emit("$loadData.$form")},100),Z=function(X){S.bus.$emit("change-$form."+X)};return l.onMounted(function(){o&&(C.top.bus.$on("$loadData.$form",Y),C.top.bus.$on("change",Z)),S.mounted()}),l.onBeforeUnmount(function(){o&&(C.top.bus.$off("$loadData.$form",Y),C.top.bus.$off("change",Z)),L&&document.head.removeChild(L),U(),x.destroyed=!0,S.unmount()}),l.onUpdated(function(){S.updated()}),l.watch(_,function(K){K?q():U()},{immediate:!0}),l.watch(function(){return oe(d.value)},function(K){S.$handle.isBreakWatch()||K.length===x.renderRule.length&&K.every(function(X){return x.renderRule.indexOf(X)>-1})||(S.$handle.updateAppendData(),S.$handle.reloadRule(d.value),a.setupState.renderRule())}),l.watch(function(){return i.option},function(){S.initOptions(),C.refresh()},{deep:!0}),l.watch(function(){return[i.disabled,i.preview]},function(){C.refresh()}),l.watch(h,function(K){JSON.stringify(K||{})!==x.updateValue&&(C.config.forceCoverValue?C.coverValue(K||{}):C.setValue(K||{}))},{deep:!0,flush:"post"}),l.watch(function(){return i.index},function(){C.coverValue({}),S.$handle.updateAppendData(),l.nextTick(function(){l.nextTick(function(){C.clearValidateState()})})},{flush:"sync"}),G(G({fc:l.markRaw(S),parent:o&&l.markRaw(o),top:l.markRaw(s),fapi:l.markRaw(C)},l.toRefs(x)),{},{getGroupInject:function(){return Tt(a,o)},refresh:function(){++x.unique},renderRule:function(){x.renderRule=oe(d.value||[])},updateValue:function(X){if(!x.destroyed){var ce=JSON.stringify(X);x.updateValue!==ce&&(x.updateValue=ce,a.emit("update:modelValue",X),l.nextTick(function(){ne(),o||Y()}))}}})},created:function(){var i=l.getCurrentInstance();i.emit("update:api",i.setupState.fapi),i.setupState.fc.init()}})}var Pr=["props"],Rr=["class","style","directives"],kr=["on","hook"],nt=function t(e){var n=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{},r=arguments.length>2&&arguments[2]!==void 0?arguments[2]:{},i=[].concat(Pr,oe(r.normal||[])),a=[].concat(Rr,oe(r.array||[])),o=[].concat(kr,oe(r.functional||[])),s=r.props||[];return e.reduce(function(u,d){for(var h in d)if(u[h])if(s.indexOf(h)>-1)u[h]=t([d[h]],u[h]);else if(i.indexOf(h)>-1)u[h]=G(G({},u[h]),d[h]);else if(a.indexOf(h)>-1){var _=u[h]instanceof Array?u[h]:[u[h]],y=d[h]instanceof Array?d[h]:[d[h]];u[h]=[].concat(oe(_),oe(y))}else if(o.indexOf(h)>-1)for(var x in d[h])if(u[h][x]){var S=u[h][x]instanceof Array?u[h][x]:[u[h][x]],C=d[h][x]instanceof Array?d[h][x]:[d[h][x]];u[h][x]=[].concat(oe(S),oe(C))}else u[h][x]=d[h][x];else if(h==="hook")for(var F in d[h])u[h][F]?u[h][F]=Zo(u[h][F],d[h][F]):u[h][F]=d[h][F];else u[h]=d[h];else i.indexOf(h)>-1||o.indexOf(h)>-1||s.indexOf(h)>-1?u[h]=G({},d[h]):a.indexOf(h)>-1?u[h]=d[h]instanceof Array?oe(d[h]):pe(d[h])==="object"?G({},d[h]):d[h]:u[h]=d[h];return u},n)},Zo=function(e,n){return function(){e&&e.apply(this,arguments),n&&n.apply(this,arguments)}},gn=["type","slot","ignore","emitPrefix","value","name","native","hidden","display","inject","options","emit","link","prefix","suffix","update","sync","optionsTo","key","slotUpdate","computed","preview","component","cache","modelEmit"],Ft=["validate","children","control"],jt=["effect","deep","renderSlots"];function Vr(){return[].concat(gn,oe(Pr),oe(Rr),oe(kr),Ft,jt)}function Mr(t,e,n){return"[form-create ".concat(t,"]: ").concat(e)+(n?`

rule: `+JSON.stringify(n.getRule?n.getRule():n):"")}function vn(t,e){console.error(Mr("err",t,e))}function ea(t){vn(t.toString()),console.error(t)}function Be(t){var e=t.replace(/(-[a-z])/g,function(n){return n.replace("-","").toLocaleUpperCase()});return Tr(e)}function Tr(t){return t.replace(t[0],t[0].toLowerCase())}var yn="[[FORM-CREATE-PREFIX-",_n="-FORM-CREATE-SUFFIX]]";function bn(t,e){return JSON.stringify(wt(Array.isArray(t)?[]:{},t,!0),function(n,r){if(!(r&&r._isVue===!0)){if(typeof r!="function")return r;if(r.__json)return r.__json;if(r.__origin&&(r=r.__origin),!r.__emit)return yn+r+_n}},e)}function wn(t){return new Function("return "+t)()}function ze(t,e){if(t&&j.String(t)&&t.length>4){var n=t.trim(),r=!1;try{if(n.indexOf(_n)>0&&n.indexOf(yn)===0)n=n.replace(_n,"").replace(yn,""),r=!0;else if(n.indexOf("$FN:")===0)n=n.substring(4),r=!0;else if(n.indexOf("$EXEC:")===0)n=n.substring(6),r=!0;else if(n.indexOf("$GLOBAL:")===0){var i=n.substring(8);return n=function(){for(var s=arguments.length,u=new Array(s),d=0;d<s;d++)u[d]=arguments[d];var h=u[0].api.getGlobalEvent(i);if(h)return h.call.apply(h,[this].concat(u))},n.__json=t,n.__inject=!0,n}else{if(n.indexOf("$FNX:")===0)return n=wn("function($inject){"+n.substring(5)+"}"),n.__json=t,n.__inject=!0,n;(!e&&n.indexOf("function ")===0&&n!=="function "||!e&&n.indexOf("function(")===0&&n!=="function(")&&(r=!0)}if(!r)return t;var a;try{a=wn(n)}catch{a=wn("function "+n)}return a.__json=t,a}catch(o){vn("\u89E3\u6790\u5931\u8D25:".concat(n,`

err: `).concat(o));return}}return t}function Fr(t,e){return JSON.parse(t,function(n,r){return j.Undef(r)||!r.indexOf?r:ze(r,e)})}function $n(t,e){return{value:t,enumerable:!1,configurable:!1,writable:!!e}}function jr(t,e){return Ir([t],e||!1)[0]}function Ir(t,e){return wt([],oe(t),e||!1)}function He(t,e){return nt(Array.isArray(e)?e:[e],t,{array:Ft,normal:jt}),t}function Br(t){var e=j.Function(t.getRule)?t.getRule():t;return e.type||(e.type="input"),e}function ta(t,e){return t?(Object.keys(e||{}).forEach(function(n){e[n]&&(t[n]=He(t[n]||{},e[n]))}),t):e}function Nr(t,e){Object.defineProperties(t,Object.keys(e).reduce(function(n,r){return n[r]={get:function(){return e[r]()}},n},{}))}function rt(t){return t.__fc__||(t.__origin__?t.__origin__.__fc__:null)}function de(t,e){try{e=t()}catch(n){ea(n)}return e}function On(){var t={},e=function(r){return r||"default"};return{setSlot:function(r,i){r=e(r),!(!i||Array.isArray(i)&&i.length)&&(t[r]||(t[r]=[]),t[r].push(i))},getSlot:function(r,i){r=e(r);var a=[];return(t[r]||[]).forEach(function(o){if(Array.isArray(o))a.push.apply(a,oe(o));else if(j.Function(o)){var s=o.apply(void 0,oe(i||[]));Array.isArray(s)?a.push.apply(a,oe(s)):a.push(s)}else j.Undef(o)||a.push(o)}),a},getSlots:function(){var r=this,i={};return Object.keys(t).forEach(function(a){i[a]=function(){for(var o=arguments.length,s=new Array(o),u=0;u<o;u++)s[u]=arguments[u];return r.getSlot(a,s)}}),i},slotLen:function(r){return r=e(r),t[r]?t[r].length:0},mergeBag:function(r){var i=this;if(!r)return this;var a=j.Function(r.getSlots)?r.getSlots():r;return Array.isArray(r)||l.isVNode(r)?this.setSlot(void 0,function(){return r}):Object.keys(a).forEach(function(o){i.setSlot(o,a[o])}),this}}}function Lr(t){var e=G({},t.props||{});return Object.keys(t.on||{}).forEach(function(n){n.indexOf("-")>0&&(n=Be(n));var r="on".concat(Ko(n));Array.isArray(e[r])?e[r]=[].concat(oe(e[r]),[t.on[n]]):e[r]?e[r]=[e[r],t.on[n]]:e[r]=t.on[n]}),e.key=t.key,e.ref=t.ref,e.class=t.class,e.id=t.id,e.style=t.style,e.slot&&delete e.slot,e}function It(t,e){return Object.setPrototypeOf(t,e),t}var Ur=function(e,n){return typeof e=="string"?String(n):typeof e=="number"?Number(n):n},it={"==":function(e,n){return JSON.stringify(e)===JSON.stringify(Ur(e,n))},"!=":function(e,n){return!it["=="](e,n)},">":function(e,n){return e>n},">=":function(e,n){return e>=n},"<":function(e,n){return e<n},"<=":function(e,n){return e<=n},on:function(e,n){return e&&e.indexOf&&e.indexOf(Ur(e[0],n))>-1},notOn:function(e,n){return!it.on(e,n)},in:function(e,n){return n&&n.indexOf&&n.indexOf(e)>-1},notIn:function(e,n){return!it.in(e,n)},between:function(e,n){return e>n[0]&&e<n[1]},notBetween:function(e,n){return e<n[0]||e>n[1]},empty:function(e){return j.empty(e)},notEmpty:function(e){return!j.empty(e)},pattern:function(e,n){return new RegExp(n,"g").test(e)}};function Ve(t,e){return(Array.isArray(e)?e:(e||"").split(".")).forEach(function(n){t!=null&&(t=t[n])}),t}function na(t){for(var e=/{{\s*(.*?)\s*}}/g,n,r={};(n=e.exec(t))!==null;)n[1]&&(r[n[1]]=!0);return Object.keys(r)}function zr(t){var e=t.split("."),n=[],r="";return e.forEach(function(i,a){a===0?r=i:r+="."+i,n.push(r)}),n.join(" && ")}function qr(){return{props:{},on:{},options:[],children:[],hidden:!1,display:!0,value:void 0}}function Bt(t,e){return function(n,r,i){var a=arguments.length>3&&arguments[3]!==void 0?arguments[3]:{},o=new Sn(t,n,r,i,a);return e&&(j.Function(e)?e(o):o.props(e)),o}}function Sn(t,e,n,r,i){this._data=J(qr(),{type:t,title:e,field:n,value:r,props:i||{}}),this.event=this.on}J(Sn.prototype,{getRule:function(){return this._data},setProp:function(e,n){return ke(this._data,e,n),this},modelField:function(e){return this._data.modelField=e,this},_clone:function(){var e=new this.constructor;return e._data=jr(this._data),e}});function Gr(t){t.forEach(function(e){Sn.prototype[e]=function(n){return He(this._data,Se({},e,arguments.length<2?n:Se({},n,arguments[1]))),this}})}Gr(Vr());var ra=Bt("");function ia(t,e,n){var r=ra("",e);return r._data.type=t,r._data.title=n,r}function oa(){return{create:ia,factory:Bt}}function aa(t,e,n){var r="fail to ".concat(t," ").concat(n.status,"'"),i=new Error(r);return i.status=n.status,i.url=t,i}function Hr(t){var e=t.responseText||t.response;if(!e)return e;try{return JSON.parse(e)}catch{return e}}function Wr(t){if(!(typeof XMLHttpRequest>"u")){var e=new XMLHttpRequest,n=t.action||"";if(e.upload&&t.onProgress&&e.upload.addEventListener("progress",function(o){o.percent=o.total>0?o.loaded/o.total*100:0,t.onProgress(o)}),t.query){var r=new URLSearchParams(t.query).toString();n.includes("?")?n+="&".concat(r):n+="?".concat(r)}e.onerror=function(s){t.onError(s)},e.onload=function(){if(e.status<200||e.status>=300)return t.onError(aa(n,t,e),Hr(e));t.onSuccess(Hr(e))},e.open(t.method||"get",n,!0);var i;(t.data||t.file)&&(t.file||(t.dataType||"").toLowerCase()!=="json"?(i=new FormData,Object.keys(t.data||{}).map(function(o){i.append(o,t.data[o])})):(i=JSON.stringify(t.data||{}),e.setRequestHeader("content-type","application/json"))),t.file&&i.append(t.filename,t.file,t.file.name),t.withCredentials&&"withCredentials"in e&&(e.withCredentials=!0);var a=t.headers||{};Object.keys(a).forEach(function(o){a[o]!=null&&e.setRequestHeader(o,a[o])}),e.send(i)}}function Xr(t,e,n){return new Promise(function(r,i){(e||Wr)(G(G({},t),{},{onSuccess:function(o){var s=function(h){return h},u=ze(t.parse);j.Function(u)?s=u:u&&j.String(u)&&(s=function(h){return Ve(h,u)}),r(s(o,void 0,n))},onError:function(o){i(o)}}))})}function Ot(t){return Ie(t)}function sa(t){function e(a){return j.Undef(a)?a=t.fields():Array.isArray(a)||(a=[a]),a}function n(a,o,s){e(a).forEach(function(u){t.getCtxs(u).forEach(function(d){ke(d.rule,o,s),t.$render.clearCache(d)})})}function r(){var a=t.subForm;return Object.keys(a).reduce(function(o,s){var u=a[s];return u&&(Array.isArray(u)?o.push.apply(o,oe(u)):o.push(u)),o},[])}var i={get config(){return t.options},set config(a){t.fc.options.value=a},get options(){return t.options},set options(a){t.fc.options.value=a},get form(){return t.form},get rule(){return t.rules},get parent(){return t.vm.setupState.parent&&t.vm.setupState.parent.setupState.fapi},get top(){return i.parent?i.parent.top:i},get children(){return r()},get siblings(){var a=t.vm.setupState.getGroupInject();if(a){var o=a.getSubForm();if(Array.isArray(o))return oe(o)}},get index(){var a=i.siblings;if(a){var o=a.indexOf(i);return o>-1?o:void 0}},formData:function(o){if(o==null){var s={};return Object.keys(t.form).forEach(function(u){t.ignoreFields.indexOf(u)===-1&&(s[u]=Ot(t.form[u]))}),s}else return e(o).reduce(function(u,d){return u[d]=i.getValue(d),u},{})},getValue:function(o){var s=t.getFieldCtx(o);return s?Ot(s.rule.value):t.options.appendValue!==!1&&me(t.appendData,o)?Ot(t.appendData[o]):void 0},coverValue:function(o){var s=G({},o||{});t.deferSyncValue(function(){t.appendData={},i.fields().forEach(function(u){var d=t.fieldCtx[u];if(d){var h=me(o,u);d.forEach(function(_){_.rule.value=h?o[u]:void 0}),delete s[u]}}),J(t.appendData,s)},!0)},setValue:function(o){var s=o;arguments.length>=2&&(s=Se({},o,arguments[1])),t.deferSyncValue(function(){Object.keys(s).forEach(function(u){var d=t.fieldCtx[u];if(!d)return t.appendData[u]=s[u];d.forEach(function(h){h.rule.value=s[u]})})},!0)},removeField:function(o){var s=t.getCtx(o);return t.deferSyncValue(function(){t.getCtxs(o).forEach(function(u){u.rm()})},!0),s?s.origin:void 0},removeRule:function(o){var s=o&&rt(o);if(!!s)return s.rm(),s.origin},fields:function(){return t.fields()},append:function(o,s,u){var d=t.sort.length-1,h,_=t.getCtx(s);if(_)if(u){if(h=_.getPending("children",_.rule.children),!Array.isArray(h))return;d=_.rule.children.length-1}else d=_.root.indexOf(_.origin),h=_.root;else h=t.rules;h.splice(d+1,0,o)},prepend:function(o,s,u){var d=0,h,_=t.getCtx(s);if(_)if(u){if(h=_.getPending("children",_.rule.children),!Array.isArray(h))return}else d=_.root.indexOf(_.origin),h=_.root;else h=t.rules;h.splice(d,0,o)},hidden:function(o,s){n(s,"hidden",!!o),t.refresh()},hiddenStatus:function(o){var s=t.getCtx(o);if(!!s)return!!s.rule.hidden},display:function(o,s){n(s,"display",!!o),t.refresh()},displayStatus:function(o){var s=t.getCtx(o);if(!!s)return!!s.rule.display},disabled:function(o,s){e(s).forEach(function(u){t.getCtxs(u).forEach(function(d){ke(d.rule.props,"disabled",!!o)})}),t.refresh()},all:function(o){return Object.keys(t.ctxs).map(function(s){var u=t.ctxs[s];return o?u.origin:u.rule})},model:function(o){return t.fields().reduce(function(s,u){var d=t.fieldCtx[u][0];return s[u]=o?d.origin:d.rule,s},{})},component:function(o){return Object.keys(t.nameCtx).reduce(function(s,u){var d=t.nameCtx[u].map(function(h){return o?h.origin:h.rule});return s[u]=d.length===1?d[0]:d,s},{})},bind:function(){return i.form},reload:function(o){t.reloadRule(o)},updateOptions:function(o){t.fc.updateOptions(o),i.refresh()},onSubmit:function(o){i.updateOptions({onSubmit:o})},sync:function(o){if(Array.isArray(o)){o.forEach(function(u){return i.sync(u)});return}var s=j.Object(o)?rt(o):t.getCtxs(o);!s||(s=Array.isArray(s)?s:[s],s.forEach(function(u){if(!u.deleted){var d=t.subForm[u.id];d&&(Array.isArray(d)?d.forEach(function(h){h.refresh()}):d&&d.refresh()),t.$render.clearCache(u)}}),t.refresh())},refresh:function(){r().forEach(function(o){o.refresh()}),t.$render.clearCacheAll(),t.refresh()},refreshOptions:function(){t.$manager.updateOptions(t.options),i.refresh()},hideForm:function(o){t.vm.setupState.isShow=!o},changeStatus:function(){return t.changeStatus},clearChangeStatus:function(){t.changeStatus=!1},updateRule:function(o,s){t.getCtxs(o).forEach(function(u){J(u.rule,s)})},updateRules:function(o){Object.keys(o).forEach(function(s){i.updateRule(s,o[s])})},mergeRule:function(o,s){t.getCtxs(o).forEach(function(u){He(u.rule,s)})},mergeRules:function(o){Object.keys(o).forEach(function(s){i.mergeRule(s,o[s])})},getRule:function(o,s){var u=t.getCtx(o);if(u)return s?u.origin:u.rule},getRenderRule:function(o){var s=t.getCtx(o);if(s)return s.prop},getRefRule:function(o){var s=t.getCtxs(o);if(s){var u=s.map(function(d){return d.rule});return u.length===1?u[0]:u}},setEffect:function(o,s,u){var d=t.getCtx(o);d&&s&&(s[0]==="$"&&(s=s.substr(1)),me(d.rule,"$"+s)&&ke(d.rule,"$"+s,u),me(d.rule,"effect")||(d.rule.effect={}),ke(d.rule.effect,s,u))},clearEffectData:function(o,s){var u=t.getCtx(o);u&&(s&&s[0]==="$"&&(s=s.substr(1)),u.clearEffectData(s),i.sync(o))},updateValidate:function(o,s,u){u?i.mergeRule(o,{validate:s}):n(o,"validate",s)},updateValidates:function(o,s){Object.keys(o).forEach(function(u){i.updateValidate(u,o[u],s)})},refreshValidate:function(){i.refresh()},resetFields:function(o){e(o).forEach(function(s){t.getCtxs(s).forEach(function(u){t.$render.clearCache(u),u.rule.value=Ot(u.defaultValue)})}),l.nextTick(function(){l.nextTick(function(){l.nextTick(function(){i.clearValidateState(o)})})}),o==null&&(j.Function(t.options.onReset)&&de(function(){return t.options.onReset(i)}),t.vm.emit("reset",i))},method:function(o,s){var u=i.el(o);if(!u||!u[s])throw new Error(Mr("err","".concat(s," \u65B9\u6CD5\u4E0D\u5B58\u5728")));return function(){return u[s].apply(u,arguments)}},exec:function(o,s){for(var u=arguments.length,d=new Array(u>2?u-2:0),h=2;h<u;h++)d[h-2]=arguments[h];return de(function(){return i.method(o,s).apply(void 0,d)})},toJson:function(o){return bn(i.rule,o)},trigger:function(o,s){for(var u=i.el(o),d=arguments.length,h=new Array(d>2?d-2:0),_=2;_<d;_++)h[_-2]=arguments[_];u&&u.$emit.apply(u,[s].concat(h))},el:function(o){var s=t.getCtx(o);if(s)return s.el||t.vm.refs[s.ref]},closeModal:function(o){t.bus.$emit("fc:closeModal:"+o)},getSubForm:function(o){var s=t.getCtx(o);return s?t.subForm[s.id]:void 0},getChildrenRuleList:function(o){var s=pe(o)==="object",u=s?rt(o):t.getCtx(o),d=u?u.rule:s?o:i.getRule(o);if(!d)return[];var h=[],_=function(x){x&&x.forEach(function(S){pe(S)==="object"&&(S.field&&h.push(S),h.push.apply(h,oe(i.getChildrenRuleList(S))))})};return _(u?u.loadChildrenPending():d.children),h},getParentRule:function(o){var s=pe(o)==="object",u=s?rt(o):t.getCtx(o);return u.parent.rule},getParentSubRule:function(o){var s=pe(o)==="object",u=s?rt(o):t.getCtx(o);if(u){var d=u.getParentGroup();if(d)return d.rule}},getChildrenFormData:function(o){var s=i.getChildrenRuleList(o);return s.reduce(function(u,d){return u[d.field]=Ot(d.value),u},{})},setChildrenFormData:function(o,s,u){var d=i.getChildrenRuleList(o);t.deferSyncValue(function(){d.forEach(function(h){me(s,h.field)?h.value=s[h.field]:u&&(h.value=void 0)})})},getGlobalEvent:function(o){var s=i.options.globalEvent[o];if(s)return pe(s)==="object"&&(s=s.handle),ze(s)},getGlobalData:function(o){return new Promise(function(s,u){var d=i.options.globalData[o];d||s(t.fc.loadData[o]),d.type==="fetch"?i.fetch(d).then(function(h){s(h)}).catch(u):s(d.data)})},nextTick:function(o){t.bus.$once("next-tick",o),t.refresh()},nextRefresh:function(o){t.nextRefresh(),o&&de(o)},deferSyncValue:function(o,s){t.deferSyncValue(o,s)},emit:function(o){for(var s,u=arguments.length,d=new Array(u>1?u-1:0),h=1;h<u;h++)d[h-1]=arguments[h];(s=t.vm).emit.apply(s,[o].concat(d))},bus:t.bus,fetch:function(o){return new Promise(function(s,u){o=Ie(o),o=t.loadFetchVar(o),t.beforeFetch(o).then(function(){return Xr(o,t.fc.create.fetch,i).then(function(d){de(function(){return o.onSuccess&&o.onSuccess(d)}),s(d)}).catch(function(d){de(function(){return o.onError&&o.onError(d)}),u(d)})})})},watchFetch:function(o,s,u,d){return t.fc.watchLoadData(function(h,_){var y=Ie(o);y=t.loadFetchVar(y,h),!(d&&d(y,_)===!1)&&t.beforeFetch(y).then(function(){return Xr(y,t.fc.create.fetch,i).then(function(x){de(function(){return y.onSuccess&&y.onSuccess(x)}),s&&s(x,_)}).catch(function(x){de(function(){return y.onError&&y.onError(x)}),u&&u(x)})})},o.wait==null?1e3:o.wait)},getData:function(o,s){return t.fc.getLoadData(o,s)},watchData:function(o){return t.fc.watchLoadData(function(s,u){de(function(){return o(s,u)})})},setData:function(o,s,u){return t.fc.setData(o,s,u)},refreshData:function(o){return t.fc.refreshData(o)},t:function(o,s){return t.fc.t(o,s)},getLocale:function(){return t.fc.getLocale()},helper:{tidyFields:e,props:n}};return["on","once","off"].forEach(function(a){i[a]=function(){var o;(o=t.bus)["$".concat(a)].apply(o,arguments)}}),i.changeValue=i.changeField=i.setValue,i}function ua(t){J(t.prototype,{initCache:function(){this.clearCacheAll()},clearCache:function(n){if(!n.rule.cache){if(!this.cache[n.id]){n.parent&&this.clearCache(n.parent);return}(this.cache[n.id].use===!0||this.cache[n.id].parent)&&this.$handle.refresh(),this.cache[n.id].parent&&this.clearCache(this.cache[n.id].parent),this.cache[n.id]=null}},clearCacheAll:function(){this.cache={}},setCache:function(n,r,i){this.cache[n.id]={vnode:r,use:!1,parent:i,slot:n.rule.slot}},getCache:function(n){var r=this.cache[n.id];if(r)return r.use=!0,r.vnode}})}function la(t){return t==null?"":pe(t)==="object"?JSON.stringify(t,null,2):String(t)}var ca=0;function Nt(){var t=370+ ++ca;return"F"+Math.random().toString(36).substr(3,3)+Number("".concat(Date.now())).toString(36)+t.toString(36)+"c"}function Je(t,e,n){var r=t,i;return(e||"").split(".").forEach(function(a){i&&((!r[i]||pe(r[i])!="object")&&(r[i]={}),r=r[i]),i=a}),r[i]=n,r}function fa(t){J(t.prototype,{initRender:function(){this.cacheConfig={}},getTypeSlot:function(n){var r=function i(a){if(a){var o=void 0;return n.rule.field&&(o=a.slots["field-"+$t(n.rule.field)]||a.slots["field-"+n.rule.field]),o||(o=a.slots["type-"+$t(n.type)]||a.slots["type-"+n.type]),o||i(a.setupState.parent)}};return r(this.vm)},render:function(){var n=this;if(!!this.vm.setupState.isShow){this.$manager.beforeRender();var r=On();return this.sort.forEach(function(i){n.renderSlot(r,n.$handle.ctxs[i])}),this.$manager.render(r)}},renderSlot:function(n,r,i){if(this.isFragment(r)){r.initProp(),this.mergeGlobal(r),r.initNone();var a=this.renderChildren(r.loadChildrenPending(),r),o=a.default;o&&n.setSlot(r.rule.slot,function(){return o()}),delete a.default,n.mergeBag(a)}else n.setSlot(r.rule.slot,this.renderCtx(r,i))},mergeGlobal:function(n){var r=this,i=this.$handle.options.global;!i||(this.cacheConfig[n.trueType]||(this.cacheConfig[n.trueType]=l.computed(function(){var a=r.$handle.options.global;return He({},[a["*"]||a.default||{},a[n.originType]||a[n.type]||a[n.type]||{}])})),n.prop=He({},[this.cacheConfig[n.trueType].value,n.prop]))},setOptions:function(n){var r=n.loadPending({key:"options",origin:n.prop.options,def:[]});n.prop.options=r,n.prop.optionsTo&&r&&Je(n.prop,n.prop.optionsTo,r)},deepSet:function(n){var r=n.rule.deep;r&&Object.keys(r).sort(function(i,a){return i.length<a.length?-1:1}).forEach(function(i){Je(n.prop,i,r[i])})},parseSide:function(n,r){return j.Object(n)?He({props:{formCreateInject:r.prop.props.formCreateInject}},n):n},renderSides:function(n,r,i){var a=r[i?"rule":"prop"];return[this.renderRule(this.parseSide(a.prefix,r)),n,this.renderRule(this.parseSide(a.suffix,r))]},renderId:function(n,r){var i=this,a=this.$handle[r==="field"?"fieldCtx":"nameCtx"][n];return a?a.map(function(o){return i.renderCtx(o,o.parent)}):void 0},renderCtx:function(n,r){var i=this;try{if(n.type==="hidden")return;var a=n.rule;if(!this.cache[n.id]||this.cache[n.id].slot!==a.slot){var o;n.initProp(),this.mergeGlobal(n),n.initNone(),this.$manager.tidyRule(n),this.deepSet(n),this.setOptions(n),this.ctxProp(n);var s=n.prop;s.preview=!!(s.preview!=null?s.preview:this.$handle.preview),s.props.formCreateInject=this.injectProp(n);var u=s.cache!==!1,d=s.preview;if(s.hidden){this.setCache(n,void 0,r);return}o=function(){for(var _=arguments.length,y=new Array(_),x=0;x<_;x++)y[x]=arguments[x];var S={rule:a,prop:s,preview:d,api:i.$handle.api,model:s.model||{},slotValue:y};y.length&&a.slotUpdate&&de(function(){return a.slotUpdate(S)});var C={},F=n.loadChildrenPending();n.parser.renderChildren?C=n.parser.renderChildren(F,n):n.parser.loadChildren!==!1&&(C=i.renderChildren(F,n)),Object.keys(s.renderSlots||{}).forEach(function(L){C[L]=function(){var Y=i.parseSide(s.renderSlots[L],n);return i.renderRule(Y)}});var q=i.getTypeSlot(n),U;return q?(S.children=C,U=q(S)):U=d?n.parser.preview(mn(C),n):n.parser.render(mn(C),n),U=i.renderSides(U,n),!(!n.input&&j.Undef(s.native))&&s.native!==!0&&(i.fc.targetFormDriver("updateWrap",n),U=i.$manager.makeWrap(n,U)),n.none&&(Array.isArray(U)?U=U.map(function(L){return!L||!L.__v_isVNode?L:i.none(L)}):U=i.none(U)),u&&i.setCache(n,function(){return i.stable(U)},r),U},this.setCache(n,o,r)}return function(){var h=i.getCache(n);if(h)return h.apply(void 0,arguments);if(i.cache[n.id])return;var _=i.renderCtx(n,n.parent);if(_)return _()}}catch(h){console.error(h);return}},none:function(n){if(n)return n.props.class=this.mergeClass(n.props.class,"fc-none"),n},mergeClass:function(n,r){if(Array.isArray(n))n.push(r);else return n?[n,r]:r;return n},stable:function(n){var r=this,i=Array.isArray(n)?n:[n];return i.forEach(function(a){a&&a.__v_isVNode&&a.children&&pe(a.children)==="object"&&(a.children.$stable=!0,r.stable(a.children))}),n},getModelField:function(n){return n.prop.modelField||n.parser.modelField||this.fc.modelFields[this.vNode.aliasMap[n.type]]||this.fc.modelFields[n.type]||this.fc.modelFields[n.originType]||"modelValue"},isFragment:function(n){return n.type==="fragment"||n.type==="template"},injectProp:function(n){var r=this,i=this.vm.setupState;i.ctxInject[n.id]||(i.ctxInject[n.id]={api:this.$handle.api,form:this.fc.create,subForm:function(s){r.$handle.addSubForm(n,s)},getSubForm:function(){return r.$handle.subForm[n.id]},slots:function(){return r.vm.setupState.top.slots},options:[],children:[],preview:!1,id:n.id,field:n.field,rule:n.rule,input:n.input,t:function(){var s;return(s=r.$handle.api).t.apply(s,arguments)},updateValue:function(s){r.$handle.onUpdateValue(n,s)}});var a=i.ctxInject[n.id];return J(a,{preview:n.prop.preview,options:n.prop.options,children:n.loadChildrenPending()}),a},ctxProp:function(n){var r=this,i=n.ref,a=n.key,o=n.rule;this.$manager.mergeProp(n),n.parser.mergeProp(n);var s=[{ref:i,key:o.key||"".concat(a,"fc"),slot:void 0,on:{vnodeMounted:function(_){_.el.__rule__=n.rule,r.onMounted(n,_.el)},"fc.updateValue":function(_){r.$handle.onUpdateValue(n,_)},"fc.el":function(_){n.exportEl=_,_&&((_.$el||_).__rule__=n.rule)}}}];if(n.input){this.vm.props.disabled===!0&&(n.prop.props.disabled=!0);var u=this.getModelField(n),d={callback:function(_){r.onInput(n,_)},modelField:u,value:this.$handle.getFormData(n)};s.push({on:G(Se({},"update:".concat(u),d.callback),n.prop.modelEmit?Se({},n.prop.modelEmit,function(){return r.onEmitInput(n)}):{}),props:Se({},u,d.value)}),n.prop.model=d}return nt(s,n.prop),n.prop},onMounted:function(n,r){n.el=this.vm.refs[n.ref]||r,n.parser.mounted(n),this.$handle.effect(n,"mounted"),this.$handle.targetHook(n,"mounted")},onInput:function(n,r){if(n.prop.modelEmit){this.$handle.onBaseInput(n,r);return}this.$handle.onInput(n,r)},onEmitInput:function(n){this.$handle.setValue(n,n.parser.toValue(n.modelValue,n),n.modelValue)},renderChildren:function(n,r){var i=this;if(!j.trueArray(n))return{};var a=On();return n.map(function(o){if(!!o){if(j.String(o))return a.setSlot(null,o);if(o.__fc__)return i.renderSlot(a,o.__fc__,r);o.type&&l.nextTick(function(){i.$handle.loadChildren(n,r),i.$handle.refresh()})}}),a.getSlots()},defaultRender:function(n,r){var i=n.prop;return i.component?typeof i.component=="string"?this.vNode.make(i.component,i,r):this.vNode.makeComponent(i.component,i,r):this.vNode[n.type]?this.vNode[n.type](i,r):this.vNode[n.originType]?this.vNode[n.originType](i,r):this.vNode.make(Tr(i.type),i,r)},renderRule:function(n,r,i){var a=this;if(!!n){if(j.String(n))return n;var o;if(i)o=n.type;else if(o=n.is,n.type){o=Be(n.type);var s=this.vNode.aliasMap[o];s&&(o=Be(s))}if(!!o){var u=On();j.trueArray(n.children)&&n.children.forEach(function(h){h&&u.setSlot(h==null?void 0:h.slot,function(){return a.renderRule(h)})});var d=G({},n);return delete d.type,delete d.is,this.vNode.make(o,d,u.mergeBag(r).getSlots())}}}})}var da=1;function En(t){J(this,{$handle:t,fc:t.fc,vm:t.vm,$manager:t.$manager,vNode:new t.fc.CreateNode(t.vm),id:da++}),Nr(this,{options:function(){return t.options},sort:function(){return t.sort}}),this.initCache(),this.initRender()}ua(En),fa(En);function ha(t){J(t.prototype,{parseInjectEvent:function(n,r){var i=n.inject||this.options.injectEvent;return this.parseEventLst(n,r,i)},parseEventLst:function(n,r,i,a){var o=this;return Object.keys(r).forEach(function(s){var u=o.parseEvent(n,r[s],i,a);u&&(r[s]=u)}),r},parseEvent:function(n,r,i,a){if(j.Function(r)&&(i!==!1&&!j.Undef(i)||r.__inject))return this.inject(n,r,i);if(!a&&Array.isArray(r)&&r[0]&&(j.String(r[0])||j.Function(r[0])))return this.parseEventLst(n,r,i,!0);if(j.String(r)){var o=ze(r);if(o&&r!==o)return o.__inject?this.parseEvent(n,o,i,!0):o}},parseEmit:function(n){var r=this,i={},a=n.rule,o=a.emitPrefix,s=a.field,u=a.name,d=a.inject,h=a.emit||[];return j.trueArray(h)&&h.forEach(function(_){if(!!_){var y,x=o||s||u;if(j.Object(_)&&(y=_.inject,_=_.name,x=_.prefix||x),x){var S=$t("".concat(x,"-").concat(_)),C=function(){var U,L,Y;r.vm.emitsOptions&&(r.vm.emitsOptions[S]=null);for(var ne=arguments.length,Z=new Array(ne),K=0;K<ne;K++)Z[K]=arguments[K];(U=r.vm).emit.apply(U,[S].concat(Z)),(L=r.vm).emit.apply(L,["emit-event",S].concat(Z)),(Y=r.bus).$emit.apply(Y,[S].concat(Z))};if(C.__emit=!0,!y&&d===!1)i[_]=C;else{var F=y||d||r.options.injectEvent;i[_]=j.Undef(F)?C:r.inject(a,C,F)}}}}),n.computed.on=i,i},getInjectData:function(n,r){var i=n.__fc__&&n.__fc__.$api,a=n.__fc__&&n.__fc__.$handle.vm||this.vm,o=a.props,s=o.option,u=o.rule;return{$f:i||this.api,api:i||this.api,rule:u,self:n.__origin__,option:s,inject:r}},inject:function(n,r,i){if(r.__origin){if(this.watching&&!this.loading)return r;r=r.__origin}var a=this,o=function(){for(var u=a.getInjectData(n,i),d=arguments.length,h=new Array(d),_=0;_<d;_++)h[_]=arguments[_];return u.args=[].concat(h),h.unshift(u),r.apply(this,h)};return o.__origin=r,o.__json=r.__json,o},loadStrVar:function(n,r){var i=this;if(n&&typeof n=="string"&&n.indexOf("{{")>-1&&n.indexOf("}}")>-1){var a=n,o=na(n),s;if(o.forEach(function(u){var d=u.split("||"),h=d[0].trim();if(h){var _=(d[1]||"").trim(),y=r?r(h,_):i.fc.getLoadData(h,_);s=y,n=n.replaceAll("{{".concat(u,"}}"),y==null?"":y)}}),o.length===1&&a==="{{".concat(o[0],"}}"))return s}return n},loadFetchVar:function(n,r){var i=this,a=function(s){return i.loadStrVar(s,r)};return n.action=a(n.action||""),["headers","data","query"].forEach(function(o){if(n[o]){var s={};Object.keys(n[o]).forEach(function(u){s[a(u)]=a(n[o][u])}),n[o]=s}}),n}})}var Yr=["hook:updated","hook:mounted"];function pa(t){J(t.prototype,{usePage:function(){var n=this,r=this.options.page;if(!!r){var i=25,a=ma(this.rules);j.Object(r)&&(r.first&&(i=parseInt(r.first,10)||i),r.limit&&(a=parseInt(r.limit,10)||a)),J(this,{first:i,limit:a,pageEnd:this.rules.length<=i}),this.bus.$on("page-end",function(){return n.vm.emit("page-end",n.api)}),this.pageLoad()}},pageLoad:function(){var n=this,r=function i(){n.pageEnd?(n.bus.$off(Yr,i),n.bus.$emit("page-end")):(n.first+=n.limit,n.pageEnd=n.rules.length<=n.first,n.loadRule(),n.refresh())};this.bus.$on(Yr,r)}})}function ma(t){return t.length<31?31:Math.ceil(t.length/3)}function ga(t){J(t.prototype,{clearNextTick:function(){this.nextTick&&clearTimeout(this.nextTick),this.nextTick=null},bindNextTick:function(n){var r=this;this.clearNextTick(),this.nextTick=setTimeout(function(){n(),r.nextTick=null},10)},render:function(){return++this.loadedId,this.vm.setupState.unique>0?this.$render.render():(this.vm.setupState.unique=1,[])}})}function va(t){Object.defineProperties(t.origin,{__fc__:$n(l.markRaw(t),!0)}),t.rule!==t.origin&&Object.defineProperties(t.rule,{__fc__:$n(l.markRaw(t),!0)})}function Jr(t,e,n){var r=Nt(),i=!!e.field;J(this,{id:r,ref:r,wrapRef:r+"fi",rule:e,origin:e.__origin__||e,name:e.name,pending:{},none:!1,watch:[],linkOn:[],root:[],ctrlRule:[],children:[],parent:null,group:e.subRule?this:null,cacheConfig:null,prop:G({},e),computed:{},payload:{},refRule:{},input:i,el:void 0,exportEl:void 0,defaultValue:i?Ie(n):void 0,field:e.field||void 0}),this.updateKey(),va(this),this.update(t,!0)}J(Jr.prototype,{getParentGroup:function(){for(var e=this.parent;e;){if(e.group)return e;e=e.parent}},loadChildrenPending:function(){var e=this,n=this.rule.children||[];return Array.isArray(n)?n:this.loadPending({key:"children",origin:n,def:[],onLoad:function(i){e.$handle&&e.$handle.loadChildren(i,e)},onUpdate:function(i,a){e.$handle&&(i===a?e.$handle.loadChildren(i,e):e.$handle.updateChildren(e,i,a))},onReload:function(i){e.$handle?e.$handle.updateChildren(e,[],i):delete e.pending.children}})},loadPending:function(e){var n=this,r=e.key,i=e.origin,a=e.def,o=e.onLoad,s=e.onReload,u=e.onUpdate;if(this.pending[r]&&this.pending[r].origin===i)return this.getPending(r,a);delete this.pending[r];var d=i;if(j.Function(i)){var h=de(function(){return i({rule:n.rule,api:n.$api,update:function(y){var x=y||a,S=n.getPending(r,a);n.setPending(r,i,x),u&&u(x,S)},reload:function(){var y=n.getPending(r,a);delete n.pending[r],s&&s(y),n.$api&&n.$api.sync(n.rule)}})});h&&j.Function(h.then)?(h.then(function(_){var y=_||a;n.setPending(r,i,y),o&&o(y),n.$api&&n.$api.sync(n.rule)}).catch(function(_){console.error(_)}),d=a,this.setPending(r,i,d)):(d=h||a,this.setPending(r,i,d),o&&o(d))}return d},getPending:function(e,n){return this.pending[e]&&this.pending[e].value||n},setPending:function(e,n,r){this.pending[e]={origin:n,value:l.reactive(r)}},effectData:function(e){return this.payload[e]||(this.payload[e]={}),this.payload[e]},clearEffectData:function(e){e===void 0?this.payload={}:delete this.payload[e]},updateKey:function(e){this.key=Nt(),e&&this.parent&&this.parent.updateKey(e)},updateType:function(){this.originType=this.rule.type,this.type=Be(this.rule.type),this.trueType=this.$handle.getType(this.originType)},setParser:function(e){this.parser=e,e.init(this)},initProp:function(){var e=this,n,r,i=G({},this.rule);delete i.children,delete i.validate,this.prop=He({},[i].concat(oe(Object.keys(this.payload).map(function(a){return e.payload[a]})),[this.computed])),this.prop.validate=[].concat(oe(((n=this.refRule)===null||n===void 0||(r=n.__$validate)===null||r===void 0?void 0:r.value)||[]),oe(this.prop.validate||[]))},initNone:function(){this.none=!(j.Undef(this.prop.display)||!!this.prop.display)},injectValidate:function(){return this.prop.validate},check:function(e){return this.vm===e.vm},unwatch:function(){this.watch.forEach(function(e){return e()}),this.watch=[],this.refRule={}},unlink:function(){this.linkOn.forEach(function(e){return e()}),this.linkOn=[]},link:function(){this.unlink(),this.$handle.appendLink(this)},watchTo:function(){this.$handle.watchCtx(this)},delete:function(){this.unwatch(),this.unlink(),this.rmCtrl(),this.parent&&this.parent.children.splice(this.parent.children.indexOf(this)>>>0,1),J(this,{deleted:!0,computed:{},parent:null,children:[],cacheConfig:null,none:!1})},rmCtrl:function(){this.ctrlRule.forEach(function(e){return e.__fc__&&e.__fc__.rm()}),this.ctrlRule=[]},rm:function(){var e=this,n=function(){var i=e.root.indexOf(e.origin);i>-1&&(e.root.splice(i,1),e.$handle&&e.$handle.refresh())};if(this.deleted){n();return}this.$handle.noWatch(function(){e.$handle.deferSyncValue(function(){e.rmCtrl(),n(),e.$handle.rmCtx(e),J(e,{root:[]})},e.input)})},update:function(e,n){J(this,{deleted:!1,$handle:e,$render:e.$render,$api:e.api,vm:e.vm,vNode:e.$render.vNode,updated:!1,cacheValue:this.rule.value}),!n&&this.unwatch(),this.watchTo(),this.link(),this.updateType()}});function ya(t){J(t.prototype,{nextRefresh:function(n){var r=this,i=this.loadedId;l.nextTick(function(){i===r.loadedId&&(n?n():r.refresh())})},parseRule:function(n){var r=this,i=Br(n);return Object.defineProperties(i,{__origin__:$n(n,!0)}),_a(i),this.appendValue(i),[i,i.prefix,i.suffix].forEach(function(a){!a||r.loadFn(a,i)}),this.loadCtrl(i),i.update&&(i.update=ze(i.update)),i},loadFn:function(n,r){var i=this;["on","props","deep"].forEach(function(a){n[a]&&i.parseInjectEvent(r,n[a])})},loadCtrl:function(n){n.control&&n.control.forEach(function(r){r.handle&&(r.handle=ze(r.handle))})},syncProp:function(n){var r=this,i=n.rule;j.trueArray(i.sync)&&nt([{on:i.sync.reduce(function(a,o){return a[pe(o)==="object"&&o.event||"update:".concat(o)]=function(s){i.props[pe(o)==="object"&&o.prop||o]=s,r.vm.emit("sync",o,s,i,r.fapi)},a},{})}],n.computed)},loadRule:function(){var n=this;this.cycleLoad=!1,this.loading=!0,this.pageEnd&&this.bus.$emit("load-start"),this.deferSyncValue(function(){if(n._loadRule(n.rules),n.loading=!1,n.cycleLoad&&n.pageEnd)return n.loadRule();n.syncForm(),n.pageEnd&&n.bus.$emit("load-end"),n.vm.setupState.renderRule()})},loadChildren:function(n,r){if(this.cycleLoad=!1,this.loading=!0,this.bus.$emit("load-start"),this._loadRule(n,r),this.loading=!1,this.cycleLoad)return this.loadRule();this.syncForm(),this.bus.$emit("load-end"),this.$render.clearCache(r)},_loadRule:function(n,r){var i=this,a=function u(d){var h=n[d-1];if(!h||!h.__fc__)return d>0?u(d-1):-1;var _=i.sort.indexOf(h.__fc__.id);return _>-1?_:u(d-1)},o=function(d,h){j.trueArray(d)&&i._loadRule(d,h)},s=n.map(function(u,d){if(!(r&&!j.Object(u))&&!(!i.pageEnd&&!r&&d>=i.first)){if(u.__fc__&&u.__fc__.root===n&&i.ctxs[u.__fc__.id])return o(u.__fc__.loadChildrenPending(),u.__fc__),u.__fc__;var h=Br(u),_=function(){return!!(h.field&&i.fieldCtx[h.field]&&i.fieldCtx[h.field][0]!==u.__fc__)};i.fc.targetFormDriver("loadRule",{rule:h,api:i.api},i.fc),i.ruleEffect(h,"init",{repeat:_()}),_()&&i.vm.emit("repeat-field",u,i.api);var y,x=!1,S=!!u.__fc__,C=h.value;if(S){if(y=u.__fc__,C=y.defaultValue,y.deleted){if(Kr(y))return;y.update(i)}else if(!y.check(i)){if(Kr(y))return;n[d]=u=u._clone?u._clone():Fr(bn(u)),y=null,x=!0}}if(y)y.originType!==y.rule.type&&y.updateType(),i.bindParser(y),i.appendValue(y.rule),y.parent&&y.parent!==r&&i.rmSubRuleData(y);else{var F=i.parseRule(u);y=new Jr(i,F,C),i.bindParser(y)}i.parseEmit(y),i.syncProp(y),y.parent=r||null,y.root=n,i.setCtx(y),!x&&!S&&(i.effect(y,"load"),i.targetHook(y,"load")),i.effect(y,"created");var q=y.loadChildrenPending();if(y.parser.loadChildren===!1||o(q,y),!r){var U=a(d);U>-1||!d?i.sort.splice(U+1,0,y.id):i.sort.push(y.id)}var L=y.rule;return y.updated||(y.updated=!0,j.Function(L.update)&&i.bus.$once("load-end",function(){i.refreshUpdate(y,L.value,"init")}),i.effect(y,"loaded")),i.refreshControl(y)&&(i.cycleLoad=!0),y}}).filter(function(u){return!!u});r&&(r.children=s)},refreshControl:function(n){return n.input&&n.rule.control&&this.useCtrl(n)},useCtrl:function(n){var r=this,i=ba(n),a=[],o=this.api;if(!i.length)return!1;for(var s=function(x){var S=i[x],C=S.handle||function(q){return(it[S.condition||"=="]||it["=="])(q,S.value)};if(!j.trueArray(S.rule))return"continue";var F=G(G({},S),{},{valid:de(function(){return C(n.rule.value,o)}),ctrl:wa(n,S.rule),isHidden:j.String(S.rule[0])});if(F.valid&&F.ctrl||!F.valid&&!F.ctrl&&!F.isHidden)return"continue";a.push(F)},u=0;u<i.length;u++)var d=s(u);if(!a.length)return!1;var h=[],_=!1;return this.deferSyncValue(function(){a.reverse().forEach(function(y){var x=y.isHidden,S=y.valid,C=y.rule,F=y.prepend,q=y.append,U=y.child,L=y.ctrl,Y=y.method;if(x){S?n.ctrlRule.push({__ctrl:!0,children:C,valid:S}):L&&n.ctrlRule.splice(n.ctrlRule.indexOf(L)>>>0,1),h[S?"push":"unshift"](function(){Y==="disabled"||Y==="enabled"?r.api.disabled(!S,C):Y==="display"?r.api.display(S,C):Y==="required"?(C.forEach(function(K){r.api.setEffect(K,"required",S)}),S||r.api.clearValidateState(C)):r.api.hidden(!S,C)});return}if(S){_=!0;var ne={type:"fragment",native:!0,__ctrl:!0,children:C};n.ctrlRule.push(ne),r.bus.$once("load-start",function(){F?o.prepend(ne,F,U):q||U?o.append(ne,q||n.id,U):n.root.splice(n.root.indexOf(n.origin)+1,0,ne)})}else{n.ctrlRule.splice(n.ctrlRule.indexOf(L),1);var Z=rt(L);Z&&Z.rm()}})}),h.length&&(this.loading?h.length&&this.bus.$once("load-end",function(){h.forEach(function(y){return y()})}):h.length&&l.nextTick(function(){h.forEach(function(y){return y()})})),this.vm.emit("control",n.origin,this.api),this.effect(n,"control"),_},reloadRule:function(n){return this._reloadRule(n)},_reloadRule:function(n){var r=this;n||(n=this.rules);var i=G({},this.ctxs);this.clearNextTick(),this.initData(n),this.fc.rules=n,this.deferSyncValue(function(){r.bus.$once("load-end",function(){Object.keys(i).filter(function(a){return r.ctxs[a]===void 0}).forEach(function(a){return r.rmCtx(i[a])}),r.$render.clearCacheAll()}),r.reloading=!0,r.loadRule(),r.reloading=!1,r.refresh(),r.bus.$emit("reloading",r.api)}),this.bus.$off("next-tick",this.nextReload),this.bus.$once("next-tick",this.nextReload),this.bus.$emit("update",this.api)},refresh:function(){this.vm.setupState.refresh()}})}function _a(t){var e=qr();return Object.keys(e).forEach(function(n){me(t,n)||(t[n]=e[n])}),t}function ba(t){var e=t.rule.control||[];return j.Object(e)?[e]:e}function wa(t,e){for(var n=0;n<t.ctrlRule.length;n++){var r=t.ctrlRule[n];if(r.children===e)return r}}function Kr(t){return!!t.rule.__ctrl}function $a(t){J(t.prototype,{setValue:function(n,r,i,a){var o=this;n.deleted||(n.rule.value=r,this.changeStatus=!0,this.nextRefresh(),this.$render.clearCache(n),this.setFormData(n,i),this.syncValue(),this.valueChange(n,r),this.vm.emit("change",n.field,r,n.origin,this.api,a||!1),this.effect(n,"value"),this.targetHook(n,"value",{value:r}),this.emitEvent("change",n.field,r,{rule:n.origin,api:this.api,setFlag:a||!1}),a&&l.nextTick(function(){l.nextTick(function(){l.nextTick(function(){o.api.clearValidateState(n.id)})})}))},onInput:function(n,r){var i;n.input&&(this.isQuote(n,i=n.parser.toValue(r,n))||this.isChange(n,r))&&this.setValue(n,i,r)},onUpdateValue:function(n,r){var i=this;this.deferSyncValue(function(){var a=n.getParentGroup(),o=a?i.subRuleData[a.id]:null,s={};Object.keys(r||{}).forEach(function(u){o&&me(o,u)?s[u]=r[u]:me(i.api.form,u)?i.api.form[u]=r[u]:i.api.top!==i.api&&me(i.api.top.form,u)&&(i.api.top.form[u]=r[u])}),Object.keys(s).length&&i.api.setChildrenFormData(a.rule,s)})},onBaseInput:function(n,r){this.setFormData(n,r),n.modelValue=r,this.nextRefresh(),this.$render.clearCache(n)},setFormData:function(n,r){n.modelValue=r;var i=n.getParentGroup();i&&(this.subRuleData[i.id]||(this.subRuleData[i.id]={}),this.subRuleData[i.id][n.field]=n.rule.value),ke(this.formData,n.id,r)},rmSubRuleData:function(n){var r=n.getParentGroup();r&&this.subRuleData[r.id]&&delete this.subRuleData[r.id][n.field]},getFormData:function(n){return this.formData[n.id]},syncForm:function(){var n=this,r=l.reactive({}),i=this.fields(),a=[];this.options.appendValue!==!1&&Object.keys(this.appendData).reduce(function(o,s){return i.indexOf(s)===-1&&(o[s]=l.toRef(n.appendData,s)),o},r),i.reduce(function(o,s){var u=(n.fieldCtx[s]||[]).filter(function(d){return!n.isIgnore(d.rule)})[0]||n.fieldCtx[s][0];return n.isIgnore(u.rule)&&a.push(s),o[s]=l.toRef(u.rule,"value"),o},r),this.form=r,this.ignoreFields=a,this.syncValue()},isIgnore:function(n){return n.ignore===!0||n.ignore==="hidden"&&n.hidden||this.options.ignoreHiddenFields&&n.hidden},appendValue:function(n){(!n.field||!me(this.appendData,n.field))&&!this.options.forceCoverValue||(n.value=this.appendData[n.field],delete this.appendData[n.field])},addSubForm:function(n,r){this.subForm[n.id]=r},deferSyncValue:function(n,r){this.deferSyncFn||(this.deferSyncFn=n),this.deferSyncFn.sync||(this.deferSyncFn.sync=r),de(n),this.deferSyncFn===n&&(this.deferSyncFn=null,n.sync&&this.syncForm())},syncValue:function(){var n=this;if(this.deferSyncFn)return this.deferSyncFn.sync=!0;var r={};Object.keys(this.form).forEach(function(i){n.ignoreFields.indexOf(i)===-1&&(r[i]=n.form[i])}),this.vm.setupState.updateValue(r)},isChange:function(n,r){return JSON.stringify(this.getFormData(n),Qr)!==JSON.stringify(r,Qr)},isQuote:function(n,r){return(j.Object(r)||Array.isArray(r))&&r===n.rule.value},refreshUpdate:function(n,r,i,a){var o=this;if(j.Function(n.rule.update)){var s=de(function(){return n.rule.update(r,n.origin,o.api,{origin:i||"change",linkField:a})});if(s===void 0)return;n.rule.hidden=s===!0}},valueChange:function(n,r){this.refreshRule(n,r),this.bus.$emit("change-"+n.field,r)},refreshRule:function(n,r,i,a){this.refreshControl(n)&&(this.$render.clearCacheAll(),this.loadRule(),this.bus.$emit("update",this.api),this.refresh()),this.refreshUpdate(n,r,i,a)},appendLink:function(n){var r=this,i=n.rule.link;j.trueArray(i)&&i.forEach(function(a){var o=function(){return r.refreshRule(n,n.rule.value,"link",a)};r.bus.$on("change-"+a,o),n.linkOn.push(function(){return r.bus.$off("change-"+a,o)})})},fields:function(){return Object.keys(this.fieldCtx)}})}function Qr(t,e){return typeof e=="function"?""+e:e}var Lt={init:function(e){},toFormValue:function(e,n){return e},toValue:function(e,n){return e},mounted:function(e){},render:function(e,n){return n.$handle.fc.renderDriver&&n.$handle.fc.renderDriver.defaultRender?n.$handle.fc.renderDriver.defaultRender(n,e):n.$render.defaultRender(n,e)},preview:function(e,n){return n.$handle.fc.renderDriver&&n.$handle.fc.renderDriver.defaultPreview?n.$handle.fc.renderDriver.defaultPreview(n,e):this.render(e,n)},mergeProp:function(e){}},Oa=["field","value","vm","template","name","config","control","inject","sync","payload","optionsTo","update","slotUpdate","computed","component","cache"],Zr=Symbol("oldValue");function Sa(t){J(t.prototype,{getCtx:function(n){return this.getFieldCtx(n)||this.getNameCtx(n)[0]||this.ctxs[n]},getCtxs:function(n){return this.fieldCtx[n]||this.nameCtx[n]||(this.ctxs[n]?[this.ctxs[n]]:[])},setIdCtx:function(n,r,i){var a="".concat(i,"Ctx");this[a][r]?this[a][r].push(n):this[a][r]=[n]},rmIdCtx:function(n,r,i){var a="".concat(i,"Ctx"),o=this[a][r];if(!o)return!1;var s=o.splice(o.indexOf(n)>>>0,1).length>0;return o.length||delete this[a][r],s},getFieldCtx:function(n){return(this.fieldCtx[n]||[])[0]},getNameCtx:function(n){return this.nameCtx[n]||[]},setCtx:function(n){var r=n.id,i=n.field,a=n.name,o=n.rule;this.ctxs[r]=n,a&&this.setIdCtx(n,a,"name"),n.input&&(this.setIdCtx(n,i,"field"),this.setFormData(n,n.parser.toFormValue(o.value,n)),this.isMounted&&!this.reloading&&this.vm.emit("change",n.field,o.value,n.origin,this.api))},getParser:function(n){var r=this.fc.parsers,i=this.fc.renderDriver;if(i){var a=i.parsers||{},o=a[n.originType]||a[Be(n.type)]||a[n.trueType];if(o)return o}return r[n.originType]||r[Be(n.type)]||r[n.trueType]||Lt},bindParser:function(n){n.setParser(this.getParser(n))},getType:function(n){var r=this.fc.CreateNode.aliasMap,i=r[n]||r[Be(n)]||n;return Be(i)},noWatch:function(n){this.noWatchFn||(this.noWatchFn=n),de(n),this.noWatchFn===n&&(this.noWatchFn=null)},watchCtx:function(n){var r=this,i=Vr();if(i.filter(function(o){return o[0]!=="_"&&o[0]!=="$"&&Oa.indexOf(o)===-1}).forEach(function(o){var s=l.toRef(n.rule,o),u=o==="children";n.refRule[o]=s,n.watch.push(l.watch(u?function(){return j.Function(s.value)?s.value:oe(s.value||[])}:function(){return s.value},function(d,h){var _=s.value;if(!r.isBreakWatch()){if(u&&n.parser.loadChildren===!1){r.$render.clearCache(n),r.nextRefresh();return}if(r.watching=!0,l.nextTick(function(){r.targetHook(n,"watch",{key:o,oldValue:h,newValue:_})}),o==="hidden"&&Boolean(_)!==Boolean(h)&&(r.$render.clearCacheAll(),l.nextTick(function(){r.targetHook(n,"hidden",{value:_})})),o==="ignore"&&n.input||o==="hidden"&&n.input&&(n.rule.ignore==="hidden"||r.options.ignoreHiddenFields))r.syncForm();else if(o==="link"){n.link();return}else["props","on","deep"].indexOf(o)>-1?(r.parseInjectEvent(n.rule,_||{}),o==="props"&&n.input&&r.setFormData(n,n.parser.toFormValue(n.rule.value,n))):o==="emit"?r.parseEmit(n):["prefix","suffix"].indexOf(o)>-1?_&&r.loadFn(_,n.rule):o==="type"?(n.updateType(),r.bindParser(n)):u&&(j.Function(h)&&(h=n.getPending("children",[])),j.Function(_)&&(_=n.loadChildrenPending()),r.updateChildren(n,_,h));r.$render.clearCache(n),r.refresh(),r.watching=!1}},{deep:!u,sync:u}))}),n.refRule.__$title=l.computed(function(){var o=(pe(n.rule.title)==="object"?n.rule.title.title:n.rule.title)||"";if(o){var s=o.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);s&&(o=r.api.t(s[1]))}return o}),n.refRule.__$info=l.computed(function(){var o=(pe(n.rule.info)==="object"?n.rule.info.info:n.rule.info)||"";if(o){var s=o.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);s&&(o=r.api.t(s[1]))}return o}),n.refRule.__$validate=l.computed(function(){return Ze(n.rule.validate).map(function(o){var s=G({},o);if(s.message){var u=s.message.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);u&&(s.message=r.api.t(u[1],{title:n.refRule.__$title.value}))}if(j.Function(s.validator)){var d=n;return s.validator=function(){for(var h,_=arguments.length,y=new Array(_),x=0;x<_;x++)y[x]=arguments[x];return(h=o.validator).call.apply(h,[{that:this,id:d.id,field:d.field,rule:d.rule,api:d.$handle.api}].concat(y))},s}return s})}),n.input){var a=l.toRef(n.rule,"value");n.watch.push(l.watch(function(){return a.value},function(){var o=n.parser.toFormValue(a.value,n);r.isChange(n,o)&&r.setValue(n,a.value,o,!0)}))}this.bus.$once("load-end",function(){var o=n.rule.computed;!o||(pe(o)!=="object"&&(o={value:o}),Object.keys(o).forEach(function(s){var u=void 0,d=l.computed(function(){var _=o[s];if(!!_){var y=r.compute(n,_);return _.linkage&&y===Zr?u:y}}),h=function(y){s==="value"?r.onInput(n,y):s[0]==="$"?r.api.setEffect(n.id,s,y):Je(n.rule,s,y)};(s==="value"?[void 0,null,""].indexOf(n.rule.value)>-1:d.value!==Ve(n.rule,s))&&h(d.value),n.watch.push(l.watch(d,function(_){u=_,setTimeout(function(){h(_)})}))}))}),this.watchEffect(n)},compute:function(n,r){var i=this,a;if(pe(r)==="object"){var o=n.getParentGroup(),s=function h(_){if(_=Array.isArray(_)?{mode:"AND",group:_}:_,!j.trueArray(_.group))return!0;for(var y=_.mode==="OR",x=!0,S=function(U){var L=_.group[U],Y=void 0,ne=null;if(L.variable)ne=JSON.stringify(i.fc.getLoadData(L.variable)||"");else if(L.field)ne=zr(L.field||"");else return{v:!0};var Z=L.compare;if(Z&&(Z=zr(Z||"")),L.mode?Y=h(L):it[L.condition]?j.Function(L.handler)?Y=de(function(){return L.handler(i.api,n.rule)}):Y=new Function("$condition","$val","$form","$group","$rule","with($form){with(this){with($group){ return $condition['".concat(L.condition,"'](").concat(ne,", ").concat(Z||"$val","); }}}")).call(i.api.form,it,L.value,i.api.top.form,o?i.subRuleData[o.id]||{}:{},n.rule):Y=!1,y&&Y)return{v:!0};y||(x=x&&Y)},C=0;C<_.group.length;C++){var F=S(C);if(pe(F)==="object")return F.v}return y?!1:x},u=s(r);return u=r.invert===!0?!u:u,r.linkage?u?de(function(){return i.computeValue(r.linkage,n,o)},void 0):Zr:u}else if(j.Function(r))a=function(){return r(i.api.form,i.api)};else{var d=n.getParentGroup();a=function(){return i.computeValue(r,n,d)}}return de(a,void 0)},computeValue:function(n,r,i){var a=this,o=Object.keys(this.fc.formulas).reduce(function(s,u){return s[u]=function(){for(var d,h=arguments.length,_=new Array(h),y=0;y<h;y++)_[y]=arguments[y];return(d=a.fc.formulas[u]).call.apply(d,[{that:this,rule:r.rule,api:a.api,fc:a.fc}].concat(_))},s},{});return new Function("$formulas","$form","$group","$rule","$api","with($form){with(this){with($group){with($formulas){ return ".concat(n," }}}}")).call(this.api.form,o,this.api.top.form,i?this.subRuleData[i.id]||{}:{},r.rule,this.api)},updateChildren:function(n,r,i){var a=this;this.deferSyncValue(function(){i&&i.forEach(function(o){(r||[]).indexOf(o)===-1&&o&&!j.String(o)&&o.__fc__&&o.__fc__.parent===n&&a.rmCtx(o.__fc__)}),j.trueArray(r)&&(a.loadChildren(r,n),a.bus.$emit("update",a.api))})},rmSub:function(n){var r=this;j.trueArray(n)&&n.forEach(function(i){i&&i.__fc__&&r.rmCtx(i.__fc__)})},rmCtx:function(n){var r=this;if(!n.deleted){var i=n.id,a=n.field,o=n.input,s=n.name;et(this.ctxs,i),et(this.formData,i),et(this.subForm,i),et(this.vm.setupState.ctxInject,i);var u=n.getParentGroup();u&&this.subRuleData[u.id]&&et(this.subRuleData[u.id],a),n.group&&et(this.subRuleData,i),o&&this.rmIdCtx(n,a,"field"),s&&this.rmIdCtx(n,s,"name"),o&&!me(this.fieldCtx,a)&&et(this.form,a),this.deferSyncValue(function(){if(!r.reloading){if(n.parser.loadChildren!==!1){var h=n.getPending("children",n.rule.children);j.trueArray(h)&&h.forEach(function(_){return _.__fc__&&r.rmCtx(_.__fc__)})}n.root===r.rules&&r.vm.setupState.renderRule()}},o);var d=this.sort.indexOf(i);return d>-1&&this.sort.splice(d,1),this.$render.clearCache(n),n.delete(),this.effect(n,"deleted"),this.targetHook(n,"deleted"),o&&!this.fieldCtx[a]&&this.vm.emit("remove-field",a,n.rule,this.api),n.rule.__ctrl||this.vm.emit("remove-rule",n.rule,this.api),n}}})}function Ea(t){J(t.prototype,{mounted:function(){var n=this,r=function(){n.isMounted=!0,n.lifecycle("mounted")};this.pageEnd?r():this.bus.$once("page-end",r)},lifecycle:function(n){this.fc.targetFormDriver(n,this.api,this.fc),this.vm.emit(n,this.api),this.emitEvent(n,this.api)},emitEvent:function(n){for(var r,i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];var s=this.options[n]||this.options[Be("on-"+n)];if(s){var u=ze(s);j.Function(u)&&de(function(){return u.apply(void 0,a)})}(r=this.bus).$emit.apply(r,[n].concat(a))},targetHook:function(n,r,i){var a,o,s=this,u=(a=n.prop)===null||a===void 0||(o=a.hook)===null||o===void 0?void 0:o[r];u&&(u=Array.isArray(u)?u:[u],u.forEach(function(d){de(function(){return d(G(G({},i||{}),{},{rule:n.rule,api:s.api}))})}))}})}function Ca(t){J(t.prototype,{useProvider:function(){var n=this,r=this.fc.providers;Object.keys(r).forEach(function(i){var a=r[i];j.Function(a)&&(a=a(n.fc)),a._c=xa(a),n.onEffect(a),n.providers[i]=a})},onEffect:function(n){var r=this,i=[];(n._c||["*"]).forEach(function(a){var o=a==="*"?"*":r.getType(a);i.indexOf(o)>-1||(i.push(o),r.bus.$on("p:".concat(n.name,":").concat(o,":").concat(n.input?1:0),function(s,u){n[s]&&n[s].apply(n,oe(u))}))}),n._used=i},watchEffect:function(n){var r=this,i={required:function(){var o,s;return(me(n.rule,"$required")?n.rule.$required:(o=n.rule)===null||o===void 0||(s=o.effect)===null||s===void 0?void 0:s.required)||!1}};Object.keys(n.rule.effect||{}).forEach(function(a){i[a]=function(){return n.rule.effect[a]}}),Object.keys(n.rule).forEach(function(a){a[0]==="$"&&(i[a.substr(1)]=function(){return n.rule[a]})}),Object.keys(i).forEach(function(a){n.watch.push(l.watch(i[a],function(o){r.effect(n,"watch",Se({},a,o))},{deep:!0}))})},ruleEffect:function(n,r,i){this.emitEffect({rule:n,input:!!n.field,type:this.getType(n.type)},r,i)},effect:function(n,r,i){this.emitEffect({rule:n.rule,input:n.input,type:n.trueType,ctx:n,custom:i},r)},getEffect:function(n,r){if(me(n,"$"+r))return n["$"+r];if(me(n,"effect")&&me(n.effect,r))return n.effect[r]},emitEffect:function(n,r,i){var a=this,o=n.ctx,s=n.rule,u=n.input,d=n.type,h=n.custom;if(!(!d||["fcFragment","fragment"].indexOf(d)>-1)){var _=h||Object.keys(s).reduce(function(y,x){return x[0]==="$"&&(y[x.substr(1)]=s[x]),y},G({},s.effect||{}));Object.keys(_).forEach(function(y){var x=a.providers[y];if(!(!x||x.input&&!u)){var S;if(!x._c)S="*";else if(x._used.indexOf(d)>-1)S=d;else return;var C=G({value:_[y],getValue:function(){return a.getEffect(s,y)}},i||{});o&&(C.getProp=function(){return o.effectData(y)},C.clearProp=function(){return o.clearEffectData(y)},C.mergeProp=function(F){return He(C.getProp(),[F])},C.id=o.id),a.bus.$emit("p:".concat(y,":").concat(S,":").concat(x.input?1:0),r,[C,s,a.api])}})}}})}function Aa(t){return t.filter(function(e,n,r){return r.indexOf(e,0)===n})}function xa(t){var e=t.components;if(Array.isArray(e)){var n=Aa(e.filter(function(r){return r!=="*"}));return n.length?n:!1}else return j.String(e)?[e]:!1}function qe(t){var e=this;Nr(this,{options:function(){return t.options.value||{}},bus:function(){return t.bus},preview:function(){return t.vm.props.preview!=null?t.vm.props.preview:t.options.value.preview||!1}}),J(this,{fc:t,vm:t.vm,watching:!1,loading:!1,reloading:!1,noWatchFn:null,deferSyncFn:null,isMounted:!1,formData:l.reactive({}),subRuleData:l.reactive({}),subForm:{},form:l.reactive({}),appendData:{},ignoreFields:[],providers:{},cycleLoad:null,loadedId:1,nextTick:null,changeStatus:!1,pageEnd:!0,nextReload:function(){e.lifecycle("reload")}}),this.initData(t.rules),this.$manager=new t.manager(this),this.$render=new En(this),this.api=t.extendApiFn.reduce(function(n,r){var i=de(function(){return r(n,e)});return i&&i!==n&&J(n,i),n},sa(this))}J(qe.prototype,{initData:function(e){J(this,{ctxs:{},fieldCtx:{},nameCtx:{},sort:[],rules:e})},init:function(){this.updateAppendData(),this.useProvider(),this.usePage(),this.loadRule(),this.$manager.__init(),this.lifecycle("created")},updateAppendData:function(){this.appendData=G(G(G({},this.options.formData||{}),this.fc.vm.props.modelValue||{}),this.appendData)},isBreakWatch:function(){return this.loading||this.noWatchFn||this.reloading},beforeFetch:function(e){var n=this;return new Promise(function(r){var i=n.options.beforeFetch&&de(function(){return n.options.beforeFetch(e,{api:n.api})});i&&j.Function(i.then)?i.then(r):r()})}}),ha(qe),pa(qe),ga(qe),ya(qe),$a(qe),Sa(qe),Ea(qe),Ca(qe);var Da="fcFragment",Cn=l.defineComponent({name:Da,inheritAttrs:!1,props:["vnode"],render:function(){return this.vnode}});function Pa(t){return Object.keys(t).map(function(e){var n=t[e],r=l.resolveDirective(e);if(!!r)return[r,n.value,n.arg,n.modifiers]}).filter(function(e){return!!e})}function ei(t,e){var n=t.directives;return n?(Array.isArray(n)||(n=[n]),l.withDirectives(e,n.reduce(function(r,i){return r.concat(Pa(i))},[]))):e}function Ra(){var t={};function e(){}return J(e.prototype,{make:function(r,i,a){return ei(i,this.h(r,Lr(i),a))},makeComponent:function(r,i,a){try{return ei(i,l.createVNode(r,Lr(i),a))}catch(o){return console.error(o),l.createVNode("")}},h:function(r,i,a){var o=l.getCurrentInstance().appContext.config.isNativeTag(r);o&&delete i.formCreateInject;try{return l.createVNode(o?r:l.resolveComponent(r),i,a)}catch(s){return console.error(s),l.createVNode("")}},aliasMap:t}),J(e,{aliasMap:t,alias:function(r,i){t[r]=i},use:function(r){Object.keys(r).forEach(function(i){var a=$t(i),o=la(i).toLocaleLowerCase(),s=r[i];[i,a,o].forEach(function(u){e.alias(i,s),e.prototype[u]=function(d,h){return this.make(s,d,h)}})})}}),e}function ka(t){var e=function(n){po(i,n);var r=yo(i);function i(){return ho(this,i),r.apply(this,arguments)}return i}(ti);return Object.assign(e.prototype,t),e}function ti(t){J(this,{$handle:t,vm:t.vm,options:{},ref:"fcForm",mergeOptionsRule:{normal:["form","row","info","submitBtn","resetBtn"]}}),this.updateKey(),this.init()}J(ti.prototype,{__init:function(){var e=this;this.$render=this.$handle.$render,this.$r=function(){var n;return(n=e.$render).renderRule.apply(n,arguments)}},updateKey:function(){this.key=Nt()},init:function(){},update:function(){},beforeRender:function(){},form:function(){return this.vm.refs[this.ref]},getSlot:function(e){var n=function r(i){if(i){var a=i.slots[e];return a||r(i.setupState.parent)}};return n(this.vm)},mergeOptions:function(e,n){var r=this;return nt(e.map(function(i){return r.tidyOptions(i)}),n,this.mergeOptionsRule)},updateOptions:function(e){this.$handle.fc.targetFormDriver("updateOptions",e,{handle:this.$handle,api:this.$handle.api}),this.options=this.mergeOptions([e],this.getDefaultOptions()),this.update()},tidyOptions:function(e){return e},tidyRule:function(e){},mergeProp:function(e){},getDefaultOptions:function(){return{}},render:function(e){}});var Va=function(e){var n={name:"loadData",_fn:[],loaded:function(i,a,o){this.deleted(i);var s=Ze(i.getValue()),u=[];s.forEach(function(d){if(d&&(d.attr||d.template)){var h=function(S){var C;d.template?C=e.$handle.loadStrVar(d.template,S):d.handler&&j.Function(d.handler)?C=d.handler(S,a,o):C=S(d.attr,d.default),d.copy!==!1&&(C=Ie(C));var F=d.modify?a:i.getProp();d.to==="child"?F.children?F.children[0]=C:F.children=[C]:Je(F,d.to||"options",C),o.sync(a)},_=function(S){return h(S)},y=e.watchLoadData(_);h=tt(h,d.wait||300),d.watch!==!1?u.push(y):y()}}),this._fn[i.id]=u},deleted:function(i){this._fn[i.id]&&(this._fn[i.id].forEach(function(a){a()}),delete this._fn[i.id]),i.clearProp()}};return n.watch=n.mounted,n},Ma=function(e){var n={name:"t",_fn:[],loaded:function(i,a,o){this.deleted(i);var s=i.getValue()||{},u=[];Object.keys(s).forEach(function(d){var h=s[d];if(h){var _=pe(h)==="object",y=function(F){var q=e.t(_?h.attr:h,_?h.params:null,F),U=_&&h.modify?a:i.getProp();d==="child"?U.children?U.children[0]=q:U.children=[q]:Je(U,d,q),o.sync(a)},x=function(F){return y(F)},S=e.watchLoadData(x);y=tt(y,h.wait||300),h.watch!==!1?u.push(S):S()}}),this._fn[i.id]=u},deleted:function(i){this._fn[i.id]&&(this._fn[i.id].forEach(function(a){a()}),delete this._fn[i.id]),i.clearProp()}};return n.watch=n.loaded,n},An={name:"componentValidate",load:function(e,n,r){var i=e.getValue();if(!i||i.method===!1)e.clearProp(),r.clearValidateState([n.field]);else{j.Object(i)||(i={method:i});var a=i.method;delete i.method,e.getProp().validate=[G(G({},i),{},{validator:function(){var s=rt(n);if(s){for(var u=arguments.length,d=new Array(u),h=0;h<u;h++)d[h]=arguments[h];return r.exec.apply(r,[s.id,j.String(a)?a:"formCreateValidate"].concat(d,[{attr:e,rule:n,api:r}]))}}})]}},watch:function(){An.load.apply(An,arguments)}},Ta=function(e){function n(a){return j.String(a)&&(a={action:a,to:"options"}),a}function r(a,o,s){var u=a.value;i.deleted(a),j.Function(u)&&(u=u(o,s)),u=n(u);var d=function(S){S===void 0?a.clearProp():Je(a.getProp(),u.to||"options",S),S!=null&&u&&u.key&&e.$handle.options.globalData[u.key]&&e.fetchCache.set(e.$handle.options.globalData[u.key],{status:!0,data:S}),s.sync(o)};if(!u||!u.action&&!u.key){d(void 0);return}if(u=Ie(u),u.to||(u.to="options"),u.key){var h=e.$handle.options.globalData[u.key];if(!h){d(void 0);return}if(h.type==="static"){d(h.data);return}else u=G(G({},u),h)}var _=u.onError,y=function(){if(!a.getValue())return a.clearProp(),s.sync(o),!0};i._fn[a.id]=e.watchLoadData(tt(function(x,S){if(S&&u.watch===!1)return i._fn[a.id]();var C=e.$handle.loadFetchVar(Ie(u),x),F=G(G({headers:{}},C),{},{onSuccess:function(U,L){if(!y()){var Y=function(K){return L?K:me(K,"data")?K.data:K},ne=ze(C.parse);j.Function(ne)?Y=ne:ne&&j.String(ne)&&(Y=function(K){return Ve(K,ne)}),d(Y(U,o,s)),s.sync(o)}},onError:function(U){d(void 0),!y()&&(_||function(L){return vn(L.message||"fetch fail "+C.action)})(U,o,s)}});e.$handle.beforeFetch(F,{rule:o,api:s}).then(function(){if(j.Function(C.action)){C.action(o,s).then(function(q){F.onSuccess(q,!0)}).catch(function(q){F.onError(q)});return}de(function(){return e.create.fetch(F,{inject:a,rule:o,api:s})})})},u.wait||600))}var i={name:"fetch",_fn:[],loaded:function(){r.apply(void 0,arguments)},watch:function(){r.apply(void 0,arguments)},deleted:function(o){this._fn[o.id]&&(this._fn[o.id](),delete this._fn[o.id]),o.clearProp()}};return i},Fa={fetch:Ta,loadData:Va,t:Ma,componentValidate:An};function ja(t){t=t||new Map;var e={$on:function(r,i){var a=t.get(r),o=a&&a.push(i);o||t.set(r,[i])},$once:function(r,i){i._once=!0,e.$on(r,i)},$off:function(r,i){var a=t.get(r);a&&a.splice(a.indexOf(i)>>>0,1)},$emit:function(r){for(var i=arguments.length,a=new Array(i>1?i-1:0),o=1;o<i;o++)a[o-1]=arguments[o];(t.get(r)||[]).slice().map(function(s){s._once&&(e.$off(r,s),delete s._once),s.apply(void 0,a)}),(t.get("*")||[]).slice().map(function(s){s(r,a)})}};return e}var Ia="html",Ba={name:Ia,loadChildren:!1,render:function(e,n){return n.prop.props.innerHTML=e.default(),n.vNode.make(n.prop.props.tag||"div",n.prop)},renderChildren:function(e){return{default:function(){return e.filter(function(r){return j.String(r)}).join("")}}}};function Na(t){t=t+"=";for(var e=decodeURIComponent(document.cookie),n=e.split(";"),r=0;r<n.length;r++){for(var i=n[r];i.charAt(0)===" ";)i=i.substring(1);if(i.indexOf(t)===0){i=i.substring(t.length,i.length);try{return JSON.parse(i)}catch{return i}}}return null}function La(t){var e=localStorage.getItem(t);if(e)try{return JSON.parse(e)}catch{return e}return null}function Ua(t){var e=sessionStorage.getItem(t);if(e)try{return JSON.parse(e)}catch{return e}return null}function xn(t,e){if(!e)return null;var n=e.split("."),r=t(n.shift());return n.length?r==null?null:Ve(r,n):r}function za(t){return xn(Na,t)}function qa(t){return xn(La,t)}function Ga(t){return xn(Ua,t)}function Ha(t,e){var n;return arguments.length===2?(n=arguments[1],e=n[t]):n=arguments[2],{id:e,prop:n}}function Dn(){return Ha.apply(void 0,["name"].concat(Array.prototype.slice.call(arguments)))}function Wa(t){var e=t.key||[],n=t.array||[],r=t.normal||[];gn.push.apply(gn,oe(e)),Ft.push.apply(Ft,oe(n)),jt.push.apply(jt,oe(r)),Gr([].concat(oe(e),oe(n),oe(r)))}var Xa=1,Me={},Ut=Symbol("defValue");function ni(t){var e=Se({},Cn.name,Cn),n={},r={},i={},a={},o=[],s=[],u=[t.extendApi],d=G({},Fa),h=oa(),_={global:{}},y=l.reactive({}),x=Ra(),S={},C=t.isMobile===!0,F={};Wa(t.attrs||{});function q(D){var A=Me[D];if(Array.isArray(A))return A.map(function(R){return R.api()});if(A)return A.api()}function U(D){o.push(D)}function L(){var D=Dn.apply(void 0,arguments);D.id&&D.prop&&(r[D.id]=D.prop)}function Y(){var D=Dn.apply(void 0,arguments);D.id&&D.prop&&(d[D.id]=j.Function(D.prop)?D.prop:G(G({},D.prop),{},{name:D.id}))}function ne(D){x.use(D)}function Z(){var D=Dn.apply(void 0,arguments);if(!D.id||!D.prop)return Lt;var A=Be(D.id),R=D.prop,g=R.merge===!0?n[A]:void 0;n[A]=It(R,g||Lt),h[A]=Bt(A),R.maker&&J(h,R.maker)}function K(D,A){var R;if(j.String(D)){if(R=D,A===void 0)return e[R]}else R=D.displayName||D.name,A=D;if(!(!R||!A)){var g=Be(R);e[R]=A,e[g]=A,delete x.aliasMap[R],delete x.aliasMap[g],delete n[R],delete n[g],A.formCreateParser&&Z(R,A.formCreateParser)}}function X(){return Qo(M,e,r)}function ce(D,A){var R=X();return l.createApp({data:function(){return l.reactive({rule:D,option:A})},render:function(){return l.h(R,G({ref:"fc"},this.$data))}})}function se(){return Cn}function re(D,A){return j.Function(D.install)?D.install($e,A):j.Function(D)&&D($e,A),this}function $e(D,A){var R=ce(D,A||{});o.forEach(function(w){de(function(){return w($e,R)})});var g=document.createElement("div");((A==null?void 0:A.el)||document.body).appendChild(g);var $=R.mount(g);return $.$refs.fc.fapi}It($e,F);function Ne(D){var A=G({},t);return D?A.inherit={components:e,parsers:n,directives:r,modelFields:i,providers:d,useApps:o,maker:h,formulas:S,loadData:y}:delete A.inherit,ni(A)}function k(D,A){i[D]=A}function O(D,A){S[D]=A}function b(D,A){var R=a[D]||{},g=R.parsers||{};A.parsers&&Object.keys(A.parsers).forEach(function($){g[$]=It(A.parsers[$],Lt)}),A.name=D,a[D]=G(G(G({},R),A),{},{parsers:g})}function f(D){D&&Object.keys(Me).forEach(function(A){var R=Array.isArray(Me[A])?Me[A]:[Me[A]];R.forEach(function(g){g.bus.$emit("$loadData."+D)})})}function c(D,A){Je(y,D,A),f(D)}function m(D,A){var R=function(){for(var $=arguments.length,w=new Array($),P=0;P<$;P++)w[P]=arguments[P];return de(function(){return A.apply(void 0,w)})};R._driver=!0,c(D,R)}function v(D,A){var R=(D||"").split(".");D=R.shift();var g=R.join(".");if(me(y,D)||(y[D]=Ut),y[D]!==Ut){var $=y[D];return $&&$._driver?$=$(g):R.length&&($=Ve($,R)),$==null||$===""?A:$}else return A}function p(D){u.push(D)}function E(D){delete y[D],f(D)}function T(D,A){s.push({name:D,callback:A})}function M(D){var A=this;J(this,{id:Xa++,create:$e,vm:D,manager:ka(t.manager),parsers:n,providers:d,modelFields:i,formulas:S,isMobile:C,rules:D.props.rule,name:D.props.name||Nt(),inFor:D.props.inFor,prop:{components:e,directives:r},drivers:a,renderDriver:null,get:null,refreshData:f,loadData:y,CreateNode:x,bus:new ja,unwatch:[],options:l.ref({}),extendApiFn:u,fetchCache:new WeakMap,tmpData:l.reactive({})}),s.forEach(function(R){A.bus.$on(R.name,R.callback)}),l.nextTick(function(){l.watch(A.options,function(){A.$handle.$manager.updateOptions(A.options.value),A.api().refresh()},{deep:!0})}),J(D.appContext.components,e),J(D.appContext.directives,r),this.$handle=new qe(this),this.name&&(this.inFor?(Me[this.name]||(Me[this.name]=[]),Me[this.name].push(this)):Me[this.name]=this)}M.isMobile=C,J(M.prototype,{init:function(){var A=this;this.isSub()&&this.unwatch.push(l.watch(function(){return A.vm.setupState.parent.setupState.fc.options.value},function(){A.initOptions(),A.$handle.api.refresh()},{deep:!0})),this.vm.props.driver&&(this.renderDriver=pe(this.vm.props.driver)==="object"?this.vm.props.driver:this.drivers[this.vm.props.driver]),!this.renderDriver&&this.vm.setupState.parent&&(this.renderDriver=this.vm.setupState.parent.setupState.fc.renderDriver),this.renderDriver||(this.renderDriver=this.drivers.default),this.initOptions(),this.$handle.init()},targetFormDriver:function(A){for(var R=this,g=arguments.length,$=new Array(g>1?g-1:0),w=1;w<g;w++)$[w-1]=arguments[w];if(this.renderDriver&&this.renderDriver[A])return de(function(){var P;return(P=R.renderDriver)[A].apply(P,$)})},t:function(A,R,g){var $=g?g("$t."+A):this.globalLanguageDriver(A);return $==null&&($=""),$&&R&&Object.keys(R).forEach(function(w){var P=new RegExp("{".concat(w,"}"),"g");$=$.replace(P,R[w])}),$},globalDataDriver:function(A){var R=this,g=A.split("."),$=g.shift(),w=this.options.value.globalData&&this.options.value.globalData[$];if(w){if(w.type==="static")return Ve(w.data,g);var P,V=this.fetchCache.get(w);if(V){if(V.status&&(P=Ve(V.data,g)),!V.loading)return P;V.loading=!1,this.fetchCache.set(w,V)}else this.fetchCache.set(w,{status:!1});var H=tt(function(){ae();var ie=R.fetchCache.get(w);R.options.value.globalData&&Object.values(R.options.value.globalData).indexOf(w)!==-1?(ie&&(ie.loading=!0,R.fetchCache.set(w,ie)),R.bus.$emit("$loadData.$globalData."+$)):R.fetchCache.delete(w)},w.wait||600),W=function(ue){R.fetchCache.set(w,{status:!0,data:ue}),R.bus.$emit("$loadData.$globalData."+$)},ee=function(ue,ye){if(ye&&w.watch===!1)return ae();if(ye){H();return}var he=R.$handle.loadFetchVar(mn(w),ue);R.$handle.api.fetch(he).then(function(fe){W(fe)}).catch(function(fe){W(null)})},ae=this.watchLoadData(ee);return this.unwatch.push(ae),P}},getLocale:function(){var A=this.vm.setupState.top.props.locale;return A&&pe(A)==="object"?A.name:typeof A=="string"?A:"zh-cn"},globalLanguageDriver:function(A){var R=this.vm.setupState.top.props.locale,g=void 0;if(R&&pe(R)==="object"&&(g=Ve(R,A)),g==null){var $=this.options.value.language||{},w=this.getLocale();g=Ve($[w],A)}return g},globalVarDriver:function(A){var R=this,g=A.split("."),$=g.shift(),w=this.options.value.globalVariable&&this.options.value.globalVariable[$];if(w){var P=j.Function(w)?w:ze(w.handle);if(P)return Ve(de(function(){return P(R.get||function(){return R.getLoadData.apply(R,arguments)},R.$handle.api)}),g)}},setData:function(A,R,g){g?c(A,R):(Je(this.vm.setupState.top.setupState.fc.tmpData,A,R),this.bus.$emit("$loadData."+A))},getLoadData:function(A,R){var g=null;if(A!=null){var $=A.split("."),w=$.shift();if(w==="$topForm")g=this.$handle.api.top.formData();else if(w==="$form")g=this.$handle.api.formData();else if(w==="$options")g=this.options.value;else if(w==="$globalData")g=this.globalDataDriver($.join(".")),$=[];else if(w==="$var")g=this.globalVarDriver($.join(".")),$=[];else if(w==="$locale")g=this.getLocale(),$=[];else if(w==="$t")g=this.globalLanguageDriver($.join(".")),$=[];else{var P=this.vm.setupState.top.setupState.fc.tmpData;me(P,w)||(P[w]=Ut),g=P[w]!==Ut?Ve(P,A):v(A),$=[]}g&&$.length&&(g=Ve(g,$))}return g==null||g===""?R:g},watchLoadData:function(A,R){var g=this,$={},w=function(W){g.get||(g.get=P),de(function(){A(P,W)}),g.get===P&&(g.get=void 0)},P=function(W,ee){if($[W])return $[W].val;var ae=l.computed(function(){return g.getLoadData(W,ee)}),ie=W.split("."),ue=ie.shift(),ye=ie.shift()||"",he=tt(function(){var le=g.getLoadData(W,ee);if($[W])JSON.stringify(le)!==JSON.stringify($[W].val)&&($[W].val=le,w(!0));else return},R||0),fe=l.watch(ae,function(le){he()});return g.bus.$on("$loadData."+ue,he),ye&&g.bus.$on("$loadData."+ue+"."+ye,he),$[W]={fn:function(){g.bus.$off("$loadData."+ue,he),ye&&g.bus.$off("$loadData."+ue+"."+ye,he),fe()},val:ae.value},ae.value};w(!1);var V=function(){Object.keys($).forEach(function(W){return $[W].fn()}),$={}};return this.unwatch.push(V),V},isSub:function(){return this.vm.setupState.parent&&this.vm.props.extendOption},initOptions:function(){this.options.value={};var A=G({formData:{},submitBtn:{},resetBtn:{},globalEvent:{},globalData:{}},Ie(_));this.isSub()&&(A=this.mergeOptions(A,this.vm.setupState.parent.setupState.fc.options.value||{},!0)),A=this.mergeOptions(A,this.vm.props.option),this.updateOptions(A)},mergeOptions:function(A,R,g){return R=G({},R||{}),g&&["page","onSubmit","onReset","onCreated","onChange","onMounted","mounted","onReload","reload","formData","el","globalClass","style"].forEach(function($){delete R[$]}),R.global&&(A.global=ta(A.global,R.global),delete R.global),this.$handle.$manager.mergeOptions([R],A),A},updateOptions:function(A){this.options.value=this.mergeOptions(this.options.value,A),this.$handle.$manager.updateOptions(this.options.value),this.bus.$emit("$loadData.$options")},api:function(){return this.$handle.api},render:function(){return this.$handle.render()},mounted:function(){this.$handle.mounted()},unmount:function(){var A=this;if(this.name)if(this.inFor){var R=Me[this.name].indexOf(this);Me[this.name].splice(R,1)}else delete Me[this.name];s.forEach(function(g){A.bus.$off(g.name,g.callback)}),this.tmpData={},this.unwatch.forEach(function(g){return g()}),this.unwatch=[],this.$handle.reloadRule([])},updated:function(){var A=this;this.$handle.bindNextTick(function(){return A.bus.$emit("next-tick",A.$handle.api)})}});function z(D){J(D,{version:t.version,ui:t.ui,isMobile:C,extendApi:p,getData:v,setDataDriver:m,setData:c,removeData:E,refreshData:f,maker:h,component:K,directive:L,setModelField:k,setFormula:O,setDriver:b,register:Y,$vnode:se,parser:Z,use:re,factory:Ne,componentAlias:ne,copyRule:jr,copyRules:Ir,mergeRule:He,fetch:Wr,$form:X,parseFn:ze,parseJson:Fr,toJson:bn,useApp:U,getApi:q,on:T})}function I(D){J(D,{create:$e,install:function(R,g){_=G(G({},_),g||{});var $="_installedFormCreate_"+t.ui;if(R[$]!==!0){R[$]=!0;var w=function(H){var W=arguments.length>1&&arguments[1]!==void 0?arguments[1]:{};return $e(H,W)};z(w),R.config.globalProperties.$formCreate=w;var P=X();R.component(P.name,P),o.forEach(function(V){de(function(){return V(D,R)})})}}})}if(z(F),I(F),m("$cookie",za),m("$localStorage",qa),m("$sessionStorage",Ga),x.use({fragment:"fcFragment"}),t.install&&$e.use(t),U(function(D,A){A.mixin({props:["formCreateInject"]})}),Z(Ba),t.inherit){var B=t.inherit;B.components&&J(e,B.components),B.parsers&&J(n,B.parsers),B.directives&&J(r,B.directives),B.modelFields&&J(i,B.modelFields),B.providers&&J(d,B.providers),B.useApps&&J(o,B.useApps),B.maker&&J(h,B.maker),B.loadData&&J(y,B.loadData),B.formulas&&J(S,B.formulas)}var N=X();return It(N,F),Object.defineProperties(N,{fetch:{get:function(){return F.fetch},set:function(A){F.fetch=A}}}),N.util=F,N}var Pn="hidden",Ya={name:Pn,maker:Se({},Pn,function(t,e){return Bt(Pn)("",t,e)}),render:function(){return[]}},Ja={name:"FcRow",render:function(e,n){return n.vNode.col({props:{span:24}},{default:function(){return[n.vNode.row(n.prop,e)]}})}},Ka={name:"checkbox",mergeProp:function(e){var n=e.prop.props;me(n,"options")||(n.options=e.prop.options||[])}},Qa={name:"radio",mergeProp:function(e){var n=e.prop.props;me(n,"options")||(n.options=e.prop.options||[])}},Za={name:"select",mergeProp:function(e){var n=e.prop.props;me(n,"options")||(n.options=e.prop.options||[])}},es={name:"cascader",mergeProp:function(e){var n=e.prop.props;me(n,"options")||(n.options=e.prop.options||[])}},ts=[Ya,Ja,es,Ka,Qa,Za],De="van",ns={button:De+"-button",icon:De+"-icon",slider:De+"-slider",stepper:De+"-stepper",rate:De+"-rate",uploader:"fc-uploader",cell:De+"-cell",timePicker:"fc-time-picker",datePicker:"fc-date-picker",switch:De+"-switch",select:"fc-select",cascader:"fc-cascader",calendar:"fc-calendar",checkbox:"fc-checkbox",radio:"fc-radio",input:De+"-field",field:De+"-field",formItem:De+"-field",form:De+"-form",col:De+"-col",row:De+"-row",group:"fc-group",array:"fc-group",object:"fc-sub-form",subForm:"fc-sub-form"};function rs(){return{form:{required:"auto",labelAlign:"right",inputAlign:"right"},row:{show:!0,gutter:0},submitBtn:{type:"primary",loading:!1,disabled:!1,block:!0,innerText:"",size:"small",show:!0,col:void 0,click:void 0},resetBtn:{type:"default",loading:!1,disabled:!1,block:!0,innerText:"",size:"small",show:!1,col:void 0,click:void 0}}}function ri(t,e){if(!!me(t,e)&&j.String(t[e])){var n;t[e]=(n={},Se(n,e,t[e]),Se(n,"show",!0),n)}}function We(t){return t===!1}function is(t,e){me(t,e)&&!j.Object(t[e])&&(t[e]={show:!!t[e]})}function ii(t){var e=G({},t);return delete e.children,e}var os={validate:function(){var e=this.form();return e?e.validate():new Promise(function(n){return n()})},validateField:function(e){var n=this;return new Promise(function(r,i){var a=n.form();a?a.validate(e).then(r).catch(i):r()})},clearValidateState:function(e){var n=this.form();if(n)return n.resetValidation(e.id)},tidyOptions:function(e){return["submitBtn","resetBtn","row","info","wrap","col","title"].forEach(function(n){is(e,n)}),e},tidyRule:function(e){var n=e.prop;return ri(n,"title"),ri(n,"info"),n},mergeProp:function(e){var n=this,r={info:{icon:!0},title:{},col:{span:24},wrap:{}};["info","wrap","col","title"].forEach(function(i){e.prop[i]=nt([n.options[i]||{},e.prop[i]||{}],r[i])})},getDefaultOptions:function(){return rs()},update:function(){var e=this.options.form;this.rule={props:G({},e),on:{submit:function(r){r.preventDefault()}},class:[e.className,e.class,"form-create-m",this.$handle.preview?"is-preview":""],style:e.style,type:"form"}},beforeRender:function(){var e=this.key,n=this.ref;J(this.rule,{key:e,ref:n})},render:function(e){var n=this;return e.slotLen()&&!this.$handle.preview&&e.setSlot(void 0,function(){return n.makeFormBtn()}),this.$r(this.rule,We(this.options.row.show)?e.getSlots():[this.makeRow(e)])},makeWrap:function(e,n){var r=this,i=e.prop,a="".concat(this.key).concat(e.key),o=i.col,s=this.isTitle(i)&&i.wrap.title!==!1,u=this.rule.props.col;delete i.wrap.title;var d=We(i.wrap.show)?n:this.$r(nt([i.wrap,{props:G(G({modelValue:e.rule.value,label:s?i.title.title:void 0},ii(i.wrap||{})),{},{name:e.id,rules:e.injectValidate()}),class:this.$render.mergeClass(i.className,"fc-form-item"),key:"".concat(a,"fi"),ref:e.wrapRef,type:"formItem"}]),G({input:function(){return n}},s?{label:function(){return r.makeInfo(i,a,e)}}:{}));return We(u)||We(o.show)?d:this.makeCol(i,a,[d])},isTitle:function(e){if(this.options.form.title===!1)return!1;var n=e.title;return!(!n.title&&!n.native||We(n.show))},makeInfo:function(e,n,r){var i,a,o=G({},e.title),s=G({},e.info),u=this.getSlot("title"),d=[u?u({title:(i=r.refRule)===null||i===void 0?void 0:i.__$title.value,rule:r.rule,options:this.options}):(a=r.refRule)===null||a===void 0?void 0:a.__$title.value],h=!We(s.show)&&(s.info||s.native)&&!We(s.icon);h&&d[s.align!=="left"?"unshift":"push"](this.$r({type:s.icon===!0?"icon-warning":s.icon,style:"width:1em;"}));var _=nt([o,{props:ii(o),key:"".concat(n,"tit"),class:"fc-form-title",type:o.type||"span"}]);return h&&s.info&&!_.props.onClick&&(_.props.onClick=function(){var y;dn.showNotify({type:"warning",message:(y=r.refRule)===null||y===void 0?void 0:y.__$info.value,duration:1e3})}),delete _.props.show,delete _.props.title,delete _.props.native,this.$r(_,d)},makeCol:function(e,n,r){var i=e.col;return this.$r({class:this.$render.mergeClass(i.class,"fc-form-col"),type:"col",props:i||{span:24},key:"".concat(n,"col")},r)},makeRow:function(e){var n=this.options.row||{};return this.$r({type:"row",props:n,class:this.$render.mergeClass(n.class,"fc-form-row"),key:"".concat(this.key,"row")},e)},makeFormBtn:function(){var e=[];if(We(this.options.submitBtn.show)||e.push(this.makeSubmitBtn()),We(this.options.resetBtn.show)||e.push(this.makeResetBtn()),!!e.length)return this.$r({type:"cell",class:"fc-form-cell fc-form-footer",key:"".concat(this.key,"fb")},e)},makeResetBtn:function(){var e=this,n=G({},this.options.resetBtn),r=n.innerText||this.$handle.api.t("reset")||"\u91CD\u7F6E";return delete n.innerText,delete n.click,delete n.col,delete n.show,this.$r({type:"button",props:n,class:"fc-reset-btn",style:{width:n.width},on:{click:function(){var a=e.$handle.api;e.options.resetBtn.click?e.options.resetBtn.click(a):a.resetFields()}},key:"".concat(this.key,"b2")},[r])},makeSubmitBtn:function(){var e=this,n=G({},this.options.submitBtn),r=n.innerText||this.$handle.api.t("submit")||"\u63D0\u4EA4";return delete n.innerText,delete n.click,delete n.col,delete n.show,this.$r({type:"button",props:n,class:"fc-submit-btn",style:{width:n.width},on:{click:function(){var a=e.$handle.api;e.options.submitBtn.click?e.options.submitBtn.click(a):a.submit().catch(function(){})}},key:"".concat(this.key,"b1")},[r])}};function as(t,e){e===void 0&&(e={});var n=e.insertAt;if(!(!t||typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css",n==="top"&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}var ss='.form-create-m{width:100%}.form-create-m .fc-none,.form-create-m.is-preview .fc-clock{display:none!important}.form-create-m .van-field:has(.van-field__value>.van-field__body>.van-field__control>.van-field):after{display:none}.form-create-m .van-field>.van-field__value>.van-field__body>.van-field__control>*>.van-field:after,.form-create-m .van-field>.van-field__value>.van-field__body>.van-field__control>.van-field:after{display:block;right:0}.form-create-m .fc-form-item>.van-field__value>.van-field__body>.van-field__control>.van-cell{padding:0}.form-create-m .fc-form-item .fc-form-item{--van-padding-md:0;padding-left:0;padding-right:0}.form-create-m>.van-row>.van-col--24:last-of-type>.van-cell:last-of-type:after{display:none}.form-create-m .fc-reset-btn{margin-top:12px}.form-create-m .fc-form-title{align-items:center;display:inline-flex}.form-create-m.is-preview .van-field__label--required:before{display:none}._fc-m-group{align-items:flex-end;display:flex;flex-direction:column;justify-content:center;min-height:38px;width:100%}._fc-m-group-disabled ._fc-m-group-add,._fc-m-group-disabled ._fc-m-group-btn{cursor:not-allowed}._fc-m-group-handle{background-color:#fff;border:1px dashed #d9d9d9;border-radius:15px;bottom:-15px;display:flex;flex-direction:row;padding:3px 8px;position:absolute;right:30px;transform:scale(1.1)}._fc-m-group-btn{cursor:pointer}._fc-m-group-idx{align-items:center;background:#eee;border-radius:15px;bottom:-15px;display:flex;font-weight:700;height:30px;justify-content:center;left:10px;position:absolute;width:30px}._fc-m-group-handle ._fc-m-group-btn+._fc-m-group-btn{margin-left:7px}._fc-m-group-container{border:1px dashed #d9d9d9;border-radius:5px;box-sizing:border-box;display:flex;flex-direction:column;margin-bottom:25px;padding:5px 5px 25px;position:relative;width:100%}._fc-m-group-arrow{height:20px;position:relative;width:20px}._fc-m-group-arrow:before{border-left:2px solid #999;border-top:2px solid #999;content:"";height:9px;left:5px;position:absolute;top:8px;transform:rotate(45deg);width:9px}._fc-m-group-arrow._fc-m-group-down{transform:rotate(180deg)}._fc-m-group-plus-minus{cursor:pointer;height:20px;position:relative;width:20px}._fc-m-group-plus-minus:after,._fc-m-group-plus-minus:before{background-color:#409eff;content:"";height:2px;left:50%;position:absolute;top:50%;transform:translate(-50%,-50%);width:60%}._fc-m-group-plus-minus:before{transform:translate(-50%,-50%) rotate(90deg)}._fc-m-group-plus-minus._fc-m-group-minus:before{display:none}._fc-m-group-plus-minus._fc-m-group-minus:after{background-color:#f56c6c}._fc-m-group-add{border:1px solid rgba(64,158,255,.5);border-radius:15px;cursor:pointer;height:25px;transform:scale(1.1);width:25px}._fc-m-group-add._fc-m-group-plus-minus:after,._fc-m-group-add._fc-m-group-plus-minus:before{width:50%}';as(ss);function oi(t,e){return j.Boolean(t)?t={show:t}:!j.Undef(t)&&!j.Object(t)&&(t={show:e}),t}function us(t,e){return{formEl:function(){return e.$manager.form()},wrapEl:function(r){var i=e.getFieldCtx(r);if(!!i)return e.vm.refs[i.wrapRef]},validate:function(r){return new Promise(function(i,a){var o=t.children,s=[e.$manager.validate()];o.forEach(function(u){s.push(u.validate())}),Promise.all(s).then(function(){i(!0),r&&r(!0)}).catch(function(u){a(u),r&&r(u),e.vm.emit("validate-fail",u,{api:t})})})},validateField:function(r,i){return new Promise(function(a,o){var s=e.getFieldCtx(r);if(!!s){var u=e.subForm[s.id],d=[e.$manager.validateField(s.id)];Ze(u).forEach(function(h){d.push(h.validate())}),Promise.all(d).then(function(){a(null),i&&i(null)}).catch(function(h){o(h),i&&i(h),e.vm.emit("validate-field-fail",h,{field:r,api:t})})}})},clearValidateState:function(r){var i=this,a=arguments.length>1&&arguments[1]!==void 0?arguments[1]:!0;t.helper.tidyFields(r).forEach(function(o){a&&i.clearSubValidateState(o),e.getCtxs(o).forEach(function(s){e.$manager.clearValidateState(s)})})},clearSubValidateState:function(r){t.helper.tidyFields(r).forEach(function(i){e.getCtxs(i).forEach(function(a){var o=e.subForm[a.id];!o||(Array.isArray(o)?o.forEach(function(s){s.clearValidateState()}):o&&o.clearValidateState())})})},btn:{loading:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.submitBtnProps({loading:!!r})},disabled:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.submitBtnProps({disabled:!!r})},show:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.submitBtnProps({show:!!r})}},resetBtn:{loading:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.resetBtnProps({loading:!!r})},disabled:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.resetBtnProps({disabled:!!r})},show:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:!0;t.resetBtnProps({show:!!r})}},submitBtnProps:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=oi(e.options.submitBtn,!0);J(i,r),e.options.submitBtn=i,t.refreshOptions()},resetBtnProps:function(){var r=arguments.length>0&&arguments[0]!==void 0?arguments[0]:{},i=oi(e.options.resetBtn,!1);J(i,r),e.options.resetBtn=i,t.refreshOptions()},submit:function(r,i){return new Promise(function(a,o){t.validate().then(function(){var s=t.formData();j.Function(r)&&de(function(){return r(s,t)}),j.Function(e.options.onSubmit)&&de(function(){return e.options.onSubmit(s,t)}),e.vm.emit("submit",s,t),a(s)}).catch(function(){for(var s=arguments.length,u=new Array(s),d=0;d<s;d++)u[d]=arguments[d];j.Function(i)&&de(function(){return i.apply(void 0,[t].concat(u))}),o.apply(void 0,u)})})}}}var Rn={name:"required",load:function(e,n,r){var i=ls(e.getValue());if(i.required===!1)e.clearProp(),r.clearValidateState([n.field]);else{var a=G({required:!0,validator:function(d){return!j.empty(d)},trigger:["onChange","onSubmit"]},i),o=n.__fc__.refRule.__$title.value;if(!a.message)a.message=r.t("required",{title:o})||o+(r.getLocale()==="en"?" is required":"\u4E0D\u80FD\u4E3A\u7A7A");else{var s=a.message.match(/^\{\{\s*\$t\.(.+)\s*\}\}$/);s&&(a.message=r.t(s[1],{title:o}))}e.getProp().validate=[a]}r.sync(n)},watch:function(){Rn.load.apply(Rn,arguments)}};function ls(t){return j.Boolean(t)?{required:t}:j.String(t)?{message:t}:j.Undef(t)?{required:!1}:j.Function(t)?{validator:t}:j.Object(t)?t:{}}function cs(t){t.componentAlias(ns),Jo.forEach(function(e){t.component(e.name,e)}),t.register(Rn),ts.forEach(function(e){t.parser(e)}),dn.Field.props&&(dn.Field.props.modelValue={type:[String,Number,Array,Object,Boolean],default:""})}function fs(){return ni({ui:"vant",version:"3.2.18",manager:os,extendApi:us,install:cs,isMobile:!0,attrs:{normal:["col","wrap"],key:["title","info"]}})}var ge=fs();typeof window<"u"&&(window.formCreateMobile=ge),ge.maker;/*!
 * @form-create/component-elm-select v3.2.18
 * (c) 2018-2025 xaboy
 * Github https://github.com/xaboy/form-create with select
 * Released under the MIT License.
 */function ai(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function ds(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?ai(Object(n),!0).forEach(function(r){hs(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):ai(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function zt(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?zt=function(e){return typeof e}:zt=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},zt(t)}function hs(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ps(t,e){return Object.keys(t).reduce(function(n,r){return(!e||e.indexOf(r)===-1)&&(n[r]=t[r]),n},{})}var qt={type:function(e,n){return Object.prototype.toString.call(e)==="[object "+n+"]"},Undef:function(e){return e==null},Element:function(e){return zt(e)==="object"&&e!==null&&e.nodeType===1&&!qt.Object(e)},trueArray:function(e){return Array.isArray(e)&&e.length>0},Function:function(e){var n=this.getType(e);return n==="Function"||n==="AsyncFunction"},getType:function(e){var n=Object.prototype.toString.call(e);return/^\[object (.*)\]$/.exec(n)[1]},empty:function(e){return e==null||Array.isArray(e)&&Array.isArray(e)&&!e.length?!0:typeof e=="string"&&!e}};["Date","Object","String","Boolean","Array","Number"].forEach(function(t){qt[t]=function(e){return qt.type(e,t)}});function ms(t,e){return{}.hasOwnProperty.call(t,e)}var gs="fcSelect",vs=l.defineComponent({name:gs,inheritAttrs:!1,props:{formCreateInject:Object,modelValue:{type:[Array,String,Number,Boolean,Object],default:void 0},type:String},emits:["update:modelValue","fc.el"],setup:function(e){var n=l.toRef(e.formCreateInject,"options",[]),r=l.toRef(e,"modelValue"),i=function(){return Array.isArray(n.value)?n.value:[]};return{options:i,value:r}},render:function(){var e=this,n,r,i=function(u,d){return l.createVNode(l.resolveComponent("ElOption"),l.mergeProps(u,{key:""+d+"-"+u.value}),null)},a=function(u,d){return l.createVNode(l.resolveComponent("ElOptionGroup"),{label:u.label,key:""+d+"-"+u.label},{default:function(){return[qt.trueArray(u.options)&&u.options.map(function(_,y){return i(_,y)})]}})},o=this.options();return l.createVNode(l.resolveComponent("ElSelect"),l.mergeProps(this.$attrs,{modelValue:this.value,"onUpdate:modelValue":function(u){return e.$emit("update:modelValue",u)},ref:"el"}),ds({default:function(){return[o.map(function(u,d){return ms(u||"","options")?a(u,d):i(u,d)}),(n=(r=e.$slots).default)===null||n===void 0?void 0:n.call(r)]}},ps(this.$slots,["default"])))},mounted:function(){this.$emit("fc.el",this.$refs.el)}});/*!
 * @form-create/component-elm-tree v3.2.18
 * (c) 2018-2025 xaboy
 * Github https://github.com/xaboy/form-create with tree
 * Released under the MIT License.
 */function ys(t){return Array.isArray(t)?t:[null,void 0,""].indexOf(t)>-1?[]:[t]}var _s="fcTree",bs=l.defineComponent({name:_s,inheritAttrs:!1,formCreateParser:{mergeProp:function(e){var n=e.prop.props;n.nodeKey||(n.nodeKey="id"),n.props||(n.props={label:"title"})}},props:{type:String,modelValue:{type:[Array,String,Number],default:function(){return[]}}},emits:["update:modelValue","fc.el"],watch:{modelValue:function(){this.setValue()}},methods:{updateValue:function(){if(!!this.$refs.tree){var e;this.type==="selected"?e=this.$refs.tree.getCurrentKey():e=this.$refs.tree.getCheckedKeys(),this.$emit("update:modelValue",e)}},setValue:function(){if(!!this.$refs.tree){var e=this.type;e==="selected"?this.$refs.tree.setCurrentKey(this.modelValue):this.$refs.tree.setCheckedKeys(ys(this.modelValue))}}},render:function(){return l.createVNode(l.resolveComponent("ElTree"),l.mergeProps(this.$attrs,{ref:"tree",onCheck:this.updateValue,onNodeClick:this.updateValue}),this.$slots)},mounted:function(){this.setValue(),this.$emit("fc.el",this.$refs.tree)}});/*!
 * @form-create/component-elm-upload v3.2.18
 * (c) 2018-2025 xaboy
 * Github https://github.com/xaboy/form-create with upload
 * Released under the MIT License.
 */function si(t,e){var n=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter(function(i){return Object.getOwnPropertyDescriptor(t,i).enumerable})),n.push.apply(n,r)}return n}function kn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{};e%2?si(Object(n),!0).forEach(function(r){ws(t,r,n[r])}):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):si(Object(n)).forEach(function(r){Object.defineProperty(t,r,Object.getOwnPropertyDescriptor(n,r))})}return t}function Gt(t){return typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Gt=function(e){return typeof e}:Gt=function(e){return e&&typeof Symbol=="function"&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e},Gt(t)}function ws(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Ht(t){return Array.isArray(t)?t:[null,void 0,""].indexOf(t)>-1?[]:[t]}function $s(t,e){return Object.keys(t).reduce(function(n,r){return(!e||e.indexOf(r)===-1)&&(n[r]=t[r]),n},{})}function Os(t,e){e===void 0&&(e={});var n=e.insertAt;if(!(!t||typeof document>"u")){var r=document.head||document.getElementsByTagName("head")[0],i=document.createElement("style");i.type="text/css",n==="top"&&r.firstChild?r.insertBefore(i,r.firstChild):r.appendChild(i),i.styleSheet?i.styleSheet.cssText=t:i.appendChild(document.createTextNode(t))}}var Ss="._fc-upload{width:100%}._fc-exceed .el-upload{display:none}.el-upload-list.is-disabled .el-upload{cursor:not-allowed!important}";Os(Ss);var ui={name:"IconUpload"},Es={class:"icon",viewBox:"0 0 1024 1024",xmlns:"http://www.w3.org/2000/svg"},Cs=l.createElementVNode("path",{fill:"currentColor",d:"M160 832h704a32 32 0 110 64H160a32 32 0 110-64zm384-578.304V704h-64V247.296L237.248 490.048 192 444.8 508.8 128l316.8 316.8-45.312 45.248L544 253.696z"},null,-1),As=[Cs];function xs(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("svg",Es,As)}ui.render=xs;function li(t,e){return Gt(t)==="object"?t:{url:t,is_string:!0,name:Ds(t),uid:e}}function ci(t){return kn(kn({},t),{},{file:t,value:t})}function Ds(t){return(""+t).split("/").pop()}var Ps="fcUpload",Rs=l.defineComponent({name:Ps,inheritAttrs:!1,formCreateParser:{toFormValue:function(e){return Ht(e)},toValue:function(e,n){return n.prop.props.limit===1?e[0]||"":e}},props:{previewMask:void 0,onPreview:Function,httpRequest:Function,modalTitle:String,listType:String,formCreateInject:Object,modelValue:[Array,String,Object]},emits:["update:modelValue","change","remove","fc.el"],data:function(){return{previewVisible:!1,previewImage:"",fileList:[]}},created:function(){this.fileList=Ht(this.modelValue).map(li).map(ci)},watch:{modelValue:function(e){this.fileList=Ht(e).map(li).map(ci)}},methods:{handlePreview:function(e){this.onPreview?this.onPreview.apply(this,arguments):this.listType==="text"?window.open(e.url):(this.previewImage=e.url,this.previewVisible=!0)},update:function(e){var n=e.map(function(r){return r.is_string?r.url:r.value||r.url}).filter(function(r){return r!==void 0});this.$emit("update:modelValue",n)},handleCancel:function(){this.previewVisible=!1},handleChange:function(e,n){this.$emit.apply(this,["change"].concat(Array.prototype.slice.call(arguments))),e.status==="success"&&this.update(n)},handleRemove:function(e,n){this.$emit.apply(this,["remove"].concat(Array.prototype.slice.call(arguments))),this.update(n)},doHttpRequest:function(e){if(this.httpRequest)return this.httpRequest(e);e.source="upload",this.formCreateInject.api.fetch(e)}},render:function(){var e,n,r=this,i=Ht(this.modelValue).length;return l.createVNode("div",{class:"_fc-upload"},[l.createVNode(l.resolveComponent("ElUpload"),l.mergeProps({key:i},this.$attrs,{listType:this.listType||"picture-card",class:{"_fc-exceed":this.$attrs.limit?this.$attrs.limit<=i:!1},onPreview:this.handlePreview,onChange:this.handleChange,onRemove:this.handleRemove,httpRequest:this.doHttpRequest,fileList:this.fileList,ref:"upload"}),kn({default:function(){return[((e=(n=r.$slots).default)===null||e===void 0?void 0:e.call(n))||(["text","picture"].indexOf(r.listType)===-1?l.createVNode(l.resolveComponent("ElIcon"),null,{default:function(){return[l.createVNode(ui,null,null)]}}):l.createVNode(l.resolveComponent("ElButton"),{type:"primary"},{default:function(){return[r.formCreateInject.t("clickToUpload")||"\u70B9\u51FB\u4E0A\u4F20"]}}))]}},$s(this.$slots,["default"]))),l.createVNode(l.resolveComponent("ElDialog"),{appendToBody:!0,modal:this.previewMask,title:this.modalTitle,modelValue:this.previewVisible,onClose:this.handleCancel},{default:function(){return[l.createVNode("img",{style:"width: 100%",src:r.previewImage},null)]}})])},mounted:function(){this.$emit("fc.el",this.$refs.upload)}});const Te={type(t,e){return Object.prototype.toString.call(t)==="[object "+e+"]"},Undef(t){return t==null},Element(t){return typeof t=="object"&&t!==null&&t.nodeType===1&&!Te.Object(t)},trueArray(t){return Array.isArray(t)&&t.length>0},Function(t){const e=this.getType(t);return e==="Function"||e==="AsyncFunction"},getType(t){const e=Object.prototype.toString.call(t);return/^\[object (.*)\]$/.exec(e)[1]},empty(t){return t==null||Array.isArray(t)&&Array.isArray(t)&&!t.length?!0:typeof t=="string"&&!t}};["Date","Object","String","Boolean","Array","Number"].forEach(t=>{Te[t]=function(e){return Te.type(e,t)}});function fi(t,e){return{}.hasOwnProperty.call(t,e)}const ft={Add:(t,e)=>{let n,r,i;try{n=t.toString().split(".")[1].length}catch{n=0}try{r=e.toString().split(".")[1].length}catch{r=0}return i=Math.pow(10,Math.max(n,r)),(t*i+e*i)/i},Sub:(t,e)=>{let n,r,i;try{n=t.toString().split(".")[1].length}catch{n=0}try{r=e.toString().split(".")[1].length}catch{r=0}i=Math.pow(10,Math.max(n,r));let a=n>=r?n:r;return Number(((t*i-e*i)/i).toFixed(a))},Mul:(t,e)=>{let n=0,r=t.toString(),i=e.toString();try{n+=r.split(".")[1].length}catch{}try{n+=i.split(".")[1].length}catch{}return Number(r.replace(".",""))*Number(i.replace(".",""))/Math.pow(10,n)},Div:(t,e)=>{let n=0,r=0;try{n=t.toString().split(".")[1].length}catch{}try{r=e.toString().split(".")[1].length}catch{}let i=Number(t.toString().replace(".","")),a=Number(e.toString().replace(".",""));return i/a*Math.pow(10,r-n)}},St={ADD:function(t,e){return ft.Add(t,e)},SUB:function(t,e){return ft.Sub(t,e)},MUL:function(t,e){return ft.Mul(t,e)},DIV:function(t,e){return ft.Div(t,e)},SUM:function(...t){return(t||[]).reduce((e,n)=>ft.Add(e,Array.isArray(n)?St.SUM(...n):n||0),0)},MAX:function(...t){const e=Array.isArray(t[0])?t[0]:t;return Math.max(...e.map(n=>parseFloat(n)).filter(n=>!isNaN(n)))},MIN:function(...t){const e=Array.isArray(t[0])?t[0]:t;return Math.min(...e.map(n=>parseFloat(n)).filter(n=>!isNaN(n)))},ABS:function(t){return parseFloat(Math.abs(t))||0},AVG:function(...t){const e=Array.isArray(t[0])?t[0]:t;return e.length?ft.Div(St.SUM(e),e.length):0},POWER:function(t,e){return Math.pow(parseFloat(t),parseFloat(e))},RAND:function(){return Math.random()},CEIL:function(t){return Math.ceil(parseFloat(t))||0},FLOOR:function(t){return Math.floor(parseFloat(t)||0)},FIXED:function(t,e){const n=Math.pow(10,e||0);return(Math.floor(parseFloat(t)*n)/n).toFixed(e||0)},ISNUMBER:function(t){return t===""||t===null?!1:!isNaN(Number(t))},PI:function(){return Number(Math.PI)},ROUND:function(t,e){return t=parseFloat(t),isNaN(t)?0:parseFloat(t.toFixed(parseFloat(e)||0))},SQRT:function(t){return Math.sqrt(parseFloat(t))||0},TONUMBER:function(t){return parseFloat(t)||0},NOW:function(){const t=new Date,e=t.getFullYear(),n=("0"+(t.getMonth()+1)).slice(-2),r=("0"+t.getDate()).slice(-2),i=("0"+t.getHours()).slice(-2),a=("0"+t.getMinutes()).slice(-2),o=("0"+t.getSeconds()).slice(-2);return e+"-"+n+"-"+r+" "+i+":"+a+":"+o},TODAY:function(){const t=new Date,e=t.getFullYear(),n=("0"+(t.getMonth()+1)).slice(-2),r=("0"+t.getDate()).slice(-2);return e+"-"+n+"-"+r},YEAR:function(t){return t?new Date(t).getFullYear():null},MONTH:function(t){return t?new Date(t).getMonth()+1:null},DAY:function(t){return t?new Date(t).getDate():null},HOUR:function(t){return t?new Date(t).getHours():null},MINUTE:function(t){return t?new Date(t).getMinutes():null},SECOND:function(t){return t?new Date(t).getSeconds():null},DIFFDAYS:function(t,e){const n=new Date(t),r=new Date(e);return parseInt(Math.ceil(Math.abs(r.getTime()-n.getTime())/(1e3*60*60*24)))},DIFFHOURS:function(t,e){const n=new Date(t),r=new Date(e);return parseFloat(Math.abs(r.getTime()-n.getTime())/(1e3*60*60)).toFixed(2)},DIFFMINUTES:function(t,e){const n=new Date(t),r=new Date(e);return parseInt(Math.ceil(Math.abs(r.getTime()-n.getTime())/(1e3*60)))},TIMESTAMP:function(t){return Date.parse(t)},STARTSWITH:function(t,e){return(""+t).substring(0,(""+e).length)===e},EMPTY:function(t){return Te.empty(t)},NOTEMPTY:function(t){return!Te.empty(t)},LEN:function(t){return Array.isArray(t)?t.length:0},MOD:function(t,e){return t=parseFloat(t),e=parseFloat(e),!t||!e||isNaN(t)||isNaN(e)?0:t%e},SLICELEFT:function(t,e){return(""+t).slice(0,Number(e)||0)},SLICERIGHT:function(t,e){return(""+t).slice(Number(e)*-1)},TOLOWER:function(t){return(""+t).toLowerCase()},TOUPPER:function(t){return(""+t).toUpperCase()},INCLUDES:function(t,e){return(t||"").indexOf(e||"")},REPLACE:function(t,e,n){return(t||"").replace(e||"",n||"")},REPLACEALL:function(t,e,n){return(t||"").replaceAll(e||"",n||"")},TRIM:function(t){return(t||"").trim()},TOCHINSESAMOUNT:function(t){let e=["\u96F6","\u58F9","\u8D30","\u53C1","\u8086","\u4F0D","\u9646","\u67D2","\u634C","\u7396"],n=["","\u4E07","\u4EBF","\u4E07\u4EBF","\u4EBF\u4EBF"],r=["","\u62FE","\u4F70","\u4EDF"],i=["\u89D2","\u5206"];function a(y){let x="",S="",C=0,F=!0;for(;y>0;){let q=y%10;q===0?F||(F=!0,S=e[q]+S):(F=!1,x=e[q],x+=r[C],S=x+S),C++,y=Math.floor(y/10)}return S}let o=0,s="",u="",d=!1;if(t===0)return e[0];let h=Math.floor(t),_=Math.round((t-h)*100);for(;h>0;){let y=h%1e4;d&&(u=e[0]+u),s=a(y),s+=y!==0?n[o]:n[0],u=s+u,d=y<1e3&&y>0,h=Math.floor(h/1e4),o++}return u&&(u+="\u5143"),_>0?(u+=e[Math.floor(_/10)]+i[0],_%10!==0&&(u+=e[_%10]+i[1])):u&&(u+="\u6574"),u},UNION:function(...t){return(Array.isArray(t[0])?t[0]:t).filter((n,r,i)=>i.indexOf(n)===r)},AND:function(...t){return!!t.reduce((e,n)=>e&&n,!0)},OR:function(...t){return!!t.reduce((e,n)=>e||n,!1)},IF:function(t,e=!0,n=!1){return t?e:n},DEFAULT:function(t,e){return Te.Undef(t)?e:t},CASE:function(...t){for(let e=0;e<t.length-1;e+=2)if(t[e])return t[e+1];return null},COLUMN:function(t,e){const n=[];return Array.isArray(t)&&t.forEach(function(r){Array.isArray(r)?n.push(...St.COLUMN(r,e)):r&&n.push(r[e])}),n},VALUE:function(t,e,n){const r=(""+e).split(".");let i=t;for(let a=0;a<r.length;a++)if(fi(i,r[a]))i=i[r[a]];else return n;return i},CONCAT:function(...t){return"".concat(...t)},INTERSECTIONSET:function(t,e){const n=[];for(let r=0;r<t.length;r++){const i=t[r];e.indexOf(i)!==-1&&n.indexOf(i)===-1&&n.push(i)}return n},LIST:function(...t){return t},IN:function(t,e){return(t||[]).indexOf(e)>-1},FALSE:function(){return!1},TRUE:function(){return!0},NOT:function(t){return!t},EQ:function(t,e){return t===e},GE:function(t,e){return t>=e},GT:function(t,e){return t>e},LE:function(t,e){return t<=e},LT:function(t,e){return t<e},NE:function(t,e){return t!==e}},rm="",we=(t,e)=>{const n=t.__vccOpts||t;for(const[r,i]of e)n[r]=i;return n},di=t=>{const e=[];return t.forEach(n=>{n.field&&e.push(n.field),n.children&&e.push(...di(n.children))}),e},ks=l.defineComponent({name:"StepForm",props:{stepsProps:Object,modelValue:Object,formCreateInject:Object,autoValidate:Boolean,steps:{type:Array,required:!0,default:()=>[]},options:{type:Object,default:()=>l.reactive({submitBtn:!1,resetBtn:!1})}},emits:["update:modelValue","change","itemMounted","submit","next"],data(){return{active:0,cacheRule:[],cacheValue:{},subApi:{},Form:l.markRaw(this.formCreateInject.form.$form())}},watch:{active(){this.init()},modelValue(t){this.setValue(t)}},methods:{init(){this.steps.forEach((t,e)=>{this.cacheRule[e]?this.cacheRule[e].display=e===this.active:this.cacheRule[e]={type:"FcRow",native:!0,display:e===this.active,children:t.rule}})},onPrev(){this.active--},validate(){return new Promise((t,e)=>{const n=di(this.cacheRule[this.active].children);n.length>0?Promise.all(n.map(r=>this.subApi.validateField(r))).then(()=>{t()}).catch(r=>{e(r)}):t()})},onNext(){this.autoValidate?this.validate().then(()=>{this.active++}).catch(t=>{}):this.active++,this.$emit("next",{active:this.active,api:this.subApi})},submit(){const t=()=>{this.$emit("submit",this.subApi.formData(),this.subApi)};this.autoValidate?this.validate().then(()=>{t()}).catch(e=>{}):t()},addSubApi(t){this.subApi=t,this.$emit("itemMounted",t)},formData(t){this.cacheValue=JSON.stringify(t),this.$emit("update:modelValue",t),this.$emit("change",t)},setValue(t){const e=JSON.stringify(t);this.cacheValue!==e&&(this.cacheValue=e,this.subApi.coverValue(t||{}))}},created(){this.init()}}),Vs={class:"_fc-step-form"};function Ms(t,e,n,r,i,a){const o=l.resolveComponent("van-step"),s=l.resolveComponent("van-steps"),u=l.resolveComponent("van-button"),d=l.resolveComponent("van-col"),h=l.resolveComponent("van-row");return l.openBlock(),l.createElementBlock("div",Vs,[l.createVNode(s,l.mergeProps({active:t.active},t.stepsProps),{default:l.withCtx(()=>[(l.openBlock(!0),l.createElementBlock(l.Fragment,null,l.renderList(t.steps,_=>(l.openBlock(),l.createBlock(o,l.mergeProps({ref_for:!0},_.props),{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(_.props.title),1)]),_:2},1040))),256))]),_:1},16,["active"]),(l.openBlock(),l.createBlock(l.resolveDynamicComponent(t.Form),{option:t.options,rule:t.cacheRule,extendOption:!0,modelValue:t.modelValue,"onUpdate:api":t.addSubApi,onEmitEvent:t.$emit,"onUpdate:modelValue":t.formData},null,40,["option","rule","modelValue","onUpdate:api","onEmitEvent","onUpdate:modelValue"])),l.createVNode(h,null,{default:l.withCtx(()=>[l.createVNode(d,{span:24,style:{"margin-top":"15px"}},{default:l.withCtx(()=>[t.active>0?(l.openBlock(),l.createBlock(u,{key:0,block:"",size:"small",onClick:t.onPrev},{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("prevStep")||"\u4E0A\u4E00\u6B65"),1)]),_:1},8,["onClick"])):l.createCommentVNode("",!0),t.active<t.cacheRule.length-1?(l.openBlock(),l.createBlock(u,{key:1,block:"",size:"small",type:"primary",onClick:t.onNext},{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("nextStep")||"\u4E0B\u4E00\u6B65"),1)]),_:1},8,["onClick"])):l.createCommentVNode("",!0),t.active===t.cacheRule.length-1?(l.openBlock(),l.createBlock(u,{key:2,block:"",size:"small",class:"fc-clock",type:"primary",onClick:t.submit,style:{"margin-top":"10px"}},{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("submit")||"\u63D0\u4EA4"),1)]),_:1},8,["onClick"])):l.createCommentVNode("",!0)]),_:1})]),_:1})])}const Ts=we(ks,[["render",Ms]]);function dt(t,e,n){t[e]=n}function hi(t,e={},n){let r=!1;for(let i in e)if(Object.prototype.hasOwnProperty.call(e,i)){let a=e[i];if((r=Array.isArray(a))||Te.Object(a)){let o=t[i]===void 0;if(r)r=!1,o&&dt(t,i,[]);else if(a._clone&&n!==void 0)if(n)a=a.getRule(),o&&dt(t,i,{});else{dt(t,i,a._clone());continue}else o&&dt(t,i,{});t[i]=hi(t[i],a,n)}else dt(t,i,a),Te.Undef(a)||(Te.Undef(a.__json)||(t[i].__json=a.__json),Te.Undef(a.__origin)||(t[i].__origin=a.__origin))}return n!==void 0&&Array.isArray(t)?t.filter(i=>!i||!i.__ctrl):t}function ot(t){return hi({},{value:t}).value}const im="",Fs=l.defineComponent({name:"FcPopup",emits:["confirm","submit","validateFail"],props:{formData:Object,options:{type:Object,default:()=>l.reactive({submitBtn:!1,resetBtn:!1})},rule:Array,autoClose:{type:Boolean,default:!0},footer:{type:Boolean,default:!0},modelValue:Boolean,formCreateInject:Object,title:String},data(){return{visible:!1,fapi:{},value:{},formRule:[],Form:l.markRaw(this.formCreateInject.form.$form())}},methods:{open(t){this.$nextTick(()=>{this.visible=!0,this.value=ot(t||this.formData||{}),this.formRule=ot(this.rule||[])})},close(){this.visible=!1},handleConfirm(){this.$emit("confirm",this.fapi),this.fapi.submit().then(t=>{this.$emit("submit",t,this.fapi,this.close),this.autoClose&&this.close()}).catch(t=>{this.$emit("validateFail",t,this.fapi)})}},mounted(){this.formCreateInject.api.top.bus.$on("fc.closeDialog",this.close),l.onUnmounted(()=>{this.formCreateInject.api.top.bus.$off("fc.closeDialog",this.close)})}}),js={class:"_fc-popup-title"},Is={class:"_fc-popup-content"},Bs={class:"_fc-popup-footer"};function Ns(t,e,n,r,i,a){const o=l.resolveComponent("van-button"),s=l.resolveComponent("van-popup");return l.openBlock(),l.createBlock(s,l.mergeProps({class:"_fc-popup",closeable:""},t.$attrs,{show:t.visible,"onUpdate:show":e[1]||(e[1]=u=>t.visible=u)}),{default:l.withCtx(()=>[l.createElementVNode("div",js,l.toDisplayString(t.title),1),l.createElementVNode("div",Is,[(l.openBlock(),l.createBlock(l.resolveDynamicComponent(t.Form),{option:t.options,rule:t.formRule,extendOption:!0,api:t.fapi,"onUpdate:api":e[0]||(e[0]=u=>t.fapi=u),"model-value":t.value,onEmitEvent:t.$emit},null,40,["option","rule","api","model-value","onEmitEvent"]))]),l.createElementVNode("div",Bs,[t.footer!==!1?(l.openBlock(),l.createElementBlock(l.Fragment,{key:0},[l.createVNode(o,{block:"",size:"small",type:"primary",class:"fc-clock",onClick:t.handleConfirm},{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("ok")||"\u786E\u5B9A"),1)]),_:1},8,["onClick"]),l.createVNode(o,{block:"",size:"small",class:"fc-clock",style:{"margin-top":"10px"},onClick:t.close},{default:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("close")||"\u5173\u95ED"),1)]),_:1},8,["onClick"])],64)):l.createCommentVNode("",!0)])]),_:1},16,["show"])}const Ls=we(Fs,[["render",Ns]]);function Us(t,e,n){return`[form-create ${t}]: ${e}`+(n?`

rule: `+JSON.stringify(n.getRule?n.getRule():n):"")}function zs(t,e){console.error(Us("err",t,e))}const pi="[[FORM-CREATE-PREFIX-",mi="-FORM-CREATE-SUFFIX]]";function Vn(t){return new Function("return "+t)()}function gi(t,e){if(t&&Te.String(t)&&t.length>4){let n=t.trim(),r=!1;try{if(n.indexOf(mi)>0&&n.indexOf(pi)===0)n=n.replace(mi,"").replace(pi,""),r=!0;else if(n.indexOf("$FN:")===0)n=n.substring(4),r=!0;else if(n.indexOf("$EXEC:")===0)n=n.substring(6),r=!0;else if(n.indexOf("$GLOBAL:")===0){const a=n.substring(8);return n=function(...o){const s=o[0].api.getGlobalEvent(a);if(s)return s.call(this,...o)},n.__json=t,n.__inject=!0,n}else{if(n.indexOf("$FNX:")===0)return n=Vn("function($inject){"+n.substring(5)+"}"),n.__json=t,n.__inject=!0,n;(!e&&n.indexOf("function ")===0&&n!=="function "||!e&&n.indexOf("function(")===0&&n!=="function(")&&(r=!0)}if(!r)return t;let i;try{i=Vn(n)}catch{i=Vn("function "+n)}return i.__json=t,i}catch(i){zs(`\u89E3\u6790\u5931\u8D25:${n}

err: ${i}`);return}}return t}const om="",qs=l.defineComponent({name:"DataTable",emits:["sortChange","handleClick"],props:{column:{type:Array,default:()=>[]},globalDataKey:[String,Object],fetch:Object,data:{type:Array,default:()=>[]},button:Object,index:Boolean,selection:Boolean,page:Object,formCreateInject:Object},data(){return{total:0,loading:!1,unwatch:null,list:[],currentPage:1,id:1,order:"",orderBy:""}},watch:{globalDataKey(){this.initPage()},fetch(){this.globalDataKey||this.initPage()},data(){!this.globalDataKey&&!this.fetch&&this.initPage()},selection(){this.id++},index(){this.id++},page:{handler(){this.initPage(),this.id++},deep:!0},button:{handler(){this.id++},deep:!0}},computed:{filterList(){let t=this.list||[];const e=[];return this.column.forEach(n=>{n.prop&&Array.isArray(n.filter)&&n.filter.length>0&&e.push(r=>n.filter.indexOf(r[n.prop])>-1)}),e.forEach(n=>{t=t.filter(n)}),t}},render(){return l.withDirectives(l.h("div",{class:"_fc-data-table"},[l.h(l.resolveComponent("el-table"),{data:this.filterList,...this.$attrs,key:this.id,ref:"table",onSortChange:t=>{this.$emit("sortChange",t),t.order?(this.orderBy=t.order==="descending"?"DESC":"ASC",this.order=t.prop):(this.orderBy="",this.order=""),this.initPage()}},()=>{const t=this.column.filter(n=>n.hidden!==!0).map(n=>this.makeColumn(n));this.selection&&t.unshift(l.h(l.resolveComponent("el-table-column"),{type:"selection",width:"50px"}));const e=this.makeButtonCol();return e&&t.push(e),this.index&&t.unshift(l.h(l.resolveComponent("el-table-column"),{type:"index",width:"50px"})),t}),this.makePage()]),[[l.resolveDirective("loading"),this.loading]])},methods:{getEl(){return this.$refs.table},deepGet(t,e,n){e=(e||"").split(".");let r=0,i=e.length;for(;t!=null&&r<i;)t=t[e[r++]];return r&&r===i&&t!==void 0?t:n},initPage(){this.loading=!1,this.page&&this.page.open?(this.currentPage=1,this.nextList()):this.globalDataKey||this.fetch?this.fetchData().then(({list:t})=>{this.list=t}):this.list=this.data},btnProps(t,e){const n=t.prop||[],r={type:t.type,size:t.size,round:n.indexOf("round")>-1,link:n.indexOf("link")>-1,plain:n.indexOf("plain")>-1,disabled:n.indexOf("disabled")>-1,onClick:a=>{a.stopPropagation();const o=gi(t.click);try{o&&o(e,this.formCreateInject.api)}catch(s){console.error(s)}this.$emit("handleClick",{name:t.name,scope:e,column:e.row})}},i=gi(t.handle);try{const a=i&&i(r,e,this.formCreateInject.api);typeof a=="boolean"&&(r.disabled=a)}catch(a){console.error(a)}return r},getLimit(){return this.page.props&&this.page.props.pageSize||20},nextList(){if(this.globalDataKey||this.fetch)this.fetchData(!0).then(({list:t,total:e})=>{this.list=t,this.total=e});else{const t=this.data,e=this.getLimit(),n=this.currentPage*e;this.list=t.slice(n-e,n),this.total=t.length}},fetchData(t){return this.unwatch&&this.unwatch(),new Promise(e=>{let n=this.fetch;if(this.globalDataKey){const r=typeof this.globalDataKey=="string"?this.globalDataKey:this.globalDataKey.key;n=this.formCreateInject.api.options.globalData[r]}if(n)if(n.type==="fetch"||!this.globalDataKey){n={...n};let r={};if(t){const a=this.page.props&&this.page.props.pageSize||20,o=this.page.pageField||"page",s=this.page.pageSizeField||"limit";r={[o]:this.currentPage,[s]:a}}if(this.order){const a=this.page.orderField||"order",o=this.page.orderByField||"orderBy";r[a]=this.order,r[o]=this.orderBy}const i=Object.keys(r).map(a=>encodeURIComponent(a)+"="+encodeURIComponent(r[a]),"").join("&");i&&(n.action+=(n.action.indexOf("?")!==-1?"&":"?")+i),this.loading=!0,n.wait=1e3,this.unwatch=this.formCreateInject.api.watchFetch(n,(a,o)=>{this.loading=!1;const s=this.page.totalField,u=this.page.dataField,d=u?this.deepGet(a,u,[]):a;let h=s?this.deepGet(a,s):0;h||(h=d.length||0),e({list:d,total:h})},a=>{console.error(a),this.loading=!1},(a,o)=>{if(o)return this.unwatch&&this.unwatch(),this.unwatch=null,setTimeout(()=>{this.changePage(1)}),!1})}else{let r=n.data||[],i=n.data.length;if(t){const a=this.getLimit(),o=this.currentPage*a;r=r.slice(o-a,o),i=r.length}e({list:r,total:i})}else e({list:[],total:0})})},changePage(t){this.currentPage=t,this.nextList()},makePage(){if(this.page&&this.page.open===!0)return l.h(l.resolveComponent("el-pagination"),{layout:"prev, pager, next",total:this.total,currentPage:this.currentPage,"onUpdate:currentPage":t=>{this.currentPage!==t&&this.changePage(t)},class:this.page.position||"right",...this.page.props||{},pageSize:this.page.props&&this.page.props.pageSize||20})},makeButtonCol(){if(this.button&&this.button.open===!0&&this.button.column)return l.h(l.resolveComponent("el-table-column"),{label:this.button.label||this.formCreateInject.t("operation")||"\u64CD\u4F5C",fixed:this.button.fixed===void 0?"right":this.button.fixed,width:this.button.width||"100px"},{default:t=>this.button.column.filter(e=>e.hidden!==!0).map(e=>l.h(l.resolveComponent("el-button"),this.btnProps(e,t),()=>[e.name]))})},makeColumn(t){return l.h(l.resolveComponent("el-table-column"),{label:t.label,prop:t.prop,width:t.width,align:t.align,className:t.className,fixed:t.fixed,sortable:t.sortable},{default:!t.format||t.format==="default"?void 0:e=>this.makeTd(t,e)})},makeTd(t,e){return t.format==="custom"&&t.render?t.render(e,l.h,l.resolveComponent,this.formCreateInject.api):t.format==="tag"?l.h(l.resolveComponent("el-tag"),{disableTransitions:!0},()=>[this.deepGet(e.row,t.prop,"")]):t.format==="image"?l.h("div",{class:"_fc-data-table-img-list"},(()=>{let n=this.deepGet(e.row,t.prop,"");return n=(Array.isArray(n)?n:[n]).filter(r=>!!r),n.map((r,i)=>l.h(l.resolveComponent("el-image"),{src:r,previewSrcList:n,previewTeleported:!0,initialIndex:i,fit:"cover"}))})()):""+this.deepGet(e.row,t.prop,"")}},created(){this.initPage(),this.$watch(()=>this.data&&this.data.length,()=>{!this.globalDataKey&&!this.fetch&&this.initPage()})}}),am="",Gs={name:"FcTable",props:{label:String,width:[Number,String],border:{type:Boolean,default:!0},borderWidth:String,borderColor:String,rule:{type:Object,default:()=>({row:1,col:1})}},watch:{rule:{handler(){this.initRule(),this.loadRule(),this.tdStyle=this.rule.style||{},this.tdClass=this.rule.class||{}},immediate:!0,deep:!0}},data(){return{tdStyle:{},tdClass:{},lattice:{}}},computed:{tableColor(){const t={};return this.border===!1?t.border="0 none":(this.borderColor&&(t.borderColor=this.borderColor),this.borderWidth&&(t.borderWidth=this.borderWidth)),t}},methods:{initRule(){const t=this.rule;t.style||(t.style={}),t.layout||(t.layout=[]),t.row||(t.row=1),t.col||(t.col=1)},loadRule(){const t=[],e=this.rule||{row:1,col:1};for(let r=0;r<e.row;r++){const i=[];t.push(i);for(let a=0;a<e.col;a++)i.push({rowspan:1,colspan:1,slot:[],show:!0})}[...e.layout||[]].forEach((r,i)=>{if((!r.row||r.row<=0)&&(!r.col||r.col<=0)||!t[r.top]||!t[r.top][r.left]||!t[r.top][r.left].show){e.layout.splice(i,1);return}const a=t[r.top][r.left];a.layout=r;let o=1,s=1;if(r.col&&(o=r.col+r.left>e.col?e.col-r.left:r.col,a.colspan=o),r.row&&(s=r.row+r.top>e.row?e.row-r.top:r.row,a.rowspan=s),s&&o)for(let u=0;u<s;u++){const d=t[r.top+u];if(d)for(let h=0;h<o;h++)!h&&!u||(d[r.left+h]&&(d[r.left+h].show=!1),a.slot.push(`${r.top+u}:${r.left+h}`))}});const n=r=>!!(!r||r.layout||!r.show);t.forEach((r,i)=>{r.forEach((a,o)=>{let s=!1,u=!1;if(a.layout){const d=a.layout.col||1,h=a.layout.row||1;for(let _=0;_<d;_++)if(!t[i+h]||n(t[i+h][o+_])){u=!0;continue}for(let _=0;_<h;_++)if(!t[i+_]||n(t[i+_][o+d])){s=!0;continue}}else s=n(r[o+1]),u=t[i+1]?n(t[i+1][o]):!0;a.right=s,a.bottom=u})}),this.lattice=t}}},Hs={class:"_fc-table"};function Ws(t,e,n,r,i,a){const o=l.resolveComponent("el-col");return l.openBlock(),l.createBlock(o,{span:24},{default:l.withCtx(()=>[l.createElementVNode("div",Hs,[l.createElementVNode("table",{border:"1",cellspacing:"0",cellpadding:"0",style:l.normalizeStyle(a.tableColor)},[(l.openBlock(!0),l.createElementBlock(l.Fragment,null,l.renderList(n.rule.row,(s,u)=>(l.openBlock(),l.createElementBlock("tr",{key:u},[(l.openBlock(!0),l.createElementBlock(l.Fragment,null,l.renderList(n.rule.col,(d,h)=>(l.openBlock(),l.createElementBlock(l.Fragment,{key:`${u}${h}`},[i.lattice[u][h].show?(l.openBlock(),l.createElementBlock("td",l.mergeProps({key:0,ref_for:!0},i.lattice[u][h]?{colspan:i.lattice[u][h].colspan,rowspan:i.lattice[u][h].rowspan}:{},{valign:"top",class:i.tdClass&&i.tdClass[`${u}:${h}`]||"",style:[a.tableColor,i.tdStyle&&i.tdStyle[`${u}:${h}`]||{}]}),[l.renderSlot(t.$slots,`${u}:${h}`),(l.openBlock(!0),l.createElementBlock(l.Fragment,null,l.renderList(i.lattice[u][h].slot,_=>l.renderSlot(t.$slots,`${_}`)),256))],16)):l.createCommentVNode("",!0)],64))),128))]))),128))],4)])]),_:3})}const Xs=we(Gs,[["render",Ws]]),sm="",Ys={name:"TableForm",emits:["change","add","delete","update:modelValue"],props:{formCreateInject:Object,modelValue:{type:Array,default:()=>[]},columns:{type:Array,required:!0,default:()=>[]},filterEmptyColumn:{type:Boolean,default:!0},options:{type:Object,default:()=>l.reactive({submitBtn:!1,resetBtn:!1})},max:Number,disabled:Boolean},watch:{modelValue:{handler(){this.updateTable()},deep:!0},"formCreateInject.preview":function(t){this.emptyRule.children[0].props.colspan=this.columns.length+(t?1:2)}},data(){return{rule:[],trs:[],fapi:{},Form:l.markRaw(this.formCreateInject.form.$form()),copyTrs:"",oldValue:"",emptyRule:{type:"tr",_isEmpty:!0,native:!0,subRule:!0,children:[{type:"td",style:{textAlign:"center"},native:!0,subRule:!0,props:{colspan:this.columns.length+(this.formCreateInject.preview?1:2)},children:[this.formCreateInject.t("dataEmpty")||"\u6682\u65E0\u6570\u636E"]}]}}},methods:{formChange(){this.updateValue()},updateValue(){const t=this.trs.map((n,r)=>({...this.modelValue[r]||{},...this.fapi.getChildrenFormData(n)})).filter(n=>{if(!this.filterEmptyColumn)return!0;if(n==null)return!1;let r=!1;return Object.keys(n).forEach(i=>{r=r||n[i]!==void 0&&n[i]!==""&&n[i]!==null}),r}),e=JSON.stringify(t);e!==this.oldValue&&(this.oldValue=e,this.$emit("update:modelValue",t),this.$emit("change",t))},setRawData(t,e){const n=this.trs[t];this.fapi.setChildrenFormData(n,e,!0)},updateTable(){const t=JSON.stringify(this.modelValue);this.oldValue!==t&&(this.oldValue=t,this.trs=this.trs.splice(0,this.modelValue.length),this.modelValue.length||this.addEmpty(),this.modelValue.forEach((e,n)=>{this.trs[n]||this.addRaw(),this.setRawData(n,e||{})}),this.rule[0].children[1].children=this.trs)},addEmpty(){this.trs.push(this.emptyRule)},delRaw(t){this.disabled||(this.trs.splice(t,1),this.updateValue(),this.trs.length?this.trs.forEach(e=>this.updateRaw(e)):this.addEmpty(),this.$emit("delete",t))},addRaw(t){if(t&&this.disabled)return;const e=this.formCreateInject.form.parseJson(this.copyTrs)[0];this.trs.length===1&&this.trs[0]._isEmpty&&this.trs.splice(0,1),this.trs.push(e),this.updateRaw(e),t&&(this.$emit("add",this.trs.length),this.updateValue())},updateRaw(t){const e=this.trs.indexOf(t);t.children[0].props.innerText=e+1,t.children[t.children.length-1].children[0].props.onClick=()=>{this.delRaw(e)}},loadRule(){const t=[{type:"th",native:!0,class:"_fc-tf-head-idx",props:{innerText:"#"}}];let e=[{type:"td",class:"_fc-tf-idx",native:!0,props:{innerText:"0"}}];this.columns.forEach(n=>{t.push({type:"th",native:!0,style:n.style,class:n.required?"_fc-tf-head-required":"",props:{innerText:n.label||""}}),e.push({type:"td",native:!0,children:[...n.rule||[]]})}),t.push({type:"th",native:!0,class:"_fc-tf-edit fc-clock",props:{innerText:this.formCreateInject.t("operation")||"\u64CD\u4F5C"}}),e.push({type:"td",native:!0,class:"_fc-tf-btn fc-clock",children:[{type:"i",native:!0,class:"fc-icon icon-delete",props:{}}]}),this.copyTrs=this.formCreateInject.form.toJson([{type:"tr",native:!0,subRule:!0,children:e}]),this.rule=[{type:"table",native:!0,class:"_fc-tf-table",props:{border:"1",cellspacing:"0",cellpadding:"0"},children:[{type:"thead",native:!0,children:[{type:"tr",native:!0,children:t}]},{type:"tbody",native:!0,children:this.trs}]}]}},created(){this.loadRule()},mounted(){this.updateTable()}};function Js(t,e,n,r,i,a){const o=l.resolveComponent("el-button");return l.openBlock(),l.createElementBlock("div",{class:l.normalizeClass(["_fc-table-form",{"_fc-disabled":n.disabled}])},[(l.openBlock(),l.createBlock(l.resolveDynamicComponent(i.Form),{option:n.options,rule:i.rule,extendOption:!0,disabled:n.disabled,onChange:a.formChange,api:i.fapi,"onUpdate:api":e[0]||(e[0]=s=>i.fapi=s),onEmitEvent:t.$emit},null,40,["option","rule","disabled","onChange","api","onEmitEvent"])),!n.max||n.max>this.trs.length?(l.openBlock(),l.createBlock(o,{key:0,link:"",type:"primary",class:"fc-clock",onClick:e[1]||(e[1]=s=>a.addRaw(!0))},{default:l.withCtx(()=>[e[2]||(e[2]=l.createElementVNode("i",{class:"fc-icon icon-add-circle",style:{"font-weight":"700"}},null,-1)),l.createTextVNode(" "+l.toDisplayString(n.formCreateInject.t("add")||"\u6DFB\u52A0"),1)]),_:1})):l.createCommentVNode("",!0)],2)}const Ks=we(Ys,[["render",Js]]),um="",Qs={name:"InfiniteTableForm",emits:["change","add","delete","update:modelValue"],props:{formCreateInject:Object,modelValue:{type:Array,default:()=>[]},columns:{type:Array,required:!0,default:()=>[]},options:{type:Object,default:()=>l.reactive({submitBtn:!1,resetBtn:!1})},max:Number,layerMax:{type:Number,default:0},childrenField:String,disabled:Boolean},computed:{preview(){return this.formCreateInject.preview},subField(){return this.childrenField||"children"}},watch:{modelValue(){this.updateTable()},"formCreateInject.preview"(t){this.trs.forEach((e,n)=>{e.children[1]&&(e.children[1].children[0].props.colspan=this.rule[0].children[0].children[0].children.length-(t?1:0)),e.children[0].children[0].children[0].hidden=this.layerMax===1||t&&!(this.modelValue&&this.modelValue[n]&&Array.isArray(this.modelValue[n][this.subField])&&this.modelValue[n][this.subField].length>0)})}},data(){return{rule:[],trs:[],fapi:{},Form:l.markRaw(this.formCreateInject.form.$form()),copyTrs:"",oldValue:""}},methods:{formChange(t,e,n,r,i){i===!1&&this.updateValue()},updateValue(){const t=this.trs.map((n,r)=>{const i={...this.modelValue[r]||{},...this.fapi.getChildrenFormData(n)};return!fi(i,this.subField)&&this.modelValue[r]&&(i[this.subField]=this.modelValue[r][this.subField]),i[this.subField]==null&&delete i[this.subField],i}),e=JSON.stringify(t);e!==this.oldValue&&(this.oldValue=e,this.$emit("update:modelValue",t),this.$emit("change",t))},setRawData(t,e){const n=this.trs[t];this.fapi.setChildrenFormData(n,e,!0)},updateTable(){const t=JSON.stringify(this.modelValue);this.oldValue!==t&&(this.oldValue=t,this.trs=this.trs.splice(0,this.modelValue.length),this.modelValue.length||this.addRaw(),this.modelValue.forEach((e,n)=>{this.trs[n]||this.addRaw(),this.setRawData(n,e||{})}),this.rule[0].children[1].children=this.trs)},delRaw(t){this.disabled||(this.trs.splice(t,1),this.updateValue(),this.trs.length?this.trs.forEach(e=>this.updateRaw(e)):this.addRaw(),this.$emit("delete",t))},addRaw(t){if(t&&this.disabled)return;const e=this.formCreateInject.form.parseJson(this.copyTrs)[0],n={type:"template",subRule:!0,children:[]};n.children.push(e),this.trs.push(n),this.trs.forEach(r=>this.updateRaw(r)),t&&this.$emit("add",this.trs)},updateRaw(t){const e=this.trs.indexOf(t),n=t.children[0];n.children[0].children[0].hidden=this.layerMax===1||this.preview&&!(this.modelValue&&this.modelValue[e]&&Array.isArray(this.modelValue[e][this.subField])&&this.modelValue[e][this.subField].length>0),n.children[0].children[0].props.onClick=r=>{if(this.trs[e].children.length===1){if(this.disabled&&!(this.modelValue&&this.modelValue[e]&&Array.isArray(this.modelValue[e][this.subField])&&this.modelValue[e][this.subField].length>0))return;this.trs[e].children.push({type:"tr",native:!0,display:!0,children:[{type:"td",native:!0,props:{colspan:this.rule[0].children[0].children[0].children.length-(this.preview?1:0)},class:"_fc-itf-sub",children:[{type:"infiniteTableForm",field:this.subField,value:[...this.modelValue[e]&&this.modelValue[e][this.subField]||[]],props:{disabled:this.disabled,layerMax:this.layerMax===0?0:this.layerMax>1?this.layerMax-1:1,max:this.max||0,columns:ot(this.columns),options:ot(this.options)}}]}]})}const i=r.self.children[0]==="-"?"+":"-";r.self.children=[i],this.trs[e].children[1].display=i==="-"},n.children[1].props.innerText=e+1,n.children[n.children.length-1].children[0].props.onClick=()=>{this.delRaw(e)}},loadRule(){const t=[{type:"th",native:!0,class:"_fc-itf-sub-idx"},{type:"th",native:!0,class:"_fc-itf-head-idx",props:{innerText:"#"}}];let e=[{type:"td",class:"_fc-itf-idx",native:!0,children:[{type:"div",hidden:!1,children:["+"],inject:!0,props:{}}]},{type:"td",class:"_fc-itf-idx",native:!0,props:{innerText:"0"}}];this.columns.forEach(n=>{t.push({type:"th",native:!0,class:n.required?"_fc-itf-head-required":"",style:n.style,props:{innerText:n.label||""}}),e.push({type:"td",native:!0,children:[...n.rule||[]]})}),t.push({type:"th",native:!0,class:"_fc-itf-edit fc-clock",props:{innerText:this.formCreateInject.t("operation")||"\u64CD\u4F5C"}}),e.push({type:"td",native:!0,class:"_fc-itf-btn fc-clock",children:[{type:"i",native:!0,class:"fc-icon icon-delete",props:{}}]}),this.copyTrs=this.formCreateInject.form.toJson([{type:"tr",native:!0,children:e}]),this.rule=[{type:"table",native:!0,class:"_fc-itf-table",props:{border:"1",cellspacing:"0",cellpadding:"0"},children:[{type:"thead",native:!0,children:[{type:"tr",native:!0,children:t}]},{type:"tbody",native:!0,children:this.trs}]}]}},created(){this.loadRule()},mounted(){this.updateTable()}};function Zs(t,e,n,r,i,a){const o=l.resolveComponent("el-button");return l.openBlock(),l.createElementBlock("div",{class:l.normalizeClass(["_fc-infinite-table-form",{"_fc-disabled":n.disabled}])},[(l.openBlock(),l.createBlock(l.resolveDynamicComponent(i.Form),{option:n.options,rule:i.rule,extendOption:!0,onChange:a.formChange,disabled:n.disabled,api:i.fapi,"onUpdate:api":e[0]||(e[0]=s=>i.fapi=s),onEmitEvent:t.$emit},null,40,["option","rule","onChange","disabled","api","onEmitEvent"])),!n.max||n.max>this.trs.length?(l.openBlock(),l.createBlock(o,{key:0,link:"",type:"primary",class:"fc-clock",onClick:e[1]||(e[1]=s=>a.addRaw(!0))},{default:l.withCtx(()=>[e[2]||(e[2]=l.createElementVNode("i",{class:"fc-icon icon-add-circle",style:{"font-weight":"700"}},null,-1)),l.createTextVNode(" "+l.toDisplayString(n.formCreateInject.t("add")||"\u6DFB\u52A0"),1)]),_:1})):l.createCommentVNode("",!0)],2)}const eu=we(Qs,[["render",Zs]]),lm="",tu={name:"NestedTableForm",emits:["change","add","delete","update:modelValue"],props:{formCreateInject:Object,modelValue:{type:Array,default:()=>[]},nested:Array,nestedField:String,columns:{type:Array,required:!0,default:()=>[]},options:{type:Object,default:()=>l.reactive({submitBtn:!1,resetBtn:!1})},max:Number,nestedMax:Number,disabled:Boolean},computed:{preview(){return this.formCreateInject.preview}},watch:{modelValue(){this.updateTable()},"formCreateInject.preview"(t){this.trs.forEach(e=>{const n=e.children[1].children[0].props.colspan;e.children[1].children[0].props.colspan=t?n-1:n+1})}},data(){return{rule:[],trs:[],fapi:{},Form:l.markRaw(this.formCreateInject.form.$form()),copyTrs:"",oldValue:""}},methods:{formChange(t,e,n,r,i){i===!1&&this.updateValue()},updateValue(){const t=this.trs.map((n,r)=>({...this.modelValue[r]||{},...this.fapi.getChildrenFormData(n)})).filter(n=>{if(n==null)return!1;let r=!1;return Object.keys(n).forEach(i=>{r||(r=r||n[i]!==void 0&&n[i]!==""&&n[i]!==null&&(Array.isArray(n[i])?!!n[i].length:!0))}),r}),e=JSON.stringify(t);e!==this.oldValue&&(this.oldValue=e,this.$emit("update:modelValue",t),this.$emit("change",t))},setRawData(t,e){const n=this.trs[t];this.fapi.setChildrenFormData(n,e,!0)},updateTable(){const t=JSON.stringify(this.modelValue);this.oldValue!==t&&(this.oldValue=t,this.trs=this.trs.splice(0,this.modelValue.length),this.modelValue.length||this.addRaw(),this.modelValue.forEach((e,n)=>{this.trs[n]||this.addRaw(),this.setRawData(n,e||{})}),this.rule[0].children[1].children=this.trs)},delRaw(t){this.disabled||(this.trs.splice(t,1),this.updateValue(),this.trs.length?this.trs.forEach(e=>this.updateRaw(e)):this.addRaw(),this.$emit("delete",t))},addRaw(t){if(t&&this.disabled)return;const e=this.formCreateInject.form.parseJson(this.copyTrs)[0],n={type:"template",subRule:!0,children:[]};n.children.push(e),n.children.push({type:"tr",native:!0,display:!1,children:[{type:"td",native:!0,props:{colspan:e.children.length-(this.preview?1:0)},class:"_fc-ntf-sub",children:[{type:"TableForm",field:this.nestedField,value:[],props:{disabled:this.disabled,max:this.nestedMax||0,columns:ot(this.nested),options:ot(this.options)}}]}]}),this.trs.push(n),this.updateRaw(n),t&&this.$emit("add",this.trs)},updateRaw(t){const e=this.trs.indexOf(t),n=t.children[0];n.children[0].children[0].props.onClick=r=>{const i=r.self.children[0]==="-"?"+":"-";r.self.children=[i],this.trs[e].children[1].display=i==="-"},n.children[1].props.innerText=e+1,n.children[n.children.length-1].children[0].props.onClick=()=>{this.delRaw(e)}},loadRule(){const t=[{type:"th",native:!0,class:"_fc-ntf-sub-idx"},{type:"th",native:!0,class:"_fc-ntf-head-idx",props:{innerText:"#"}}];let e=[{type:"td",class:"_fc-ntf-idx",native:!0,children:[{type:"div",hidden:!1,children:["+"],inject:!0,props:{}}]},{type:"td",class:"_fc-ntf-idx",native:!0,props:{innerText:"0"}}];this.columns.forEach(n=>{t.push({type:"th",native:!0,class:n.required?"_fc-ntf-head-required":"",style:n.style,props:{innerText:n.label||""}}),e.push({type:"td",native:!0,children:[...n.rule||[]]})}),t.push({type:"th",native:!0,class:"_fc-ntf-edit fc-clock",props:{innerText:this.formCreateInject.t("operation")||"\u64CD\u4F5C"}}),e.push({type:"td",native:!0,class:"_fc-ntf-btn fc-clock",children:[{type:"i",native:!0,class:"fc-icon icon-delete",props:{}}]}),this.copyTrs=this.formCreateInject.form.toJson([{type:"tr",native:!0,children:e}]),this.rule=[{type:"table",native:!0,class:"_fc-ntf-table",props:{border:"1",cellspacing:"0",cellpadding:"0"},children:[{type:"thead",native:!0,children:[{type:"tr",native:!0,children:t}]},{type:"tbody",native:!0,children:this.trs}]}]}},created(){this.loadRule()},mounted(){this.updateTable()}};function nu(t,e,n,r,i,a){const o=l.resolveComponent("el-button");return l.openBlock(),l.createElementBlock("div",{class:l.normalizeClass(["_fc-nested-table-form",{"_fc-disabled":n.disabled}])},[(l.openBlock(),l.createBlock(l.resolveDynamicComponent(i.Form),{option:n.options,rule:i.rule,extendOption:!0,onChange:a.formChange,disabled:n.disabled,api:i.fapi,"onUpdate:api":e[0]||(e[0]=s=>i.fapi=s),onEmitEvent:t.$emit},null,40,["option","rule","onChange","disabled","api","onEmitEvent"])),!n.max||n.max>this.trs.length?(l.openBlock(),l.createBlock(o,{key:0,link:"",type:"primary",class:"fc-clock",onClick:e[1]||(e[1]=s=>a.addRaw(!0))},{default:l.withCtx(()=>[e[2]||(e[2]=l.createElementVNode("i",{class:"fc-icon icon-add-circle",style:{"font-weight":"700"}},null,-1)),l.createTextVNode(" "+l.toDisplayString(n.formCreateInject.t("add")||"\u6DFB\u52A0"),1)]),_:1})):l.createCommentVNode("",!0)],2)}const ru=we(tu,[["render",nu]]),cm="",iu=l.defineComponent({name:"FcCell"}),ou={ref:"cell",class:"_fc-cell"};function au(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",ou,[l.renderSlot(t.$slots,"default")],512)}const su=we(iu,[["render",au]]),uu=l.defineComponent({name:"FcValue",props:["modelValue"],watch:{modelValue(t){this.$emit("change",t)}}}),lu={class:"_fc-value"};function cu(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",lu,l.toDisplayString(t.modelValue),1)}const fu=we(uu,[["render",cu]]),du=l.defineComponent({name:"FcSlot",inheritAttrs:!1,inject:["parentFC"],props:{name:String,formCreateInject:Object},computed:{slotName(){return this.name||"block_default"},slotArg(){const{rule:t,preview:e,api:n}=this.formCreateInject,r=t.__fc__.prop;return{rule:t,prop:r,preview:e,api:n,model:r.model||{}}}},render(){const t=this.getSlot();return l.createVNode(l.Fragment,{},t?[t(this.slotArg)]:[])},methods:{getSlot(){const t=e=>{if(e){let n=e.slots[this.slotName];return n||t(e.setupState.parent)}};return t(this.parentFC)}}}),hu=l.defineComponent({name:"FcJson",inheritAttrs:!1,props:{rule:[Array,String,Object],type:String,disabled:Boolean,expand:Number,button:{type:Boolean,default:!0},max:{type:Number,default:0},min:{type:Number,default:0},sortBtn:{type:Boolean,default:!0},modelValue:[Object,Array],formCreateInject:Object},data(){return{fcSubForm:l.shallowRef(this.formCreateInject.form.component("fcSubForm")),fcGroup:l.shallowRef(this.formCreateInject.form.component("fcGroup")),uni:0,formRule:[],formOptions:{submitBtn:!1,resetBtn:!1}}},watch:{rule(){this.uni++,this.loadRule()},type(){this.loadRule()}},render(){var t,e;if(this.rule)return this.type==="object"?l.createVNode(this.fcSubForm,{key:2,...this.$attrs,modelValue:this.modelValue,"onUpdate:modelValue":n=>{this.$emit("update:modelValue",n)},disabled:this.disabled,formCreateInject:this.formCreateInject,rule:this.formRule,options:this.formOptions}):this.type==="array"?l.createVNode(this.fcGroup,{key:3,...this.$attrs,modelValue:this.modelValue,"onUpdate:modelValue":n=>{this.$emit("update:modelValue",n)},sortBtn:this.sortBtn,min:this.min,max:this.max,expand:this.expand,button:this.button,disabled:this.disabled,formCreateInject:this.formCreateInject,rule:this.formRule,options:this.formOptions}):l.createVNode(l.Fragment,{key:this.uni},[(e=(t=this.$slots).default)==null?void 0:e.call(t)])},methods:{loadRule(){let t=ot(this.rule);typeof t=="string"&&(t=this.formCreateInject.form.parseJson(t)),Array.isArray(t)?this.formRule=t:typeof t=="object"&&(this.formRule=t.rule||[],this.formOptions={submitBtn:!1,resetBtn:!1,...t.options||{}}),t!=null?["array","object"].indexOf(this.type)===-1&&(this.formCreateInject.rule.children=[{type:"template",_fc_drag_skip:!0,children:this.formRule}]):this.formCreateInject.rule.children=[]}},created(){this.rule&&this.loadRule()}}),fm="",pu=l.defineComponent({name:"fcInlineForm"}),mu={class:"_fc-line-form"};function gu(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",mu,[l.renderSlot(t.$slots,"default")])}const vu=we(pu,[["render",gu]]);var vi=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function yu(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}var yi={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(vi,function(){var n=function(){},r={},i={},a={};function o(y,x){y=y.push?y:[y];var S=[],C=y.length,F=C,q,U,L,Y;for(q=function(ne,Z){Z.length&&S.push(ne),F--,F||x(S)};C--;){if(U=y[C],L=i[U],L){q(U,L);continue}Y=a[U]=a[U]||[],Y.push(q)}}function s(y,x){if(!!y){var S=a[y];if(i[y]=x,!!S)for(;S.length;)S[0](y,x),S.splice(0,1)}}function u(y,x){y.call&&(y={success:y}),x.length?(y.error||n)(x):(y.success||n)(y)}function d(y,x,S,C){var F=document,q=S.async,U=(S.numRetries||0)+1,L=S.before||n,Y=y.replace(/[\?|#].*$/,""),ne=y.replace(/^(css|img|module|nomodule)!/,""),Z,K,X;if(C=C||0,/(^css!|\.css$)/.test(Y))X=F.createElement("link"),X.rel="stylesheet",X.href=ne,Z="hideFocus"in X,Z&&X.relList&&(Z=0,X.rel="preload",X.as="style");else if(/(^img!|\.(png|gif|jpg|svg|webp)$)/.test(Y))X=F.createElement("img"),X.src=ne;else if(X=F.createElement("script"),X.src=ne,X.async=q===void 0?!0:q,K="noModule"in X,/^module!/.test(Y)){if(!K)return x(y,"l");X.type="module"}else if(/^nomodule!/.test(Y)&&K)return x(y,"l");X.onload=X.onerror=X.onbeforeload=function(ce){var se=ce.type[0];if(Z)try{X.sheet.cssText.length||(se="e")}catch(re){re.code!=18&&(se="e")}if(se=="e"){if(C+=1,C<U)return d(y,x,S,C)}else if(X.rel=="preload"&&X.as=="style")return X.rel="stylesheet";x(y,se,ce.defaultPrevented)},L(y,X)!==!1&&F.head.appendChild(X)}function h(y,x,S){y=y.push?y:[y];var C=y.length,F=C,q=[],U,L;for(U=function(Y,ne,Z){if(ne=="e"&&q.push(Y),ne=="b")if(Z)q.push(Y);else return;C--,C||x(q)},L=0;L<F;L++)d(y[L],U,S)}function _(y,x,S){var C,F;if(x&&x.trim&&(C=x),F=(C?S:x)||{},C){if(C in r)throw"LoadJS";r[C]=!0}function q(U,L){h(y,function(Y){u(F,Y),U&&u({success:U,error:L},Y),s(C,Y)},F)}if(F.returnPromise)return new Promise(q);q()}return _.ready=function(x,S){return o(x,function(C){u(S,C)}),_},_.done=function(x){s(x,[])},_.reset=function(){r={},i={},a={}},_.isDefined=function(x){return x in r},_})})(yi);const Xe=yi.exports;function _u(t,e){var n=null;return function(...r){n!==null&&clearTimeout(n),n=setTimeout(()=>t.call(this,...r),e)}}const dm="",bu=l.defineComponent({name:"FcEcharts",data(){return{chart:null}},emits:["beforeLoad","loaded"],props:{title:String,value:Number,min:Number,max:Number,name:String,valueFormat:String,subtitle:String,funnelSort:String,config:Object,data:Array,indicator:Array,smooth:Boolean,stripe:Boolean,showLegend:{type:Boolean,default:!0},loadOptions:{type:Function,default:()=>{}},showSeriesLabel:Boolean,type:String,pieType:String,stack:Boolean,barBackgroundColor:String},watch:{$props:{handler:_u(function(){this.load()},600),deep:!0}},methods:{getSeries(){var n;const t={type:"line",stack:this.stack?"Total":"",smooth:this.smooth,showBackground:!1,label:{show:this.showSeriesLabel,position:this.stripe?"inside":"top"}};this.type==="area"?(t.areaStyle={},t.emphasis={focus:"series"}):this.type==="bar"&&(t.type="bar",this.barBackgroundColor&&(t.showBackground=!0,t.backgroundStyle={color:this.barBackgroundColor}));let e=((n=this.config)==null?void 0:n.series)||[];return e.length?(typeof e[0]!="object"&&(e=[{data:e}]),e=e.map(r=>({...t,...r})),e):[]},getTooltip(){const t={trigger:"axis",valueFormat:void 0};return this.valueFormat&&(t.valueFormatter=e=>this.valueFormat?this.valueFormat.replaceAll("{value}",e):e),this.type==="bar"&&(t.axisPointer={type:"shadow"}),t},getAxis(){var t,e;return this.stripe?{yAxis:{type:"category",boundaryGap:this.type==="bar",data:((e=this.config)==null?void 0:e.category)||[]},xAxis:{type:"value"}}:{xAxis:{type:"category",boundaryGap:this.type==="bar",data:(t=this.config)==null?void 0:t.category},yAxis:{type:"value"}}},getDefOptions(){return{title:{text:this.title,subtext:this.subtitle},tooltip:this.getTooltip(),legend:{left:"right",show:this.showLegend},grid:{left:"20px",right:"20px",bottom:"20px",containLabel:!0},...this.getAxis(),series:this.getSeries()}},getPieOptions(){const t={radius:"50%",center:"50%",startAngle:0,avoidLabelOverlap:!0,labelLine:{show:!0},emphasis:{itemStyle:{shadowBlur:10,shadowOffsetX:0,shadowColor:"rgba(0, 0, 0, 0.5)"}}};return this.pieType==="doughnut"?(t.radius=["40%","70%"],t.avoidLabelOverlap=!1):this.pieType==="half-doughnut"&&(t.radius=["40%","70%"],t.center=["50%","70%"],t.startAngle=180,t.endAngle=360),{title:{text:this.title,subtext:this.subtitle,left:"left"},tooltip:{trigger:"item"},legend:{left:"right",show:this.showLegend},series:[{type:"pie",data:this.data,...t}]}},getGaugeOptions(){return{title:{text:this.title,subtext:this.subtitle,left:"center"},series:[{name:"Pressure",type:"gauge",min:this.min||0,max:this.max||60,progress:{show:!0},detail:{valueAnimation:!0,formatter:"{value}"},data:[{value:this.value,name:this.name}]}]}},getRadarOptions(){return{title:{text:this.title,subtext:this.subtitle,left:"left"},tooltip:{trigger:"axis"},legend:{left:"right",show:this.showLegend},radar:{indicator:this.indicator},series:[{type:"radar",tooltip:{trigger:"item"},data:this.data}]}},getScatterOptions(){return{title:{text:this.title,subtext:this.subtitle,left:"left"},tooltip:{trigger:"axis"},legend:{left:"right",show:!0},xAxis:{scale:!0},yAxis:{scale:!0},grid:{left:"20px",right:"20px",bottom:"20px",containLabel:!0},series:(this.data||[]).map(t=>Array.isArray(t)?{type:"scatter",data:t}:{type:"scatter",...t})}},getFunnelOptions(){return{title:{text:this.title,subtext:this.subtitle,left:"left"},tooltip:{trigger:"item"},legend:{left:"right",show:this.showLegend},series:[{name:"Funnel",type:"funnel",left:"10%",top:"40px",bottom:"20px",width:"80%",min:0,max:Math.max(...(this.data||[]).map(t=>t.value)),minSize:"0%",maxSize:"100%",sort:this.funnelSort||"descending",gap:2,label:{show:!0,position:"inside"},labelLine:{length:10,lineStyle:{width:1,type:"solid"}},itemStyle:{borderColor:"#fff",borderWidth:1},emphasis:{label:{}},data:this.data}]}},load(){this.$nextTick(()=>{Xe.ready("echarts",()=>{this.chart=l.markRaw(window.echarts.init(this.$refs.chart));let t;if(this.type==="pie")t=this.getPieOptions();else if(this.type==="funnel")t=this.getFunnelOptions();else if(this.type==="gauge")t=this.getGaugeOptions();else if(this.type==="radar")t=this.getRadarOptions();else if(this.type==="scatter")t=this.getScatterOptions();else if(this.type==="custom"){if(t=this.loadOptions(this.config,this.chart)||{},typeof t.then=="function"){t.then(e=>{this.$emit("beforeLoad",this.chart,e),this.chart.setOption(e),this.$emit("loaded",this.chart,e)});return}}else t=this.getDefOptions();this.$emit("beforeLoad",this.chart,t),this.chart.setOption(t),this.$emit("loaded",this.chart,t)})})}},created(){window.echarts?Xe.done("echarts"):Xe.isDefined("echarts")||Xe(["https://static.form-create.com/res/echarts.min.js"],"echarts")},mounted(){this.load()}}),wu={class:"_fc-echarts",ref:"chart"};function $u(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",wu,null,512)}const Ou=we(bu,[["render",$u]]);/*!
 * Signature Pad v5.0.4 | https://github.com/szimek/signature_pad
 * (c) 2024 Szymon Nowak | Released under the MIT license
 */class Wt{constructor(e,n,r,i){if(isNaN(e)||isNaN(n))throw new Error(`Point is invalid: (${e}, ${n})`);this.x=+e,this.y=+n,this.pressure=r||0,this.time=i||Date.now()}distanceTo(e){return Math.sqrt(Math.pow(this.x-e.x,2)+Math.pow(this.y-e.y,2))}equals(e){return this.x===e.x&&this.y===e.y&&this.pressure===e.pressure&&this.time===e.time}velocityFrom(e){return this.time!==e.time?this.distanceTo(e)/(this.time-e.time):0}}class Mn{static fromPoints(e,n){const r=this.calculateControlPoints(e[0],e[1],e[2]).c2,i=this.calculateControlPoints(e[1],e[2],e[3]).c1;return new Mn(e[1],r,i,e[2],n.start,n.end)}static calculateControlPoints(e,n,r){const i=e.x-n.x,a=e.y-n.y,o=n.x-r.x,s=n.y-r.y,u={x:(e.x+n.x)/2,y:(e.y+n.y)/2},d={x:(n.x+r.x)/2,y:(n.y+r.y)/2},h=Math.sqrt(i*i+a*a),_=Math.sqrt(o*o+s*s),y=u.x-d.x,x=u.y-d.y,S=h+_==0?0:_/(h+_),C={x:d.x+y*S,y:d.y+x*S},F=n.x-C.x,q=n.y-C.y;return{c1:new Wt(u.x+F,u.y+q),c2:new Wt(d.x+F,d.y+q)}}constructor(e,n,r,i,a,o){this.startPoint=e,this.control2=n,this.control1=r,this.endPoint=i,this.startWidth=a,this.endWidth=o}length(){let n=0,r,i;for(let a=0;a<=10;a+=1){const o=a/10,s=this.point(o,this.startPoint.x,this.control1.x,this.control2.x,this.endPoint.x),u=this.point(o,this.startPoint.y,this.control1.y,this.control2.y,this.endPoint.y);if(a>0){const d=s-r,h=u-i;n+=Math.sqrt(d*d+h*h)}r=s,i=u}return n}point(e,n,r,i,a){return n*(1-e)*(1-e)*(1-e)+3*r*(1-e)*(1-e)*e+3*i*(1-e)*e*e+a*e*e*e}}class Su{constructor(){try{this._et=new EventTarget}catch{this._et=document}}addEventListener(e,n,r){this._et.addEventListener(e,n,r)}dispatchEvent(e){return this._et.dispatchEvent(e)}removeEventListener(e,n,r){this._et.removeEventListener(e,n,r)}}function Eu(t,e=250){let n=0,r=null,i,a,o;const s=()=>{n=Date.now(),r=null,i=t.apply(a,o),r||(a=null,o=[])};return function(...d){const h=Date.now(),_=e-(h-n);return a=this,o=d,_<=0||_>e?(r&&(clearTimeout(r),r=null),n=h,i=t.apply(a,o),r||(a=null,o=[])):r||(r=window.setTimeout(s,_)),i}}class Xt extends Su{constructor(e,n={}){var r,i,a;super(),this.canvas=e,this._drawingStroke=!1,this._isEmpty=!0,this._lastPoints=[],this._data=[],this._lastVelocity=0,this._lastWidth=0,this._handleMouseDown=o=>{!this._isLeftButtonPressed(o,!0)||this._drawingStroke||this._strokeBegin(this._pointerEventToSignatureEvent(o))},this._handleMouseMove=o=>{if(!this._isLeftButtonPressed(o,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(o),!1);return}this._strokeMoveUpdate(this._pointerEventToSignatureEvent(o))},this._handleMouseUp=o=>{this._isLeftButtonPressed(o)||this._strokeEnd(this._pointerEventToSignatureEvent(o))},this._handleTouchStart=o=>{o.targetTouches.length!==1||this._drawingStroke||(o.cancelable&&o.preventDefault(),this._strokeBegin(this._touchEventToSignatureEvent(o)))},this._handleTouchMove=o=>{if(o.targetTouches.length===1){if(o.cancelable&&o.preventDefault(),!this._drawingStroke){this._strokeEnd(this._touchEventToSignatureEvent(o),!1);return}this._strokeMoveUpdate(this._touchEventToSignatureEvent(o))}},this._handleTouchEnd=o=>{o.targetTouches.length===0&&(o.cancelable&&o.preventDefault(),this.canvas.removeEventListener("touchmove",this._handleTouchMove),this._strokeEnd(this._touchEventToSignatureEvent(o)))},this._handlePointerDown=o=>{!o.isPrimary||!this._isLeftButtonPressed(o)||this._drawingStroke||(o.preventDefault(),this._strokeBegin(this._pointerEventToSignatureEvent(o)))},this._handlePointerMove=o=>{if(!!o.isPrimary){if(!this._isLeftButtonPressed(o,!0)||!this._drawingStroke){this._strokeEnd(this._pointerEventToSignatureEvent(o),!1);return}o.preventDefault(),this._strokeMoveUpdate(this._pointerEventToSignatureEvent(o))}},this._handlePointerUp=o=>{!o.isPrimary||this._isLeftButtonPressed(o)||(o.preventDefault(),this._strokeEnd(this._pointerEventToSignatureEvent(o)))},this.velocityFilterWeight=n.velocityFilterWeight||.7,this.minWidth=n.minWidth||.5,this.maxWidth=n.maxWidth||2.5,this.throttle=(r=n.throttle)!==null&&r!==void 0?r:16,this.minDistance=(i=n.minDistance)!==null&&i!==void 0?i:5,this.dotSize=n.dotSize||0,this.penColor=n.penColor||"black",this.backgroundColor=n.backgroundColor||"rgba(0,0,0,0)",this.compositeOperation=n.compositeOperation||"source-over",this.canvasContextOptions=(a=n.canvasContextOptions)!==null&&a!==void 0?a:{},this._strokeMoveUpdate=this.throttle?Eu(Xt.prototype._strokeUpdate,this.throttle):Xt.prototype._strokeUpdate,this._ctx=e.getContext("2d",this.canvasContextOptions),this.clear(),this.on()}clear(){const{_ctx:e,canvas:n}=this;e.fillStyle=this.backgroundColor,e.clearRect(0,0,n.width,n.height),e.fillRect(0,0,n.width,n.height),this._data=[],this._reset(this._getPointGroupOptions()),this._isEmpty=!0}fromDataURL(e,n={}){return new Promise((r,i)=>{const a=new Image,o=n.ratio||window.devicePixelRatio||1,s=n.width||this.canvas.width/o,u=n.height||this.canvas.height/o,d=n.xOffset||0,h=n.yOffset||0;this._reset(this._getPointGroupOptions()),a.onload=()=>{this._ctx.drawImage(a,d,h,s,u),r()},a.onerror=_=>{i(_)},a.crossOrigin="anonymous",a.src=e,this._isEmpty=!1})}toDataURL(e="image/png",n){switch(e){case"image/svg+xml":return typeof n!="object"&&(n=void 0),`data:image/svg+xml;base64,${btoa(this.toSVG(n))}`;default:return typeof n!="number"&&(n=void 0),this.canvas.toDataURL(e,n)}}on(){this.canvas.style.touchAction="none",this.canvas.style.msTouchAction="none",this.canvas.style.userSelect="none";const e=/Macintosh/.test(navigator.userAgent)&&"ontouchstart"in document;window.PointerEvent&&!e?this._handlePointerEvents():(this._handleMouseEvents(),"ontouchstart"in window&&this._handleTouchEvents())}off(){this.canvas.style.touchAction="auto",this.canvas.style.msTouchAction="auto",this.canvas.style.userSelect="auto",this.canvas.removeEventListener("pointerdown",this._handlePointerDown),this.canvas.removeEventListener("mousedown",this._handleMouseDown),this.canvas.removeEventListener("touchstart",this._handleTouchStart),this._removeMoveUpEventListeners()}_getListenerFunctions(){var e;const n=window.document===this.canvas.ownerDocument?window:(e=this.canvas.ownerDocument.defaultView)!==null&&e!==void 0?e:this.canvas.ownerDocument;return{addEventListener:n.addEventListener.bind(n),removeEventListener:n.removeEventListener.bind(n)}}_removeMoveUpEventListeners(){const{removeEventListener:e}=this._getListenerFunctions();e("pointermove",this._handlePointerMove),e("pointerup",this._handlePointerUp),e("mousemove",this._handleMouseMove),e("mouseup",this._handleMouseUp),e("touchmove",this._handleTouchMove),e("touchend",this._handleTouchEnd)}isEmpty(){return this._isEmpty}fromData(e,{clear:n=!0}={}){n&&this.clear(),this._fromData(e,this._drawCurve.bind(this),this._drawDot.bind(this)),this._data=this._data.concat(e)}toData(){return this._data}_isLeftButtonPressed(e,n){return n?e.buttons===1:(e.buttons&1)===1}_pointerEventToSignatureEvent(e){return{event:e,type:e.type,x:e.clientX,y:e.clientY,pressure:"pressure"in e?e.pressure:0}}_touchEventToSignatureEvent(e){const n=e.changedTouches[0];return{event:e,type:e.type,x:n.clientX,y:n.clientY,pressure:n.force}}_getPointGroupOptions(e){return{penColor:e&&"penColor"in e?e.penColor:this.penColor,dotSize:e&&"dotSize"in e?e.dotSize:this.dotSize,minWidth:e&&"minWidth"in e?e.minWidth:this.minWidth,maxWidth:e&&"maxWidth"in e?e.maxWidth:this.maxWidth,velocityFilterWeight:e&&"velocityFilterWeight"in e?e.velocityFilterWeight:this.velocityFilterWeight,compositeOperation:e&&"compositeOperation"in e?e.compositeOperation:this.compositeOperation}}_strokeBegin(e){if(!this.dispatchEvent(new CustomEvent("beginStroke",{detail:e,cancelable:!0})))return;const{addEventListener:r}=this._getListenerFunctions();switch(e.event.type){case"mousedown":r("mousemove",this._handleMouseMove),r("mouseup",this._handleMouseUp);break;case"touchstart":r("touchmove",this._handleTouchMove),r("touchend",this._handleTouchEnd);break;case"pointerdown":r("pointermove",this._handlePointerMove),r("pointerup",this._handlePointerUp);break}this._drawingStroke=!0;const i=this._getPointGroupOptions(),a=Object.assign(Object.assign({},i),{points:[]});this._data.push(a),this._reset(i),this._strokeUpdate(e)}_strokeUpdate(e){if(!this._drawingStroke)return;if(this._data.length===0){this._strokeBegin(e);return}this.dispatchEvent(new CustomEvent("beforeUpdateStroke",{detail:e}));const n=this._createPoint(e.x,e.y,e.pressure),r=this._data[this._data.length-1],i=r.points,a=i.length>0&&i[i.length-1],o=a?n.distanceTo(a)<=this.minDistance:!1,s=this._getPointGroupOptions(r);if(!a||!(a&&o)){const u=this._addPoint(n,s);a?u&&this._drawCurve(u,s):this._drawDot(n,s),i.push({time:n.time,x:n.x,y:n.y,pressure:n.pressure})}this.dispatchEvent(new CustomEvent("afterUpdateStroke",{detail:e}))}_strokeEnd(e,n=!0){this._removeMoveUpEventListeners(),this._drawingStroke&&(n&&this._strokeUpdate(e),this._drawingStroke=!1,this.dispatchEvent(new CustomEvent("endStroke",{detail:e})))}_handlePointerEvents(){this._drawingStroke=!1,this.canvas.addEventListener("pointerdown",this._handlePointerDown)}_handleMouseEvents(){this._drawingStroke=!1,this.canvas.addEventListener("mousedown",this._handleMouseDown)}_handleTouchEvents(){this.canvas.addEventListener("touchstart",this._handleTouchStart)}_reset(e){this._lastPoints=[],this._lastVelocity=0,this._lastWidth=(e.minWidth+e.maxWidth)/2,this._ctx.fillStyle=e.penColor,this._ctx.globalCompositeOperation=e.compositeOperation}_createPoint(e,n,r){const i=this.canvas.getBoundingClientRect();return new Wt(e-i.left,n-i.top,r,new Date().getTime())}_addPoint(e,n){const{_lastPoints:r}=this;if(r.push(e),r.length>2){r.length===3&&r.unshift(r[0]);const i=this._calculateCurveWidths(r[1],r[2],n),a=Mn.fromPoints(r,i);return r.shift(),a}return null}_calculateCurveWidths(e,n,r){const i=r.velocityFilterWeight*n.velocityFrom(e)+(1-r.velocityFilterWeight)*this._lastVelocity,a=this._strokeWidth(i,r),o={end:a,start:this._lastWidth};return this._lastVelocity=i,this._lastWidth=a,o}_strokeWidth(e,n){return Math.max(n.maxWidth/(e+1),n.minWidth)}_drawCurveSegment(e,n,r){const i=this._ctx;i.moveTo(e,n),i.arc(e,n,r,0,2*Math.PI,!1),this._isEmpty=!1}_drawCurve(e,n){const r=this._ctx,i=e.endWidth-e.startWidth,a=Math.ceil(e.length())*2;r.beginPath(),r.fillStyle=n.penColor;for(let o=0;o<a;o+=1){const s=o/a,u=s*s,d=u*s,h=1-s,_=h*h,y=_*h;let x=y*e.startPoint.x;x+=3*_*s*e.control1.x,x+=3*h*u*e.control2.x,x+=d*e.endPoint.x;let S=y*e.startPoint.y;S+=3*_*s*e.control1.y,S+=3*h*u*e.control2.y,S+=d*e.endPoint.y;const C=Math.min(e.startWidth+d*i,n.maxWidth);this._drawCurveSegment(x,S,C)}r.closePath(),r.fill()}_drawDot(e,n){const r=this._ctx,i=n.dotSize>0?n.dotSize:(n.minWidth+n.maxWidth)/2;r.beginPath(),this._drawCurveSegment(e.x,e.y,i),r.closePath(),r.fillStyle=n.penColor,r.fill()}_fromData(e,n,r){for(const i of e){const{points:a}=i,o=this._getPointGroupOptions(i);if(a.length>1)for(let s=0;s<a.length;s+=1){const u=a[s],d=new Wt(u.x,u.y,u.pressure,u.time);s===0&&this._reset(o);const h=this._addPoint(d,o);h&&n(h,o)}else this._reset(o),r(a[0],o)}}toSVG({includeBackgroundColor:e=!1}={}){const n=this._data,r=Math.max(window.devicePixelRatio||1,1),i=0,a=0,o=this.canvas.width/r,s=this.canvas.height/r,u=document.createElementNS("http://www.w3.org/2000/svg","svg");if(u.setAttribute("xmlns","http://www.w3.org/2000/svg"),u.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),u.setAttribute("viewBox",`${i} ${a} ${o} ${s}`),u.setAttribute("width",o.toString()),u.setAttribute("height",s.toString()),e&&this.backgroundColor){const d=document.createElement("rect");d.setAttribute("width","100%"),d.setAttribute("height","100%"),d.setAttribute("fill",this.backgroundColor),u.appendChild(d)}return this._fromData(n,(d,{penColor:h})=>{const _=document.createElement("path");if(!isNaN(d.control1.x)&&!isNaN(d.control1.y)&&!isNaN(d.control2.x)&&!isNaN(d.control2.y)){const y=`M ${d.startPoint.x.toFixed(3)},${d.startPoint.y.toFixed(3)} C ${d.control1.x.toFixed(3)},${d.control1.y.toFixed(3)} ${d.control2.x.toFixed(3)},${d.control2.y.toFixed(3)} ${d.endPoint.x.toFixed(3)},${d.endPoint.y.toFixed(3)}`;_.setAttribute("d",y),_.setAttribute("stroke-width",(d.endWidth*2.25).toFixed(3)),_.setAttribute("stroke",h),_.setAttribute("fill","none"),_.setAttribute("stroke-linecap","round"),u.appendChild(_)}},(d,{penColor:h,dotSize:_,minWidth:y,maxWidth:x})=>{const S=document.createElement("circle"),C=_>0?_:(y+x)/2;S.setAttribute("r",C.toString()),S.setAttribute("cx",d.x.toString()),S.setAttribute("cy",d.y.toString()),S.setAttribute("fill",h),u.appendChild(S)}),u.outerHTML}}const hm="",Cu=l.defineComponent({name:"SignaturePad",emits:["update:modelValue","change","remove"],data(){return{visible:!1,isEmpty:!0,signaturePad:null}},props:{modelValue:String,penColor:String,formCreateInject:Object},watch:{visible(t){t?(this.isEmpty=!0,this.$nextTick(()=>{this.signaturePad=l.markRaw(new Xt(this.$refs.pad,{penColor:this.penColor})),this.signaturePad.addEventListener("endStroke",()=>{this.isEmpty=this.signaturePad.isEmpty()})})):(this.signaturePad.off(),this.signaturePad=null)}},methods:{clear(){this.signaturePad.clear(),this.isEmpty=!0},submit(){const t=this.signaturePad.toDataURL();this.updateValue(t),this.visible=!1},updateValue(t){this.$emit("update:modelValue",t),this.$emit("change",t)},remove(){this.updateValue(""),this.$emit("remove")}}}),Au={class:"_fc-m-signature"},xu={key:0,class:"_fc-m-signature-preview"},Du=["src"],Pu={class:"_fc-m-signature-pad",ref:"pad",width:"320px",height:"145px"};function Ru(t,e,n,r,i,a){const o=l.resolveComponent("van-dialog");return l.openBlock(),l.createElementBlock("div",Au,[t.modelValue?(l.openBlock(),l.createElementBlock("div",xu,[l.createElementVNode("i",{class:"fc-icon icon-delete2",onClick:e[0]||(e[0]=(...s)=>t.remove&&t.remove(...s))}),l.createElementVNode("img",{src:t.modelValue,alt:"signature"},null,8,Du)])):(l.openBlock(),l.createElementBlock("div",{key:1,class:"_fc-m-signature-btn",onClick:e[1]||(e[1]=s=>t.visible=!0)},[e[4]||(e[4]=l.createElementVNode("i",{class:"fc-icon icon-edit2"},null,-1)),l.createTextVNode(" "+l.toDisplayString(t.formCreateInject.t("signaturePadTip")||"\u70B9\u51FB\u6DFB\u52A0\u624B\u5199\u7B7E\u540D"),1)])),l.createVNode(o,{show:t.visible,"onUpdate:show":e[3]||(e[3]=s=>t.visible=s),class:"_fc-m-signature-dialog",onConfirm:t.submit,onCancel:t.clear,"confirm-button-text":t.formCreateInject.t("ok")||"\u786E\u5B9A","cancel-button-text":t.formCreateInject.t("reset")||"\u91CD\u7F6E","confirm-button-disabled":t.isEmpty},{title:l.withCtx(()=>[l.createTextVNode(l.toDisplayString(t.formCreateInject.t("signaturePadTitle")||"\u8BF7\u5728\u865A\u7EBF\u6846\u5185\u4E66\u5199")+" ",1),l.createElementVNode("i",{class:"fc-icon icon-add2",onClick:e[2]||(e[2]=s=>t.visible=!1)})]),default:l.withCtx(()=>[l.createElementVNode("canvas",Pu,null,512)]),_:1},8,["show","onConfirm","onCancel","confirm-button-text","cancel-button-text","confirm-button-disabled"])])}const ku=we(Cu,[["render",Ru]]);function Tn(t,e,n){for(var r=[],i=Math.max(t.length,e.length),a=0,o=0;o<i||a;){var s=o<t.length?t[o]:0,u=o<e.length?e[o]:0,d=a+s+u;r.push(d%n),a=Math.floor(d/n),o++}return r}function _i(t,e,n){if(t<0)return null;if(t==0)return[];for(var r=[],i=e;t&1&&(r=Tn(r,i,n)),t=t>>1,t!==0;)i=Tn(i,i,n);return r}function Vu(t,e){for(var n=t.split(""),r=[],i=n.length-1;i>=0;i--){var a=parseInt(n[i],e);if(isNaN(a))return null;r.push(a)}return r}function Mu(t,e,n){var r=Vu(t,e);if(r===null)return null;for(var i=[],a=[1],o=0;o<r.length;o++)r[o]&&(i=Tn(i,_i(r[o],a,n),n)),a=_i(e,a,n);for(var s="",o=i.length-1;o>=0;o--)s+=i[o].toString(n);return s}function Tu(t){return t.substring(0,2)==="0x"&&(t=t.substring(2)),t=t.toLowerCase(),Mu(t,16,10)}class Fu{constructor(e){e=e||{},this.seq=0,this.mid=(e.mid||1)%1023,this.offset=e.offset||0,this.lastTime=0}generate(){const e=Date.now(),n=(e-this.offset).toString(2);this.lastTime==e?(this.seq++,this.seq>4095&&(this.seq=0)):this.seq=0,this.lastTime=e;let r=this.seq.toString(2),i=this.mid.toString(2);for(;r.length<12;)r="0"+r;for(;i.length<10;)i="0"+i;const a=n+i+r;let o="";for(let s=a.length;s>0;s-=4)o=parseInt(a.substring(s-4,s),2).toString(16)+o;return Tu(o)}}const ju=l.defineComponent({name:"FcId",props:["modelValue","prefix"],emits:["update:modelValue"],inject:{designer:{default:null}},data(){return{preview:"7379787000000000"}},watch:{modelValue:{handler:function(t){if(!t){const e=new Fu({mid:42,offset:173448e7});this.$emit("update:modelValue",""+(this.prefix||"")+e.generate())}},immediate:!0}}});function Iu(t,e,n,r,i,a){const o=l.resolveComponent("el-input");return l.openBlock(),l.createBlock(o,{modelValue:t.designer?""+(t.prefix||"")+t.preview:t.modelValue,readonly:"",disabled:""},null,8,["modelValue"])}const Bu=we(ju,[["render",Iu]]),pm="",Nu=l.defineComponent({name:"FcTitle",data(){return{}},props:{title:String,size:String,align:String},computed:{textStyle(){return{textAlign:this.align||"left"}}}});function Lu(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",{class:l.normalizeClass(["_fc-title",t.size||"h2"]),style:l.normalizeStyle(t.textStyle)},l.toDisplayString(t.title),7)}const Uu=we(Nu,[["render",Lu]]),zu=l.defineComponent({name:"AudioBox",emits:["pause","play","ended"],data(){return{}},props:{src:String,type:String,controls:{type:Boolean,default:!0},autoplay:Boolean,loop:Boolean,preload:{type:String,default:"auto"},muted:Boolean}}),qu=["controls","autoplay","loop","preload","muted"],Gu=["src","type"];function Hu(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("audio",{key:t.src,controls:t.controls,autoplay:t.autoplay,loop:t.loop,preload:t.preload,muted:t.muted,onPause:e[0]||(e[0]=o=>t.$emit("pause",o)),onPlay:e[1]||(e[1]=o=>t.$emit("play",o)),onEnded:e[2]||(e[2]=o=>t.$emit("ended",o))},[l.createElementVNode("source",{src:t.src,type:t.type},null,8,Gu),e[3]||(e[3]=l.createTextVNode(" Your browser does not support the audio element. "))],40,qu)}const Wu=we(zu,[["render",Hu]]),mm="",Xu=l.defineComponent({name:"IframeBox",emits:["load"],data(){return{}},props:{src:String,loading:String}}),Yu=["src"];function Ju(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("iframe",{class:"_fc-iframe-box",src:t.src,frameborder:"0",onLoad:e[0]||(e[0]=o=>t.$emit("load",o))},null,40,Yu)}const Ku=we(Xu,[["render",Ju]]);var Fn={},Yt={},Ce={};Object.defineProperty(Ce,"__esModule",{value:!0});function Qu(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var Zu=function t(e,n){Qu(this,t),this.data=e,this.text=n.text||e,this.options=n};Ce.default=Zu,Object.defineProperty(Yt,"__esModule",{value:!0}),Yt.CODE39=void 0;var el=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),tl=Ce,nl=rl(tl);function rl(t){return t&&t.__esModule?t:{default:t}}function il(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ol(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function al(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var sl=function(t){al(e,t);function e(n,r){return il(this,e),n=n.toUpperCase(),r.mod43&&(n+=cl(fl(n))),ol(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return el(e,[{key:"encode",value:function(){for(var r=jn("*"),i=0;i<this.data.length;i++)r+=jn(this.data[i])+"0";return r+=jn("*"),{data:r,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9A-Z\-\.\ \$\/\+\%]+$/)!==-1}}]),e}(nl.default),bi=["0","1","2","3","4","5","6","7","8","9","A","B","C","D","E","F","G","H","I","J","K","L","M","N","O","P","Q","R","S","T","U","V","W","X","Y","Z","-","."," ","$","/","+","%","*"],ul=[20957,29783,23639,30485,20951,29813,23669,20855,29789,23645,29975,23831,30533,22295,30149,24005,21623,29981,23837,22301,30023,23879,30545,22343,30161,24017,21959,30065,23921,22385,29015,18263,29141,17879,29045,18293,17783,29021,18269,17477,17489,17681,20753,35770];function jn(t){return ll(wi(t))}function ll(t){return ul[t].toString(2)}function cl(t){return bi[t]}function wi(t){return bi.indexOf(t)}function fl(t){for(var e=0,n=0;n<t.length;n++)e+=wi(t[n]);return e=e%43,e}Yt.CODE39=sl;var Ge={},In={},ht={},ve={};Object.defineProperty(ve,"__esModule",{value:!0});var Et;function Bn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var $i=ve.SET_A=0,Oi=ve.SET_B=1,Si=ve.SET_C=2;ve.SHIFT=98;var dl=ve.START_A=103,hl=ve.START_B=104,pl=ve.START_C=105;ve.MODULO=103,ve.STOP=106,ve.FNC1=207,ve.SET_BY_CODE=(Et={},Bn(Et,dl,$i),Bn(Et,hl,Oi),Bn(Et,pl,Si),Et),ve.SWAP={101:$i,100:Oi,99:Si},ve.A_START_CHAR=String.fromCharCode(208),ve.B_START_CHAR=String.fromCharCode(209),ve.C_START_CHAR=String.fromCharCode(210),ve.A_CHARS="[\0-_\xC8-\xCF]",ve.B_CHARS="[ -\x7F\xC8-\xCF]",ve.C_CHARS="(\xCF*[0-9]{2}\xCF*)",ve.BARS=[11011001100,11001101100,11001100110,10010011e3,10010001100,10001001100,10011001e3,10011000100,10001100100,11001001e3,11001000100,11000100100,10110011100,10011011100,10011001110,10111001100,10011101100,10011100110,11001110010,11001011100,11001001110,11011100100,11001110100,11101101110,11101001100,11100101100,11100100110,11101100100,11100110100,11100110010,11011011e3,11011000110,11000110110,10100011e3,10001011e3,10001000110,10110001e3,10001101e3,10001100010,11010001e3,11000101e3,11000100010,10110111e3,10110001110,10001101110,10111011e3,10111000110,10001110110,11101110110,11010001110,11000101110,11011101e3,11011100010,11011101110,11101011e3,11101000110,11100010110,11101101e3,11101100010,11100011010,11101111010,11001000010,11110001010,1010011e4,10100001100,1001011e4,10010000110,10000101100,10000100110,1011001e4,10110000100,1001101e4,10011000010,10000110100,10000110010,11000010010,1100101e4,11110111010,11000010100,10001111010,10100111100,10010111100,10010011110,10111100100,10011110100,10011110010,11110100100,11110010100,11110010010,11011011110,11011110110,11110110110,10101111e3,10100011110,10001011110,10111101e3,10111100010,11110101e3,11110100010,10111011110,10111101110,11101011110,11110101110,11010000100,1101001e4,11010011100,1100011101011],Object.defineProperty(ht,"__esModule",{value:!0});var ml=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),gl=Ce,vl=yl(gl),Pe=ve;function yl(t){return t&&t.__esModule?t:{default:t}}function _l(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function bl(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function wl(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var $l=function(t){wl(e,t);function e(n,r){_l(this,e);var i=bl(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n.substring(1),r));return i.bytes=n.split("").map(function(a){return a.charCodeAt(0)}),i}return ml(e,[{key:"valid",value:function(){return/^[\x00-\x7F\xC8-\xD3]+$/.test(this.data)}},{key:"encode",value:function(){var r=this.bytes,i=r.shift()-105,a=Pe.SET_BY_CODE[i];if(a===void 0)throw new RangeError("The encoding does not start with a start character.");this.shouldEncodeAsEan128()===!0&&r.unshift(Pe.FNC1);var o=e.next(r,1,a);return{text:this.text===this.data?this.text.replace(/[^\x20-\x7E]/g,""):this.text,data:e.getBar(i)+o.result+e.getBar((o.checksum+i)%Pe.MODULO)+e.getBar(Pe.STOP)}}},{key:"shouldEncodeAsEan128",value:function(){var r=this.options.ean128||!1;return typeof r=="string"&&(r=r.toLowerCase()==="true"),r}}],[{key:"getBar",value:function(r){return Pe.BARS[r]?Pe.BARS[r].toString():""}},{key:"correctIndex",value:function(r,i){if(i===Pe.SET_A){var a=r.shift();return a<32?a+64:a-32}else return i===Pe.SET_B?r.shift()-32:(r.shift()-48)*10+r.shift()-48}},{key:"next",value:function(r,i,a){if(!r.length)return{result:"",checksum:0};var o=void 0,s=void 0;if(r[0]>=200){s=r.shift()-105;var u=Pe.SWAP[s];u!==void 0?o=e.next(r,i+1,u):((a===Pe.SET_A||a===Pe.SET_B)&&s===Pe.SHIFT&&(r[0]=a===Pe.SET_A?r[0]>95?r[0]-96:r[0]:r[0]<32?r[0]+96:r[0]),o=e.next(r,i+1,a))}else s=e.correctIndex(r,a),o=e.next(r,i+1,a);var d=e.getBar(s),h=s*i;return{result:d+o.result,checksum:h+o.checksum}}}]),e}(vl.default);ht.default=$l;var Nn={};Object.defineProperty(Nn,"__esModule",{value:!0});var Ke=ve,Ei=function(e){return e.match(new RegExp("^"+Ke.A_CHARS+"*"))[0].length},Ci=function(e){return e.match(new RegExp("^"+Ke.B_CHARS+"*"))[0].length},Ai=function(e){return e.match(new RegExp("^"+Ke.C_CHARS+"*"))[0]};function Ln(t,e){var n=e?Ke.A_CHARS:Ke.B_CHARS,r=t.match(new RegExp("^("+n+"+?)(([0-9]{2}){2,})([^0-9]|$)"));if(r)return r[1]+String.fromCharCode(204)+xi(t.substring(r[1].length));var i=t.match(new RegExp("^"+n+"+"))[0];return i.length===t.length?t:i+String.fromCharCode(e?205:206)+Ln(t.substring(i.length),!e)}function xi(t){var e=Ai(t),n=e.length;if(n===t.length)return t;t=t.substring(n);var r=Ei(t)>=Ci(t);return e+String.fromCharCode(r?206:205)+Ln(t,r)}Nn.default=function(t){var e=void 0,n=Ai(t).length;if(n>=2)e=Ke.C_START_CHAR+xi(t);else{var r=Ei(t)>Ci(t);e=(r?Ke.A_START_CHAR:Ke.B_START_CHAR)+Ln(t,r)}return e.replace(/[\xCD\xCE]([^])[\xCD\xCE]/,function(i,a){return String.fromCharCode(203)+a})},Object.defineProperty(In,"__esModule",{value:!0});var Ol=ht,Sl=Di(Ol),El=Nn,Cl=Di(El);function Di(t){return t&&t.__esModule?t:{default:t}}function Al(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Un(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function xl(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Dl=function(t){xl(e,t);function e(n,r){if(Al(this,e),/^[\x00-\x7F\xC8-\xD3]+$/.test(n))var i=Un(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,(0,Cl.default)(n),r));else var i=Un(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));return Un(i)}return e}(Sl.default);In.default=Dl;var zn={};Object.defineProperty(zn,"__esModule",{value:!0});var Pl=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Rl=ht,kl=Vl(Rl),Pi=ve;function Vl(t){return t&&t.__esModule?t:{default:t}}function Ml(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Tl(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Fl(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var jl=function(t){Fl(e,t);function e(n,r){return Ml(this,e),Tl(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,Pi.A_START_CHAR+n,r))}return Pl(e,[{key:"valid",value:function(){return new RegExp("^"+Pi.A_CHARS+"+$").test(this.data)}}]),e}(kl.default);zn.default=jl;var qn={};Object.defineProperty(qn,"__esModule",{value:!0});var Il=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Bl=ht,Nl=Ll(Bl),Ri=ve;function Ll(t){return t&&t.__esModule?t:{default:t}}function Ul(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function zl(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function ql(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Gl=function(t){ql(e,t);function e(n,r){return Ul(this,e),zl(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,Ri.B_START_CHAR+n,r))}return Il(e,[{key:"valid",value:function(){return new RegExp("^"+Ri.B_CHARS+"+$").test(this.data)}}]),e}(Nl.default);qn.default=Gl;var Gn={};Object.defineProperty(Gn,"__esModule",{value:!0});var Hl=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Wl=ht,Xl=Yl(Wl),ki=ve;function Yl(t){return t&&t.__esModule?t:{default:t}}function Jl(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Kl(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Ql(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Zl=function(t){Ql(e,t);function e(n,r){return Jl(this,e),Kl(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,ki.C_START_CHAR+n,r))}return Hl(e,[{key:"valid",value:function(){return new RegExp("^"+ki.C_CHARS+"+$").test(this.data)}}]),e}(Xl.default);Gn.default=Zl,Object.defineProperty(Ge,"__esModule",{value:!0}),Ge.CODE128C=Ge.CODE128B=Ge.CODE128A=Ge.CODE128=void 0;var ec=In,tc=Jt(ec),nc=zn,rc=Jt(nc),ic=qn,oc=Jt(ic),ac=Gn,sc=Jt(ac);function Jt(t){return t&&t.__esModule?t:{default:t}}Ge.CODE128=tc.default,Ge.CODE128A=rc.default,Ge.CODE128B=oc.default,Ge.CODE128C=sc.default;var Ae={},Hn={},Fe={};Object.defineProperty(Fe,"__esModule",{value:!0}),Fe.SIDE_BIN="101",Fe.MIDDLE_BIN="01010",Fe.BINARIES={L:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],G:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"],R:["1110010","1100110","1101100","1000010","1011100","1001110","1010000","1000100","1001000","1110100"],O:["0001101","0011001","0010011","0111101","0100011","0110001","0101111","0111011","0110111","0001011"],E:["0100111","0110011","0011011","0100001","0011101","0111001","0000101","0010001","0001001","0010111"]},Fe.EAN2_STRUCTURE=["LL","LG","GL","GG"],Fe.EAN5_STRUCTURE=["GGLLL","GLGLL","GLLGL","GLLLG","LGGLL","LLGGL","LLLGG","LGLGL","LGLLG","LLGLG"],Fe.EAN13_STRUCTURE=["LLLLLL","LLGLGG","LLGGLG","LLGGGL","LGLLGG","LGGLLG","LGGGLL","LGLGLG","LGLGGL","LGGLGL"];var Kt={},at={};Object.defineProperty(at,"__esModule",{value:!0});var uc=Fe,lc=function(e,n,r){var i=e.split("").map(function(o,s){return uc.BINARIES[n[s]]}).map(function(o,s){return o?o[e[s]]:""});if(r){var a=e.length-1;i=i.map(function(o,s){return s<a?o+r:o})}return i.join("")};at.default=lc,Object.defineProperty(Kt,"__esModule",{value:!0});var cc=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),pt=Fe,fc=at,Vi=Mi(fc),dc=Ce,hc=Mi(dc);function Mi(t){return t&&t.__esModule?t:{default:t}}function pc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function mc(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function gc(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var vc=function(t){gc(e,t);function e(n,r){pc(this,e);var i=mc(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));return i.fontSize=!r.flat&&r.fontSize>r.width*10?r.width*10:r.fontSize,i.guardHeight=r.height+i.fontSize/2+r.textMargin,i}return cc(e,[{key:"encode",value:function(){return this.options.flat?this.encodeFlat():this.encodeGuarded()}},{key:"leftText",value:function(r,i){return this.text.substr(r,i)}},{key:"leftEncode",value:function(r,i){return(0,Vi.default)(r,i)}},{key:"rightText",value:function(r,i){return this.text.substr(r,i)}},{key:"rightEncode",value:function(r,i){return(0,Vi.default)(r,i)}},{key:"encodeGuarded",value:function(){var r={fontSize:this.fontSize},i={height:this.guardHeight};return[{data:pt.SIDE_BIN,options:i},{data:this.leftEncode(),text:this.leftText(),options:r},{data:pt.MIDDLE_BIN,options:i},{data:this.rightEncode(),text:this.rightText(),options:r},{data:pt.SIDE_BIN,options:i}]}},{key:"encodeFlat",value:function(){var r=[pt.SIDE_BIN,this.leftEncode(),pt.MIDDLE_BIN,this.rightEncode(),pt.SIDE_BIN];return{data:r.join(""),text:this.text}}}]),e}(hc.default);Kt.default=vc,Object.defineProperty(Hn,"__esModule",{value:!0});var yc=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Ct=function t(e,n,r){e===null&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,n);if(i===void 0){var a=Object.getPrototypeOf(e);return a===null?void 0:t(a,n,r)}else{if("value"in i)return i.value;var o=i.get;return o===void 0?void 0:o.call(r)}},_c=Fe,bc=Kt,wc=$c(bc);function $c(t){return t&&t.__esModule?t:{default:t}}function Oc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Sc(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Ec(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Ti=function(e){var n=e.substr(0,12).split("").map(function(r){return+r}).reduce(function(r,i,a){return a%2?r+i*3:r+i},0);return(10-n%10)%10},Cc=function(t){Ec(e,t);function e(n,r){Oc(this,e),n.search(/^[0-9]{12}$/)!==-1&&(n+=Ti(n));var i=Sc(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));return i.lastChar=r.lastChar,i}return yc(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{13}$/)!==-1&&+this.data[12]===Ti(this.data)}},{key:"leftText",value:function(){return Ct(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftText",this).call(this,1,6)}},{key:"leftEncode",value:function(){var r=this.data.substr(1,6),i=_c.EAN13_STRUCTURE[this.data[0]];return Ct(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftEncode",this).call(this,r,i)}},{key:"rightText",value:function(){return Ct(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightText",this).call(this,7,6)}},{key:"rightEncode",value:function(){var r=this.data.substr(7,6);return Ct(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightEncode",this).call(this,r,"RRRRRR")}},{key:"encodeGuarded",value:function(){var r=Ct(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"encodeGuarded",this).call(this);return this.options.displayValue&&(r.unshift({data:"000000000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),this.options.lastChar&&(r.push({data:"00"}),r.push({data:"00000",text:this.options.lastChar,options:{fontSize:this.fontSize}}))),r}}]),e}(wc.default);Hn.default=Cc;var Wn={};Object.defineProperty(Wn,"__esModule",{value:!0});var Ac=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Qt=function t(e,n,r){e===null&&(e=Function.prototype);var i=Object.getOwnPropertyDescriptor(e,n);if(i===void 0){var a=Object.getPrototypeOf(e);return a===null?void 0:t(a,n,r)}else{if("value"in i)return i.value;var o=i.get;return o===void 0?void 0:o.call(r)}},xc=Kt,Dc=Pc(xc);function Pc(t){return t&&t.__esModule?t:{default:t}}function Rc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function kc(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Vc(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Fi=function(e){var n=e.substr(0,7).split("").map(function(r){return+r}).reduce(function(r,i,a){return a%2?r+i:r+i*3},0);return(10-n%10)%10},Mc=function(t){Vc(e,t);function e(n,r){return Rc(this,e),n.search(/^[0-9]{7}$/)!==-1&&(n+=Fi(n)),kc(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return Ac(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{8}$/)!==-1&&+this.data[7]===Fi(this.data)}},{key:"leftText",value:function(){return Qt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftText",this).call(this,0,4)}},{key:"leftEncode",value:function(){var r=this.data.substr(0,4);return Qt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"leftEncode",this).call(this,r,"LLLL")}},{key:"rightText",value:function(){return Qt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightText",this).call(this,4,4)}},{key:"rightEncode",value:function(){var r=this.data.substr(4,4);return Qt(e.prototype.__proto__||Object.getPrototypeOf(e.prototype),"rightEncode",this).call(this,r,"RRRR")}}]),e}(Dc.default);Wn.default=Mc;var Xn={};Object.defineProperty(Xn,"__esModule",{value:!0});var Tc=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Fc=Fe,jc=at,Ic=ji(jc),Bc=Ce,Nc=ji(Bc);function ji(t){return t&&t.__esModule?t:{default:t}}function Lc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Uc(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function zc(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var qc=function(e){var n=e.split("").map(function(r){return+r}).reduce(function(r,i,a){return a%2?r+i*9:r+i*3},0);return n%10},Gc=function(t){zc(e,t);function e(n,r){return Lc(this,e),Uc(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return Tc(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{5}$/)!==-1}},{key:"encode",value:function(){var r=Fc.EAN5_STRUCTURE[qc(this.data)];return{data:"1011"+(0,Ic.default)(this.data,r,"01"),text:this.text}}}]),e}(Nc.default);Xn.default=Gc;var Yn={};Object.defineProperty(Yn,"__esModule",{value:!0});var Hc=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Wc=Fe,Xc=at,Yc=Ii(Xc),Jc=Ce,Kc=Ii(Jc);function Ii(t){return t&&t.__esModule?t:{default:t}}function Qc(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Zc(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function ef(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var tf=function(t){ef(e,t);function e(n,r){return Qc(this,e),Zc(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return Hc(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{2}$/)!==-1}},{key:"encode",value:function(){var r=Wc.EAN2_STRUCTURE[parseInt(this.data)%4];return{data:"1011"+(0,Yc.default)(this.data,r,"01"),text:this.text}}}]),e}(Kc.default);Yn.default=tf;var At={};Object.defineProperty(At,"__esModule",{value:!0});var nf=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();At.checksum=Jn;var rf=at,mt=Bi(rf),of=Ce,af=Bi(of);function Bi(t){return t&&t.__esModule?t:{default:t}}function sf(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function uf(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function lf(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var cf=function(t){lf(e,t);function e(n,r){sf(this,e),n.search(/^[0-9]{11}$/)!==-1&&(n+=Jn(n));var i=uf(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));return i.displayValue=r.displayValue,r.fontSize>r.width*10?i.fontSize=r.width*10:i.fontSize=r.fontSize,i.guardHeight=r.height+i.fontSize/2+r.textMargin,i}return nf(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{12}$/)!==-1&&this.data[11]==Jn(this.data)}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var r="";return r+="101",r+=(0,mt.default)(this.data.substr(0,6),"LLLLLL"),r+="01010",r+=(0,mt.default)(this.data.substr(6,6),"RRRRRR"),r+="101",{data:r,text:this.text}}},{key:"guardedEncoding",value:function(){var r=[];return this.displayValue&&r.push({data:"00000000",text:this.text.substr(0,1),options:{textAlign:"left",fontSize:this.fontSize}}),r.push({data:"101"+(0,mt.default)(this.data[0],"L"),options:{height:this.guardHeight}}),r.push({data:(0,mt.default)(this.data.substr(1,5),"LLLLL"),text:this.text.substr(1,5),options:{fontSize:this.fontSize}}),r.push({data:"01010",options:{height:this.guardHeight}}),r.push({data:(0,mt.default)(this.data.substr(6,5),"RRRRR"),text:this.text.substr(6,5),options:{fontSize:this.fontSize}}),r.push({data:(0,mt.default)(this.data[11],"R")+"101",options:{height:this.guardHeight}}),this.displayValue&&r.push({data:"00000000",text:this.text.substr(11,1),options:{textAlign:"right",fontSize:this.fontSize}}),r}}]),e}(af.default);function Jn(t){var e=0,n;for(n=1;n<11;n+=2)e+=parseInt(t[n]);for(n=0;n<11;n+=2)e+=parseInt(t[n])*3;return(10-e%10)%10}At.default=cf;var Kn={};Object.defineProperty(Kn,"__esModule",{value:!0});var ff=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),df=at,hf=Ni(df),pf=Ce,mf=Ni(pf),gf=At;function Ni(t){return t&&t.__esModule?t:{default:t}}function vf(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Qn(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function yf(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var _f=["XX00000XXX","XX10000XXX","XX20000XXX","XXX00000XX","XXXX00000X","XXXXX00005","XXXXX00006","XXXXX00007","XXXXX00008","XXXXX00009"],bf=[["EEEOOO","OOOEEE"],["EEOEOO","OOEOEE"],["EEOOEO","OOEEOE"],["EEOOOE","OOEEEO"],["EOEEOO","OEOOEE"],["EOOEEO","OEEOOE"],["EOOOEE","OEEEOO"],["EOEOEO","OEOEOE"],["EOEOOE","OEOEEO"],["EOOEOE","OEEOEO"]],wf=function(t){yf(e,t);function e(n,r){vf(this,e);var i=Qn(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));if(i.isValid=!1,n.search(/^[0-9]{6}$/)!==-1)i.middleDigits=n,i.upcA=Li(n,"0"),i.text=r.text||""+i.upcA[0]+n+i.upcA[i.upcA.length-1],i.isValid=!0;else if(n.search(/^[01][0-9]{7}$/)!==-1)if(i.middleDigits=n.substring(1,n.length-1),i.upcA=Li(i.middleDigits,n[0]),i.upcA[i.upcA.length-1]===n[n.length-1])i.isValid=!0;else return Qn(i);else return Qn(i);return i.displayValue=r.displayValue,r.fontSize>r.width*10?i.fontSize=r.width*10:i.fontSize=r.fontSize,i.guardHeight=r.height+i.fontSize/2+r.textMargin,i}return ff(e,[{key:"valid",value:function(){return this.isValid}},{key:"encode",value:function(){return this.options.flat?this.flatEncoding():this.guardedEncoding()}},{key:"flatEncoding",value:function(){var r="";return r+="101",r+=this.encodeMiddleDigits(),r+="010101",{data:r,text:this.text}}},{key:"guardedEncoding",value:function(){var r=[];return this.displayValue&&r.push({data:"00000000",text:this.text[0],options:{textAlign:"left",fontSize:this.fontSize}}),r.push({data:"101",options:{height:this.guardHeight}}),r.push({data:this.encodeMiddleDigits(),text:this.text.substring(1,7),options:{fontSize:this.fontSize}}),r.push({data:"010101",options:{height:this.guardHeight}}),this.displayValue&&r.push({data:"00000000",text:this.text[7],options:{textAlign:"right",fontSize:this.fontSize}}),r}},{key:"encodeMiddleDigits",value:function(){var r=this.upcA[0],i=this.upcA[this.upcA.length-1],a=bf[parseInt(i)][parseInt(r)];return(0,hf.default)(this.middleDigits,a)}}]),e}(mf.default);function Li(t,e){for(var n=parseInt(t[t.length-1]),r=_f[n],i="",a=0,o=0;o<r.length;o++){var s=r[o];s==="X"?i+=t[a++]:i+=s}return i=""+e+i,""+i+(0,gf.checksum)(i)}Kn.default=wf,Object.defineProperty(Ae,"__esModule",{value:!0}),Ae.UPCE=Ae.UPC=Ae.EAN2=Ae.EAN5=Ae.EAN8=Ae.EAN13=void 0;var $f=Hn,Of=gt($f),Sf=Wn,Ef=gt(Sf),Cf=Xn,Af=gt(Cf),xf=Yn,Df=gt(xf),Pf=At,Rf=gt(Pf),kf=Kn,Vf=gt(kf);function gt(t){return t&&t.__esModule?t:{default:t}}Ae.EAN13=Of.default,Ae.EAN8=Ef.default,Ae.EAN5=Af.default,Ae.EAN2=Df.default,Ae.UPC=Rf.default,Ae.UPCE=Vf.default;var vt={},Zt={},xt={};Object.defineProperty(xt,"__esModule",{value:!0}),xt.START_BIN="1010",xt.END_BIN="11101",xt.BINARIES=["00110","10001","01001","11000","00101","10100","01100","00011","10010","01010"],Object.defineProperty(Zt,"__esModule",{value:!0});var Mf=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),en=xt,Tf=Ce,Ff=jf(Tf);function jf(t){return t&&t.__esModule?t:{default:t}}function If(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Bf(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Nf(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Lf=function(t){Nf(e,t);function e(){return If(this,e),Bf(this,(e.__proto__||Object.getPrototypeOf(e)).apply(this,arguments))}return Mf(e,[{key:"valid",value:function(){return this.data.search(/^([0-9]{2})+$/)!==-1}},{key:"encode",value:function(){var r=this,i=this.data.match(/.{2}/g).map(function(a){return r.encodePair(a)}).join("");return{data:en.START_BIN+i+en.END_BIN,text:this.text}}},{key:"encodePair",value:function(r){var i=en.BINARIES[r[1]];return en.BINARIES[r[0]].split("").map(function(a,o){return(a==="1"?"111":"1")+(i[o]==="1"?"000":"0")}).join("")}}]),e}(Ff.default);Zt.default=Lf;var Zn={};Object.defineProperty(Zn,"__esModule",{value:!0});var Uf=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),zf=Zt,qf=Gf(zf);function Gf(t){return t&&t.__esModule?t:{default:t}}function Hf(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Wf(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Xf(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Ui=function(e){var n=e.substr(0,13).split("").map(function(r){return parseInt(r,10)}).reduce(function(r,i,a){return r+i*(3-a%2*2)},0);return Math.ceil(n/10)*10-n},Yf=function(t){Xf(e,t);function e(n,r){return Hf(this,e),n.search(/^[0-9]{13}$/)!==-1&&(n+=Ui(n)),Wf(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return Uf(e,[{key:"valid",value:function(){return this.data.search(/^[0-9]{14}$/)!==-1&&+this.data[13]===Ui(this.data)}}]),e}(qf.default);Zn.default=Yf,Object.defineProperty(vt,"__esModule",{value:!0}),vt.ITF14=vt.ITF=void 0;var Jf=Zt,Kf=zi(Jf),Qf=Zn,Zf=zi(Qf);function zi(t){return t&&t.__esModule?t:{default:t}}vt.ITF=Kf.default,vt.ITF14=Zf.default;var je={},st={};Object.defineProperty(st,"__esModule",{value:!0});var ed=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),td=Ce,nd=rd(td);function rd(t){return t&&t.__esModule?t:{default:t}}function id(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function od(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function ad(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var sd=function(t){ad(e,t);function e(n,r){return id(this,e),od(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return ed(e,[{key:"encode",value:function(){for(var r="110",i=0;i<this.data.length;i++){var a=parseInt(this.data[i]),o=a.toString(2);o=ud(o,4-o.length);for(var s=0;s<o.length;s++)r+=o[s]=="0"?"100":"110"}return r+="1001",{data:r,text:this.text}}},{key:"valid",value:function(){return this.data.search(/^[0-9]+$/)!==-1}}]),e}(nd.default);function ud(t,e){for(var n=0;n<e;n++)t="0"+t;return t}st.default=sd;var er={},ut={};Object.defineProperty(ut,"__esModule",{value:!0}),ut.mod10=ld,ut.mod11=cd;function ld(t){for(var e=0,n=0;n<t.length;n++){var r=parseInt(t[n]);(n+t.length)%2===0?e+=r:e+=r*2%10+Math.floor(r*2/10)}return(10-e%10)%10}function cd(t){for(var e=0,n=[2,3,4,5,6,7],r=0;r<t.length;r++){var i=parseInt(t[t.length-1-r]);e+=n[r%n.length]*i}return(11-e%11)%11}Object.defineProperty(er,"__esModule",{value:!0});var fd=st,dd=pd(fd),hd=ut;function pd(t){return t&&t.__esModule?t:{default:t}}function md(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gd(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function vd(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var yd=function(t){vd(e,t);function e(n,r){return md(this,e),gd(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n+(0,hd.mod10)(n),r))}return e}(dd.default);er.default=yd;var tr={};Object.defineProperty(tr,"__esModule",{value:!0});var _d=st,bd=$d(_d),wd=ut;function $d(t){return t&&t.__esModule?t:{default:t}}function Od(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Sd(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Ed(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Cd=function(t){Ed(e,t);function e(n,r){return Od(this,e),Sd(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n+(0,wd.mod11)(n),r))}return e}(bd.default);tr.default=Cd;var nr={};Object.defineProperty(nr,"__esModule",{value:!0});var Ad=st,xd=Dd(Ad),qi=ut;function Dd(t){return t&&t.__esModule?t:{default:t}}function Pd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Rd(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function kd(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Vd=function(t){kd(e,t);function e(n,r){return Pd(this,e),n+=(0,qi.mod10)(n),n+=(0,qi.mod10)(n),Rd(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return e}(xd.default);nr.default=Vd;var rr={};Object.defineProperty(rr,"__esModule",{value:!0});var Md=st,Td=Fd(Md),Gi=ut;function Fd(t){return t&&t.__esModule?t:{default:t}}function jd(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Id(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function Bd(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var Nd=function(t){Bd(e,t);function e(n,r){return jd(this,e),n+=(0,Gi.mod11)(n),n+=(0,Gi.mod10)(n),Id(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return e}(Td.default);rr.default=Nd,Object.defineProperty(je,"__esModule",{value:!0}),je.MSI1110=je.MSI1010=je.MSI11=je.MSI10=je.MSI=void 0;var Ld=st,Ud=Dt(Ld),zd=er,qd=Dt(zd),Gd=tr,Hd=Dt(Gd),Wd=nr,Xd=Dt(Wd),Yd=rr,Jd=Dt(Yd);function Dt(t){return t&&t.__esModule?t:{default:t}}je.MSI=Ud.default,je.MSI10=qd.default,je.MSI11=Hd.default,je.MSI1010=Xd.default,je.MSI1110=Jd.default;var tn={};Object.defineProperty(tn,"__esModule",{value:!0}),tn.pharmacode=void 0;var Kd=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Qd=Ce,Zd=eh(Qd);function eh(t){return t&&t.__esModule?t:{default:t}}function th(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function nh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function rh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var ih=function(t){rh(e,t);function e(n,r){th(this,e);var i=nh(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r));return i.number=parseInt(n,10),i}return Kd(e,[{key:"encode",value:function(){for(var r=this.number,i="";!isNaN(r)&&r!=0;)r%2===0?(i="11100"+i,r=(r-2)/2):(i="100"+i,r=(r-1)/2);return i=i.slice(0,-2),{data:i,text:this.text}}},{key:"valid",value:function(){return this.number>=3&&this.number<=131070}}]),e}(Zd.default);tn.pharmacode=ih;var nn={};Object.defineProperty(nn,"__esModule",{value:!0}),nn.codabar=void 0;var oh=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),ah=Ce,sh=uh(ah);function uh(t){return t&&t.__esModule?t:{default:t}}function lh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function ch(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function fh(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var dh=function(t){fh(e,t);function e(n,r){lh(this,e),n.search(/^[0-9\-\$\:\.\+\/]+$/)===0&&(n="A"+n+"A");var i=ch(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n.toUpperCase(),r));return i.text=i.options.text||i.text.replace(/[A-D]/g,""),i}return oh(e,[{key:"valid",value:function(){return this.data.search(/^[A-D][0-9\-\$\:\.\+\/]+[A-D]$/)!==-1}},{key:"encode",value:function(){for(var r=[],i=this.getEncodings(),a=0;a<this.data.length;a++)r.push(i[this.data.charAt(a)]),a!==this.data.length-1&&r.push("0");return{text:this.text,data:r.join("")}}},{key:"getEncodings",value:function(){return{0:"101010011",1:"101011001",2:"101001011",3:"110010101",4:"101101001",5:"110101001",6:"100101011",7:"100101101",8:"100110101",9:"110100101","-":"101001101",$:"101100101",":":"1101011011","/":"1101101011",".":"1101101101","+":"1011011011",A:"1011001001",B:"1001001011",C:"1010010011",D:"1010011001"}}}]),e}(sh.default);nn.codabar=dh;var rn={};Object.defineProperty(rn,"__esModule",{value:!0}),rn.GenericBarcode=void 0;var hh=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),ph=Ce,mh=gh(ph);function gh(t){return t&&t.__esModule?t:{default:t}}function vh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function yh(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function _h(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var bh=function(t){_h(e,t);function e(n,r){return vh(this,e),yh(this,(e.__proto__||Object.getPrototypeOf(e)).call(this,n,r))}return hh(e,[{key:"encode",value:function(){return{data:"10101010101010101010101010101010101010101",text:this.text}}},{key:"valid",value:function(){return!0}}]),e}(mh.default);rn.GenericBarcode=bh,Object.defineProperty(Fn,"__esModule",{value:!0});var wh=Yt,on=Ge,yt=Ae,Hi=vt,Pt=je,$h=tn,Oh=nn,Sh=rn;Fn.default={CODE39:wh.CODE39,CODE128:on.CODE128,CODE128A:on.CODE128A,CODE128B:on.CODE128B,CODE128C:on.CODE128C,EAN13:yt.EAN13,EAN8:yt.EAN8,EAN5:yt.EAN5,EAN2:yt.EAN2,UPC:yt.UPC,UPCE:yt.UPCE,ITF14:Hi.ITF14,ITF:Hi.ITF,MSI:Pt.MSI,MSI10:Pt.MSI10,MSI11:Pt.MSI11,MSI1010:Pt.MSI1010,MSI1110:Pt.MSI1110,pharmacode:$h.pharmacode,codabar:Oh.codabar,GenericBarcode:Sh.GenericBarcode};var _t={};Object.defineProperty(_t,"__esModule",{value:!0});var Eh=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var n=arguments[e];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(t[r]=n[r])}return t};_t.default=function(t,e){return Eh({},t,e)};var ir={};Object.defineProperty(ir,"__esModule",{value:!0}),ir.default=Ch;function Ch(t){var e=[];function n(r){if(Array.isArray(r))for(var i=0;i<r.length;i++)n(r[i]);else r.text=r.text||"",r.data=r.data||"",e.push(r)}return n(t),e}var or={};Object.defineProperty(or,"__esModule",{value:!0}),or.default=Ah;function Ah(t){return t.marginTop=t.marginTop||t.margin,t.marginBottom=t.marginBottom||t.margin,t.marginRight=t.marginRight||t.margin,t.marginLeft=t.marginLeft||t.margin,t}var ar={},sr={},an={};Object.defineProperty(an,"__esModule",{value:!0}),an.default=xh;function xh(t){var e=["width","height","textMargin","fontSize","margin","marginTop","marginBottom","marginLeft","marginRight"];for(var n in e)e.hasOwnProperty(n)&&(n=e[n],typeof t[n]=="string"&&(t[n]=parseInt(t[n],10)));return typeof t.displayValue=="string"&&(t.displayValue=t.displayValue!="false"),t}var sn={};Object.defineProperty(sn,"__esModule",{value:!0});var Dh={width:2,height:100,format:"auto",displayValue:!0,fontOptions:"",font:"monospace",text:void 0,textAlign:"center",textPosition:"bottom",textMargin:2,fontSize:20,background:"#ffffff",lineColor:"#000000",margin:10,marginTop:void 0,marginBottom:void 0,marginLeft:void 0,marginRight:void 0,valid:function(){}};sn.default=Dh,Object.defineProperty(sr,"__esModule",{value:!0});var Ph=an,Rh=Xi(Ph),kh=sn,Wi=Xi(kh);function Xi(t){return t&&t.__esModule?t:{default:t}}function Vh(t){var e={};for(var n in Wi.default)Wi.default.hasOwnProperty(n)&&(t.hasAttribute("jsbarcode-"+n.toLowerCase())&&(e[n]=t.getAttribute("jsbarcode-"+n.toLowerCase())),t.hasAttribute("data-"+n.toLowerCase())&&(e[n]=t.getAttribute("data-"+n.toLowerCase())));return e.value=t.getAttribute("jsbarcode-value")||t.getAttribute("data-value"),e=(0,Rh.default)(e),e}sr.default=Vh;var ur={},lr={},Re={};Object.defineProperty(Re,"__esModule",{value:!0}),Re.getTotalWidthOfEncodings=Re.calculateEncodingAttributes=Re.getBarcodePadding=Re.getEncodingHeight=Re.getMaximumHeightOfEncodings=void 0;var Mh=_t,Th=Fh(Mh);function Fh(t){return t&&t.__esModule?t:{default:t}}function Yi(t,e){return e.height+(e.displayValue&&t.text.length>0?e.fontSize+e.textMargin:0)+e.marginTop+e.marginBottom}function Ji(t,e,n){if(n.displayValue&&e<t){if(n.textAlign=="center")return Math.floor((t-e)/2);if(n.textAlign=="left")return 0;if(n.textAlign=="right")return Math.floor(t-e)}return 0}function jh(t,e,n){for(var r=0;r<t.length;r++){var i=t[r],a=(0,Th.default)(e,i.options),o;a.displayValue?o=Nh(i.text,a,n):o=0;var s=i.data.length*a.width;i.width=Math.ceil(Math.max(o,s)),i.height=Yi(i,a),i.barcodePadding=Ji(o,s,a)}}function Ih(t){for(var e=0,n=0;n<t.length;n++)e+=t[n].width;return e}function Bh(t){for(var e=0,n=0;n<t.length;n++)t[n].height>e&&(e=t[n].height);return e}function Nh(t,e,n){var r;if(n)r=n;else if(typeof document<"u")r=document.createElement("canvas").getContext("2d");else return 0;r.font=e.fontOptions+" "+e.fontSize+"px "+e.font;var i=r.measureText(t);if(!i)return 0;var a=i.width;return a}Re.getMaximumHeightOfEncodings=Bh,Re.getEncodingHeight=Yi,Re.getBarcodePadding=Ji,Re.calculateEncodingAttributes=jh,Re.getTotalWidthOfEncodings=Ih,Object.defineProperty(lr,"__esModule",{value:!0});var Lh=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Uh=_t,zh=qh(Uh),cr=Re;function qh(t){return t&&t.__esModule?t:{default:t}}function Gh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var Hh=function(){function t(e,n,r){Gh(this,t),this.canvas=e,this.encodings=n,this.options=r}return Lh(t,[{key:"render",value:function(){if(!this.canvas.getContext)throw new Error("The browser does not support canvas.");this.prepareCanvas();for(var n=0;n<this.encodings.length;n++){var r=(0,zh.default)(this.options,this.encodings[n].options);this.drawCanvasBarcode(r,this.encodings[n]),this.drawCanvasText(r,this.encodings[n]),this.moveCanvasDrawing(this.encodings[n])}this.restoreCanvas()}},{key:"prepareCanvas",value:function(){var n=this.canvas.getContext("2d");n.save(),(0,cr.calculateEncodingAttributes)(this.encodings,this.options,n);var r=(0,cr.getTotalWidthOfEncodings)(this.encodings),i=(0,cr.getMaximumHeightOfEncodings)(this.encodings);this.canvas.width=r+this.options.marginLeft+this.options.marginRight,this.canvas.height=i,n.clearRect(0,0,this.canvas.width,this.canvas.height),this.options.background&&(n.fillStyle=this.options.background,n.fillRect(0,0,this.canvas.width,this.canvas.height)),n.translate(this.options.marginLeft,0)}},{key:"drawCanvasBarcode",value:function(n,r){var i=this.canvas.getContext("2d"),a=r.data,o;n.textPosition=="top"?o=n.marginTop+n.fontSize+n.textMargin:o=n.marginTop,i.fillStyle=n.lineColor;for(var s=0;s<a.length;s++){var u=s*n.width+r.barcodePadding;a[s]==="1"?i.fillRect(u,o,n.width,n.height):a[s]&&i.fillRect(u,o,n.width,n.height*a[s])}}},{key:"drawCanvasText",value:function(n,r){var i=this.canvas.getContext("2d"),a=n.fontOptions+" "+n.fontSize+"px "+n.font;if(n.displayValue){var o,s;n.textPosition=="top"?s=n.marginTop+n.fontSize-n.textMargin:s=n.height+n.textMargin+n.marginTop+n.fontSize,i.font=a,n.textAlign=="left"||r.barcodePadding>0?(o=0,i.textAlign="left"):n.textAlign=="right"?(o=r.width-1,i.textAlign="right"):(o=r.width/2,i.textAlign="center"),i.fillText(r.text,o,s)}}},{key:"moveCanvasDrawing",value:function(n){var r=this.canvas.getContext("2d");r.translate(n.width,0)}},{key:"restoreCanvas",value:function(){var n=this.canvas.getContext("2d");n.restore()}}]),t}();lr.default=Hh;var fr={};Object.defineProperty(fr,"__esModule",{value:!0});var Wh=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}(),Xh=_t,Yh=Jh(Xh),dr=Re;function Jh(t){return t&&t.__esModule?t:{default:t}}function Kh(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var un="http://www.w3.org/2000/svg",Qh=function(){function t(e,n,r){Kh(this,t),this.svg=e,this.encodings=n,this.options=r,this.document=r.xmlDocument||document}return Wh(t,[{key:"render",value:function(){var n=this.options.marginLeft;this.prepareSVG();for(var r=0;r<this.encodings.length;r++){var i=this.encodings[r],a=(0,Yh.default)(this.options,i.options),o=this.createGroup(n,a.marginTop,this.svg);this.setGroupOptions(o,a),this.drawSvgBarcode(o,a,i),this.drawSVGText(o,a,i),n+=i.width}}},{key:"prepareSVG",value:function(){for(;this.svg.firstChild;)this.svg.removeChild(this.svg.firstChild);(0,dr.calculateEncodingAttributes)(this.encodings,this.options);var n=(0,dr.getTotalWidthOfEncodings)(this.encodings),r=(0,dr.getMaximumHeightOfEncodings)(this.encodings),i=n+this.options.marginLeft+this.options.marginRight;this.setSvgAttributes(i,r),this.options.background&&this.drawRect(0,0,i,r,this.svg).setAttribute("style","fill:"+this.options.background+";")}},{key:"drawSvgBarcode",value:function(n,r,i){var a=i.data,o;r.textPosition=="top"?o=r.fontSize+r.textMargin:o=0;for(var s=0,u=0,d=0;d<a.length;d++)u=d*r.width+i.barcodePadding,a[d]==="1"?s++:s>0&&(this.drawRect(u-r.width*s,o,r.width*s,r.height,n),s=0);s>0&&this.drawRect(u-r.width*(s-1),o,r.width*s,r.height,n)}},{key:"drawSVGText",value:function(n,r,i){var a=this.document.createElementNS(un,"text");if(r.displayValue){var o,s;a.setAttribute("style","font:"+r.fontOptions+" "+r.fontSize+"px "+r.font),r.textPosition=="top"?s=r.fontSize-r.textMargin:s=r.height+r.textMargin+r.fontSize,r.textAlign=="left"||i.barcodePadding>0?(o=0,a.setAttribute("text-anchor","start")):r.textAlign=="right"?(o=i.width-1,a.setAttribute("text-anchor","end")):(o=i.width/2,a.setAttribute("text-anchor","middle")),a.setAttribute("x",o),a.setAttribute("y",s),a.appendChild(this.document.createTextNode(i.text)),n.appendChild(a)}}},{key:"setSvgAttributes",value:function(n,r){var i=this.svg;i.setAttribute("width",n+"px"),i.setAttribute("height",r+"px"),i.setAttribute("x","0px"),i.setAttribute("y","0px"),i.setAttribute("viewBox","0 0 "+n+" "+r),i.setAttribute("xmlns",un),i.setAttribute("version","1.1"),i.setAttribute("style","transform: translate(0,0)")}},{key:"createGroup",value:function(n,r,i){var a=this.document.createElementNS(un,"g");return a.setAttribute("transform","translate("+n+", "+r+")"),i.appendChild(a),a}},{key:"setGroupOptions",value:function(n,r){n.setAttribute("style","fill:"+r.lineColor+";")}},{key:"drawRect",value:function(n,r,i,a,o){var s=this.document.createElementNS(un,"rect");return s.setAttribute("x",n),s.setAttribute("y",r),s.setAttribute("width",i),s.setAttribute("height",a),o.appendChild(s),s}}]),t}();fr.default=Qh;var hr={};Object.defineProperty(hr,"__esModule",{value:!0});var Zh=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();function ep(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var tp=function(){function t(e,n,r){ep(this,t),this.object=e,this.encodings=n,this.options=r}return Zh(t,[{key:"render",value:function(){this.object.encodings=this.encodings}}]),t}();hr.default=tp,Object.defineProperty(ur,"__esModule",{value:!0});var np=lr,rp=pr(np),ip=fr,op=pr(ip),ap=hr,sp=pr(ap);function pr(t){return t&&t.__esModule?t:{default:t}}ur.default={CanvasRenderer:rp.default,SVGRenderer:op.default,ObjectRenderer:sp.default};var bt={};Object.defineProperty(bt,"__esModule",{value:!0});function mr(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function gr(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return e&&(typeof e=="object"||typeof e=="function")?e:t}function vr(t,e){if(typeof e!="function"&&e!==null)throw new TypeError("Super expression must either be null or a function, not "+typeof e);t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(Object.setPrototypeOf?Object.setPrototypeOf(t,e):t.__proto__=e)}var up=function(t){vr(e,t);function e(n,r){mr(this,e);var i=gr(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return i.name="InvalidInputException",i.symbology=n,i.input=r,i.message='"'+i.input+'" is not a valid input for '+i.symbology,i}return e}(Error),lp=function(t){vr(e,t);function e(){mr(this,e);var n=gr(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return n.name="InvalidElementException",n.message="Not supported type to render on",n}return e}(Error),cp=function(t){vr(e,t);function e(){mr(this,e);var n=gr(this,(e.__proto__||Object.getPrototypeOf(e)).call(this));return n.name="NoElementException",n.message="No element to render on.",n}return e}(Error);bt.InvalidInputException=up,bt.InvalidElementException=lp,bt.NoElementException=cp,Object.defineProperty(ar,"__esModule",{value:!0});var fp=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?function(t){return typeof t}:function(t){return t&&typeof Symbol=="function"&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},dp=sr,yr=Ki(dp),hp=ur,Rt=Ki(hp),pp=bt;function Ki(t){return t&&t.__esModule?t:{default:t}}function _r(t){if(typeof t=="string")return mp(t);if(Array.isArray(t)){for(var e=[],n=0;n<t.length;n++)e.push(_r(t[n]));return e}else{if(typeof HTMLCanvasElement<"u"&&t instanceof HTMLImageElement)return gp(t);if(t&&t.nodeName&&t.nodeName.toLowerCase()==="svg"||typeof SVGElement<"u"&&t instanceof SVGElement)return{element:t,options:(0,yr.default)(t),renderer:Rt.default.SVGRenderer};if(typeof HTMLCanvasElement<"u"&&t instanceof HTMLCanvasElement)return{element:t,options:(0,yr.default)(t),renderer:Rt.default.CanvasRenderer};if(t&&t.getContext)return{element:t,renderer:Rt.default.CanvasRenderer};if(t&&(typeof t>"u"?"undefined":fp(t))==="object"&&!t.nodeName)return{element:t,renderer:Rt.default.ObjectRenderer};throw new pp.InvalidElementException}}function mp(t){var e=document.querySelectorAll(t);if(e.length!==0){for(var n=[],r=0;r<e.length;r++)n.push(_r(e[r]));return n}}function gp(t){var e=document.createElement("canvas");return{element:e,options:(0,yr.default)(t),renderer:Rt.default.CanvasRenderer,afterRender:function(){t.setAttribute("src",e.toDataURL())}}}ar.default=_r;var br={};Object.defineProperty(br,"__esModule",{value:!0});var vp=function(){function t(e,n){for(var r=0;r<n.length;r++){var i=n[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(e,i.key,i)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}();function yp(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}var _p=function(){function t(e){yp(this,t),this.api=e}return vp(t,[{key:"handleCatch",value:function(n){if(n.name==="InvalidInputException")if(this.api._options.valid!==this.api._defaults.valid)this.api._options.valid(!1);else throw n.message;else throw n;this.api.render=function(){}}},{key:"wrapBarcodeCall",value:function(n){try{var r=n.apply(void 0,arguments);return this.api._options.valid(!0),r}catch(i){return this.handleCatch(i),this.api}}}]),t}();br.default=_p;var bp=Fn,lt=Qe(bp),wp=_t,kt=Qe(wp),$p=ir,Qi=Qe($p),Op=or,Zi=Qe(Op),Sp=ar,Ep=Qe(Sp),Cp=an,Ap=Qe(Cp),xp=br,Dp=Qe(xp),eo=bt,Pp=sn,to=Qe(Pp);function Qe(t){return t&&t.__esModule?t:{default:t}}var Ye=function(){},ln=function(e,n,r){var i=new Ye;if(typeof e>"u")throw Error("No element to render on was provided.");return i._renderProperties=(0,Ep.default)(e),i._encodings=[],i._options=to.default,i._errorHandler=new Dp.default(i),typeof n<"u"&&(r=r||{},r.format||(r.format=io()),i.options(r)[r.format](n,r).render()),i};ln.getModule=function(t){return lt.default[t]};for(var no in lt.default)lt.default.hasOwnProperty(no)&&Rp(lt.default,no);function Rp(t,e){Ye.prototype[e]=Ye.prototype[e.toUpperCase()]=Ye.prototype[e.toLowerCase()]=function(n,r){var i=this;return i._errorHandler.wrapBarcodeCall(function(){r.text=typeof r.text>"u"?void 0:""+r.text;var a=(0,kt.default)(i._options,r);a=(0,Ap.default)(a);var o=t[e],s=ro(n,o,a);return i._encodings.push(s),i})}}function ro(t,e,n){t=""+t;var r=new e(t,n);if(!r.valid())throw new eo.InvalidInputException(r.constructor.name,t);var i=r.encode();i=(0,Qi.default)(i);for(var a=0;a<i.length;a++)i[a].options=(0,kt.default)(n,i[a].options);return i}function io(){return lt.default.CODE128?"CODE128":Object.keys(lt.default)[0]}Ye.prototype.options=function(t){return this._options=(0,kt.default)(this._options,t),this},Ye.prototype.blank=function(t){var e=new Array(t+1).join("0");return this._encodings.push({data:e}),this},Ye.prototype.init=function(){if(!!this._renderProperties){Array.isArray(this._renderProperties)||(this._renderProperties=[this._renderProperties]);var t;for(var e in this._renderProperties){t=this._renderProperties[e];var n=(0,kt.default)(this._options,t.options);n.format=="auto"&&(n.format=io()),this._errorHandler.wrapBarcodeCall(function(){var r=n.value,i=lt.default[n.format.toUpperCase()],a=ro(r,i,n);wr(t,a,n)})}}},Ye.prototype.render=function(){if(!this._renderProperties)throw new eo.NoElementException;if(Array.isArray(this._renderProperties))for(var t=0;t<this._renderProperties.length;t++)wr(this._renderProperties[t],this._encodings,this._options);else wr(this._renderProperties,this._encodings,this._options);return this},Ye.prototype._defaults=to.default;function wr(t,e,n){e=(0,Qi.default)(e);for(var r=0;r<e.length;r++)e[r].options=(0,kt.default)(n,e[r].options),(0,Zi.default)(e[r].options);(0,Zi.default)(n);var i=t.renderer,a=new i(t.element,e,n);a.render(),t.afterRender&&t.afterRender()}typeof window<"u"&&(window.JsBarcode=ln),typeof jQuery<"u"&&(jQuery.fn.JsBarcode=function(t,e){var n=[];return jQuery(this).each(function(){n.push(this)}),ln(n,t,e)});var kp=ln;const Vp=l.defineComponent({name:"BarCodeBox",data(){return{}},props:{value:String,format:String,displayValue:{type:Boolean,default:!0},fontSize:Number,textPosition:String,textAlign:String,textMargin:Number,width:{type:Number,default:2},height:{type:Number,default:50},background:String,lineColor:String},methods:{},computed:{},components:{},watch:{$props:{handler(){const t=this.value,e={};Object.keys(this.$props).forEach(n=>{this.$props[n]!=null&&this.$props[n]!==""&&(e[n]=this.$props[n])}),delete e.value,delete e.formCreateInject,this.$nextTick(()=>{kp(this.$refs.bar,t||"",e)})},deep:!0,immediate:!0}}}),Mp={class:"_fc-barcode",ref:"bar"};function Tp(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("img",Mp,null,512)}const Fp=we(Vp,[["render",Tp]]),gm="",jp=l.defineComponent({name:"VideoBox",emits:["pause","play","ended","error"],data(){return{player:null}},props:{src:String,type:String,controls:{type:Boolean,default:!0},autoplay:Boolean,isLive:Boolean,withCredentials:Boolean,loop:Boolean},watch:{src:{handler:function(){this.$nextTick(()=>{Xe.ready("mpegts",()=>{const t=this.$refs.video,e=window.mpegts.createPlayer({isLive:this.isLive,type:this.type,url:this.src});e.attachMediaElement(t),e.on("error",n=>{this.$emit("error",n)}),e.load(),this.autoplay&&e.play().catch(n=>{this.$emit("error",n)}),this.player=e})})},immediate:!0}},created(){window.mpegts?Xe.done("mpegts"):Xe.isDefined("mpegts")||Xe(["https://static.form-create.com/res/mpegts.min.js"],"mpegts")}}),Ip=["controls","loop"];function Bp(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("video",{ref:"video",class:"_fc-video-box",controls:t.controls,loop:t.loop,onPause:e[0]||(e[0]=o=>t.$emit("pause",o)),onPlay:e[1]||(e[1]=o=>t.$emit("play",o)),onEnded:e[2]||(e[2]=o=>t.$emit("ended",o))},null,40,Ip)}const Np=we(jp,[["render",Bp]]);var oo={exports:{}};(function(t,e){(function(n,r){t.exports=r()})(vi,()=>(()=>{var n={873:(o,s)=>{var u,d,h=function(){var _=function(k,O){var b=k,f=q[O],c=null,m=0,v=null,p=[],E={},T=function(g,$){c=function(w){for(var P=new Array(w),V=0;V<w;V+=1){P[V]=new Array(w);for(var H=0;H<w;H+=1)P[V][H]=null}return P}(m=4*b+17),M(0,0),M(m-7,0),M(0,m-7),I(),z(),N(g,$),b>=7&&B(g),v==null&&(v=A(b,f,p)),D(v,$)},M=function(g,$){for(var w=-1;w<=7;w+=1)if(!(g+w<=-1||m<=g+w))for(var P=-1;P<=7;P+=1)$+P<=-1||m<=$+P||(c[g+w][$+P]=0<=w&&w<=6&&(P==0||P==6)||0<=P&&P<=6&&(w==0||w==6)||2<=w&&w<=4&&2<=P&&P<=4)},z=function(){for(var g=8;g<m-8;g+=1)c[g][6]==null&&(c[g][6]=g%2==0);for(var $=8;$<m-8;$+=1)c[6][$]==null&&(c[6][$]=$%2==0)},I=function(){for(var g=U.getPatternPosition(b),$=0;$<g.length;$+=1)for(var w=0;w<g.length;w+=1){var P=g[$],V=g[w];if(c[P][V]==null)for(var H=-2;H<=2;H+=1)for(var W=-2;W<=2;W+=1)c[P+H][V+W]=H==-2||H==2||W==-2||W==2||H==0&&W==0}},B=function(g){for(var $=U.getBCHTypeNumber(b),w=0;w<18;w+=1){var P=!g&&($>>w&1)==1;c[Math.floor(w/3)][w%3+m-8-3]=P}for(w=0;w<18;w+=1)P=!g&&($>>w&1)==1,c[w%3+m-8-3][Math.floor(w/3)]=P},N=function(g,$){for(var w=f<<3|$,P=U.getBCHTypeInfo(w),V=0;V<15;V+=1){var H=!g&&(P>>V&1)==1;V<6?c[V][8]=H:V<8?c[V+1][8]=H:c[m-15+V][8]=H}for(V=0;V<15;V+=1)H=!g&&(P>>V&1)==1,V<8?c[8][m-V-1]=H:V<9?c[8][15-V-1+1]=H:c[8][15-V-1]=H;c[m-8][8]=!g},D=function(g,$){for(var w=-1,P=m-1,V=7,H=0,W=U.getMaskFunction($),ee=m-1;ee>0;ee-=2)for(ee==6&&(ee-=1);;){for(var ae=0;ae<2;ae+=1)if(c[P][ee-ae]==null){var ie=!1;H<g.length&&(ie=(g[H]>>>V&1)==1),W(P,ee-ae)&&(ie=!ie),c[P][ee-ae]=ie,(V-=1)==-1&&(H+=1,V=7)}if((P+=w)<0||m<=P){P-=w,w=-w;break}}},A=function(g,$,w){for(var P=ne.getRSBlocks(g,$),V=Z(),H=0;H<w.length;H+=1){var W=w[H];V.put(W.getMode(),4),V.put(W.getLength(),U.getLengthInBits(W.getMode(),g)),W.write(V)}var ee=0;for(H=0;H<P.length;H+=1)ee+=P[H].dataCount;if(V.getLengthInBits()>8*ee)throw"code length overflow. ("+V.getLengthInBits()+">"+8*ee+")";for(V.getLengthInBits()+4<=8*ee&&V.put(0,4);V.getLengthInBits()%8!=0;)V.putBit(!1);for(;!(V.getLengthInBits()>=8*ee||(V.put(236,8),V.getLengthInBits()>=8*ee));)V.put(17,8);return function(ae,ie){for(var ue=0,ye=0,he=0,fe=new Array(ie.length),le=new Array(ie.length),Q=0;Q<ie.length;Q+=1){var _e=ie[Q].dataCount,be=ie[Q].totalCount-_e;ye=Math.max(ye,_e),he=Math.max(he,be),fe[Q]=new Array(_e);for(var te=0;te<fe[Q].length;te+=1)fe[Q][te]=255&ae.getBuffer()[te+ue];ue+=_e;var xe=U.getErrorCorrectPolynomial(be),Ee=Y(fe[Q],xe.getLength()-1).mod(xe);for(le[Q]=new Array(xe.getLength()-1),te=0;te<le[Q].length;te+=1){var Oe=te+Ee.getLength()-le[Q].length;le[Q][te]=Oe>=0?Ee.getAt(Oe):0}}var fn=0;for(te=0;te<ie.length;te+=1)fn+=ie[te].totalCount;var Vt=new Array(fn),Le=0;for(te=0;te<ye;te+=1)for(Q=0;Q<ie.length;Q+=1)te<fe[Q].length&&(Vt[Le]=fe[Q][te],Le+=1);for(te=0;te<he;te+=1)for(Q=0;Q<ie.length;Q+=1)te<le[Q].length&&(Vt[Le]=le[Q][te],Le+=1);return Vt}(V,P)};E.addData=function(g,$){var w=null;switch($=$||"Byte"){case"Numeric":w=K(g);break;case"Alphanumeric":w=X(g);break;case"Byte":w=ce(g);break;case"Kanji":w=se(g);break;default:throw"mode:"+$}p.push(w),v=null},E.isDark=function(g,$){if(g<0||m<=g||$<0||m<=$)throw g+","+$;return c[g][$]},E.getModuleCount=function(){return m},E.make=function(){if(b<1){for(var g=1;g<40;g++){for(var $=ne.getRSBlocks(g,f),w=Z(),P=0;P<p.length;P++){var V=p[P];w.put(V.getMode(),4),w.put(V.getLength(),U.getLengthInBits(V.getMode(),g)),V.write(w)}var H=0;for(P=0;P<$.length;P++)H+=$[P].dataCount;if(w.getLengthInBits()<=8*H)break}b=g}T(!1,function(){for(var W=0,ee=0,ae=0;ae<8;ae+=1){T(!0,ae);var ie=U.getLostPoint(E);(ae==0||W>ie)&&(W=ie,ee=ae)}return ee}())},E.createTableTag=function(g,$){g=g||2;var w="";w+='<table style="',w+=" border-width: 0px; border-style: none;",w+=" border-collapse: collapse;",w+=" padding: 0px; margin: "+($=$===void 0?4*g:$)+"px;",w+='">',w+="<tbody>";for(var P=0;P<E.getModuleCount();P+=1){w+="<tr>";for(var V=0;V<E.getModuleCount();V+=1)w+='<td style="',w+=" border-width: 0px; border-style: none;",w+=" border-collapse: collapse;",w+=" padding: 0px; margin: 0px;",w+=" width: "+g+"px;",w+=" height: "+g+"px;",w+=" background-color: ",w+=E.isDark(P,V)?"#000000":"#ffffff",w+=";",w+='"/>';w+="</tr>"}return(w+="</tbody>")+"</table>"},E.createSvgTag=function(g,$,w,P){var V={};typeof arguments[0]=="object"&&(g=(V=arguments[0]).cellSize,$=V.margin,w=V.alt,P=V.title),g=g||2,$=$===void 0?4*g:$,(w=typeof w=="string"?{text:w}:w||{}).text=w.text||null,w.id=w.text?w.id||"qrcode-description":null,(P=typeof P=="string"?{text:P}:P||{}).text=P.text||null,P.id=P.text?P.id||"qrcode-title":null;var H,W,ee,ae,ie=E.getModuleCount()*g+2*$,ue="";for(ae="l"+g+",0 0,"+g+" -"+g+",0 0,-"+g+"z ",ue+='<svg version="1.1" xmlns="http://www.w3.org/2000/svg"',ue+=V.scalable?"":' width="'+ie+'px" height="'+ie+'px"',ue+=' viewBox="0 0 '+ie+" "+ie+'" ',ue+=' preserveAspectRatio="xMinYMin meet"',ue+=P.text||w.text?' role="img" aria-labelledby="'+R([P.id,w.id].join(" ").trim())+'"':"",ue+=">",ue+=P.text?'<title id="'+R(P.id)+'">'+R(P.text)+"</title>":"",ue+=w.text?'<description id="'+R(w.id)+'">'+R(w.text)+"</description>":"",ue+='<rect width="100%" height="100%" fill="white" cx="0" cy="0"/>',ue+='<path d="',W=0;W<E.getModuleCount();W+=1)for(ee=W*g+$,H=0;H<E.getModuleCount();H+=1)E.isDark(W,H)&&(ue+="M"+(H*g+$)+","+ee+ae);return(ue+='" stroke="transparent" fill="black"/>')+"</svg>"},E.createDataURL=function(g,$){g=g||2,$=$===void 0?4*g:$;var w=E.getModuleCount()*g+2*$,P=$,V=w-$;return Ne(w,w,function(H,W){if(P<=H&&H<V&&P<=W&&W<V){var ee=Math.floor((H-P)/g),ae=Math.floor((W-P)/g);return E.isDark(ae,ee)?0:1}return 1})},E.createImgTag=function(g,$,w){g=g||2,$=$===void 0?4*g:$;var P=E.getModuleCount()*g+2*$,V="";return V+="<img",V+=' src="',V+=E.createDataURL(g,$),V+='"',V+=' width="',V+=P,V+='"',V+=' height="',V+=P,V+='"',w&&(V+=' alt="',V+=R(w),V+='"'),V+"/>"};var R=function(g){for(var $="",w=0;w<g.length;w+=1){var P=g.charAt(w);switch(P){case"<":$+="&lt;";break;case">":$+="&gt;";break;case"&":$+="&amp;";break;case'"':$+="&quot;";break;default:$+=P}}return $};return E.createASCII=function(g,$){if((g=g||1)<2)return function(fe){fe=fe===void 0?2:fe;var le,Q,_e,be,te,xe=1*E.getModuleCount()+2*fe,Ee=fe,Oe=xe-fe,fn={"\u2588\u2588":"\u2588","\u2588 ":"\u2580"," \u2588":"\u2584","  ":" "},Vt={"\u2588\u2588":"\u2580","\u2588 ":"\u2580"," \u2588":" ","  ":" "},Le="";for(le=0;le<xe;le+=2){for(_e=Math.floor((le-Ee)/1),be=Math.floor((le+1-Ee)/1),Q=0;Q<xe;Q+=1)te="\u2588",Ee<=Q&&Q<Oe&&Ee<=le&&le<Oe&&E.isDark(_e,Math.floor((Q-Ee)/1))&&(te=" "),Ee<=Q&&Q<Oe&&Ee<=le+1&&le+1<Oe&&E.isDark(be,Math.floor((Q-Ee)/1))?te+=" ":te+="\u2588",Le+=fe<1&&le+1>=Oe?Vt[te]:fn[te];Le+=`
`}return xe%2&&fe>0?Le.substring(0,Le.length-xe-1)+Array(xe+1).join("\u2580"):Le.substring(0,Le.length-1)}($);g-=1,$=$===void 0?2*g:$;var w,P,V,H,W=E.getModuleCount()*g+2*$,ee=$,ae=W-$,ie=Array(g+1).join("\u2588\u2588"),ue=Array(g+1).join("  "),ye="",he="";for(w=0;w<W;w+=1){for(V=Math.floor((w-ee)/g),he="",P=0;P<W;P+=1)H=1,ee<=P&&P<ae&&ee<=w&&w<ae&&E.isDark(V,Math.floor((P-ee)/g))&&(H=0),he+=H?ie:ue;for(V=0;V<g;V+=1)ye+=he+`
`}return ye.substring(0,ye.length-1)},E.renderTo2dContext=function(g,$){$=$||2;for(var w=E.getModuleCount(),P=0;P<w;P++)for(var V=0;V<w;V++)g.fillStyle=E.isDark(P,V)?"black":"white",g.fillRect(P*$,V*$,$,$)},E};_.stringToBytes=(_.stringToBytesFuncs={default:function(k){for(var O=[],b=0;b<k.length;b+=1){var f=k.charCodeAt(b);O.push(255&f)}return O}}).default,_.createStringToBytes=function(k,O){var b=function(){for(var c=$e(k),m=function(){var z=c.read();if(z==-1)throw"eof";return z},v=0,p={};;){var E=c.read();if(E==-1)break;var T=m(),M=m()<<8|m();p[String.fromCharCode(E<<8|T)]=M,v+=1}if(v!=O)throw v+" != "+O;return p}(),f="?".charCodeAt(0);return function(c){for(var m=[],v=0;v<c.length;v+=1){var p=c.charCodeAt(v);if(p<128)m.push(p);else{var E=b[c.charAt(v)];typeof E=="number"?(255&E)==E?m.push(E):(m.push(E>>>8),m.push(255&E)):m.push(f)}}return m}};var y,x,S,C,F,q={L:1,M:0,Q:3,H:2},U=(y=[[],[6,18],[6,22],[6,26],[6,30],[6,34],[6,22,38],[6,24,42],[6,26,46],[6,28,50],[6,30,54],[6,32,58],[6,34,62],[6,26,46,66],[6,26,48,70],[6,26,50,74],[6,30,54,78],[6,30,56,82],[6,30,58,86],[6,34,62,90],[6,28,50,72,94],[6,26,50,74,98],[6,30,54,78,102],[6,28,54,80,106],[6,32,58,84,110],[6,30,58,86,114],[6,34,62,90,118],[6,26,50,74,98,122],[6,30,54,78,102,126],[6,26,52,78,104,130],[6,30,56,82,108,134],[6,34,60,86,112,138],[6,30,58,86,114,142],[6,34,62,90,118,146],[6,30,54,78,102,126,150],[6,24,50,76,102,128,154],[6,28,54,80,106,132,158],[6,32,58,84,110,136,162],[6,26,54,82,110,138,166],[6,30,58,86,114,142,170]],x=1335,S=7973,F=function(k){for(var O=0;k!=0;)O+=1,k>>>=1;return O},(C={}).getBCHTypeInfo=function(k){for(var O=k<<10;F(O)-F(x)>=0;)O^=x<<F(O)-F(x);return 21522^(k<<10|O)},C.getBCHTypeNumber=function(k){for(var O=k<<12;F(O)-F(S)>=0;)O^=S<<F(O)-F(S);return k<<12|O},C.getPatternPosition=function(k){return y[k-1]},C.getMaskFunction=function(k){switch(k){case 0:return function(O,b){return(O+b)%2==0};case 1:return function(O,b){return O%2==0};case 2:return function(O,b){return b%3==0};case 3:return function(O,b){return(O+b)%3==0};case 4:return function(O,b){return(Math.floor(O/2)+Math.floor(b/3))%2==0};case 5:return function(O,b){return O*b%2+O*b%3==0};case 6:return function(O,b){return(O*b%2+O*b%3)%2==0};case 7:return function(O,b){return(O*b%3+(O+b)%2)%2==0};default:throw"bad maskPattern:"+k}},C.getErrorCorrectPolynomial=function(k){for(var O=Y([1],0),b=0;b<k;b+=1)O=O.multiply(Y([1,L.gexp(b)],0));return O},C.getLengthInBits=function(k,O){if(1<=O&&O<10)switch(k){case 1:return 10;case 2:return 9;case 4:case 8:return 8;default:throw"mode:"+k}else if(O<27)switch(k){case 1:return 12;case 2:return 11;case 4:return 16;case 8:return 10;default:throw"mode:"+k}else{if(!(O<41))throw"type:"+O;switch(k){case 1:return 14;case 2:return 13;case 4:return 16;case 8:return 12;default:throw"mode:"+k}}},C.getLostPoint=function(k){for(var O=k.getModuleCount(),b=0,f=0;f<O;f+=1)for(var c=0;c<O;c+=1){for(var m=0,v=k.isDark(f,c),p=-1;p<=1;p+=1)if(!(f+p<0||O<=f+p))for(var E=-1;E<=1;E+=1)c+E<0||O<=c+E||p==0&&E==0||v==k.isDark(f+p,c+E)&&(m+=1);m>5&&(b+=3+m-5)}for(f=0;f<O-1;f+=1)for(c=0;c<O-1;c+=1){var T=0;k.isDark(f,c)&&(T+=1),k.isDark(f+1,c)&&(T+=1),k.isDark(f,c+1)&&(T+=1),k.isDark(f+1,c+1)&&(T+=1),T!=0&&T!=4||(b+=3)}for(f=0;f<O;f+=1)for(c=0;c<O-6;c+=1)k.isDark(f,c)&&!k.isDark(f,c+1)&&k.isDark(f,c+2)&&k.isDark(f,c+3)&&k.isDark(f,c+4)&&!k.isDark(f,c+5)&&k.isDark(f,c+6)&&(b+=40);for(c=0;c<O;c+=1)for(f=0;f<O-6;f+=1)k.isDark(f,c)&&!k.isDark(f+1,c)&&k.isDark(f+2,c)&&k.isDark(f+3,c)&&k.isDark(f+4,c)&&!k.isDark(f+5,c)&&k.isDark(f+6,c)&&(b+=40);var M=0;for(c=0;c<O;c+=1)for(f=0;f<O;f+=1)k.isDark(f,c)&&(M+=1);return b+Math.abs(100*M/O/O-50)/5*10},C),L=function(){for(var k=new Array(256),O=new Array(256),b=0;b<8;b+=1)k[b]=1<<b;for(b=8;b<256;b+=1)k[b]=k[b-4]^k[b-5]^k[b-6]^k[b-8];for(b=0;b<255;b+=1)O[k[b]]=b;return{glog:function(f){if(f<1)throw"glog("+f+")";return O[f]},gexp:function(f){for(;f<0;)f+=255;for(;f>=256;)f-=255;return k[f]}}}();function Y(k,O){if(k.length===void 0)throw k.length+"/"+O;var b=function(){for(var c=0;c<k.length&&k[c]==0;)c+=1;for(var m=new Array(k.length-c+O),v=0;v<k.length-c;v+=1)m[v]=k[v+c];return m}(),f={getAt:function(c){return b[c]},getLength:function(){return b.length},multiply:function(c){for(var m=new Array(f.getLength()+c.getLength()-1),v=0;v<f.getLength();v+=1)for(var p=0;p<c.getLength();p+=1)m[v+p]^=L.gexp(L.glog(f.getAt(v))+L.glog(c.getAt(p)));return Y(m,0)},mod:function(c){if(f.getLength()-c.getLength()<0)return f;for(var m=L.glog(f.getAt(0))-L.glog(c.getAt(0)),v=new Array(f.getLength()),p=0;p<f.getLength();p+=1)v[p]=f.getAt(p);for(p=0;p<c.getLength();p+=1)v[p]^=L.gexp(L.glog(c.getAt(p))+m);return Y(v,0).mod(c)}};return f}var ne=function(){var k=[[1,26,19],[1,26,16],[1,26,13],[1,26,9],[1,44,34],[1,44,28],[1,44,22],[1,44,16],[1,70,55],[1,70,44],[2,35,17],[2,35,13],[1,100,80],[2,50,32],[2,50,24],[4,25,9],[1,134,108],[2,67,43],[2,33,15,2,34,16],[2,33,11,2,34,12],[2,86,68],[4,43,27],[4,43,19],[4,43,15],[2,98,78],[4,49,31],[2,32,14,4,33,15],[4,39,13,1,40,14],[2,121,97],[2,60,38,2,61,39],[4,40,18,2,41,19],[4,40,14,2,41,15],[2,146,116],[3,58,36,2,59,37],[4,36,16,4,37,17],[4,36,12,4,37,13],[2,86,68,2,87,69],[4,69,43,1,70,44],[6,43,19,2,44,20],[6,43,15,2,44,16],[4,101,81],[1,80,50,4,81,51],[4,50,22,4,51,23],[3,36,12,8,37,13],[2,116,92,2,117,93],[6,58,36,2,59,37],[4,46,20,6,47,21],[7,42,14,4,43,15],[4,133,107],[8,59,37,1,60,38],[8,44,20,4,45,21],[12,33,11,4,34,12],[3,145,115,1,146,116],[4,64,40,5,65,41],[11,36,16,5,37,17],[11,36,12,5,37,13],[5,109,87,1,110,88],[5,65,41,5,66,42],[5,54,24,7,55,25],[11,36,12,7,37,13],[5,122,98,1,123,99],[7,73,45,3,74,46],[15,43,19,2,44,20],[3,45,15,13,46,16],[1,135,107,5,136,108],[10,74,46,1,75,47],[1,50,22,15,51,23],[2,42,14,17,43,15],[5,150,120,1,151,121],[9,69,43,4,70,44],[17,50,22,1,51,23],[2,42,14,19,43,15],[3,141,113,4,142,114],[3,70,44,11,71,45],[17,47,21,4,48,22],[9,39,13,16,40,14],[3,135,107,5,136,108],[3,67,41,13,68,42],[15,54,24,5,55,25],[15,43,15,10,44,16],[4,144,116,4,145,117],[17,68,42],[17,50,22,6,51,23],[19,46,16,6,47,17],[2,139,111,7,140,112],[17,74,46],[7,54,24,16,55,25],[34,37,13],[4,151,121,5,152,122],[4,75,47,14,76,48],[11,54,24,14,55,25],[16,45,15,14,46,16],[6,147,117,4,148,118],[6,73,45,14,74,46],[11,54,24,16,55,25],[30,46,16,2,47,17],[8,132,106,4,133,107],[8,75,47,13,76,48],[7,54,24,22,55,25],[22,45,15,13,46,16],[10,142,114,2,143,115],[19,74,46,4,75,47],[28,50,22,6,51,23],[33,46,16,4,47,17],[8,152,122,4,153,123],[22,73,45,3,74,46],[8,53,23,26,54,24],[12,45,15,28,46,16],[3,147,117,10,148,118],[3,73,45,23,74,46],[4,54,24,31,55,25],[11,45,15,31,46,16],[7,146,116,7,147,117],[21,73,45,7,74,46],[1,53,23,37,54,24],[19,45,15,26,46,16],[5,145,115,10,146,116],[19,75,47,10,76,48],[15,54,24,25,55,25],[23,45,15,25,46,16],[13,145,115,3,146,116],[2,74,46,29,75,47],[42,54,24,1,55,25],[23,45,15,28,46,16],[17,145,115],[10,74,46,23,75,47],[10,54,24,35,55,25],[19,45,15,35,46,16],[17,145,115,1,146,116],[14,74,46,21,75,47],[29,54,24,19,55,25],[11,45,15,46,46,16],[13,145,115,6,146,116],[14,74,46,23,75,47],[44,54,24,7,55,25],[59,46,16,1,47,17],[12,151,121,7,152,122],[12,75,47,26,76,48],[39,54,24,14,55,25],[22,45,15,41,46,16],[6,151,121,14,152,122],[6,75,47,34,76,48],[46,54,24,10,55,25],[2,45,15,64,46,16],[17,152,122,4,153,123],[29,74,46,14,75,47],[49,54,24,10,55,25],[24,45,15,46,46,16],[4,152,122,18,153,123],[13,74,46,32,75,47],[48,54,24,14,55,25],[42,45,15,32,46,16],[20,147,117,4,148,118],[40,75,47,7,76,48],[43,54,24,22,55,25],[10,45,15,67,46,16],[19,148,118,6,149,119],[18,75,47,31,76,48],[34,54,24,34,55,25],[20,45,15,61,46,16]],O=function(f,c){var m={};return m.totalCount=f,m.dataCount=c,m},b={getRSBlocks:function(f,c){var m=function(B,N){switch(N){case q.L:return k[4*(B-1)+0];case q.M:return k[4*(B-1)+1];case q.Q:return k[4*(B-1)+2];case q.H:return k[4*(B-1)+3];default:return}}(f,c);if(m===void 0)throw"bad rs block @ typeNumber:"+f+"/errorCorrectionLevel:"+c;for(var v=m.length/3,p=[],E=0;E<v;E+=1)for(var T=m[3*E+0],M=m[3*E+1],z=m[3*E+2],I=0;I<T;I+=1)p.push(O(M,z));return p}};return b}(),Z=function(){var k=[],O=0,b={getBuffer:function(){return k},getAt:function(f){var c=Math.floor(f/8);return(k[c]>>>7-f%8&1)==1},put:function(f,c){for(var m=0;m<c;m+=1)b.putBit((f>>>c-m-1&1)==1)},getLengthInBits:function(){return O},putBit:function(f){var c=Math.floor(O/8);k.length<=c&&k.push(0),f&&(k[c]|=128>>>O%8),O+=1}};return b},K=function(k){var O=k,b={getMode:function(){return 1},getLength:function(m){return O.length},write:function(m){for(var v=O,p=0;p+2<v.length;)m.put(f(v.substring(p,p+3)),10),p+=3;p<v.length&&(v.length-p==1?m.put(f(v.substring(p,p+1)),4):v.length-p==2&&m.put(f(v.substring(p,p+2)),7))}},f=function(m){for(var v=0,p=0;p<m.length;p+=1)v=10*v+c(m.charAt(p));return v},c=function(m){if("0"<=m&&m<="9")return m.charCodeAt(0)-"0".charCodeAt(0);throw"illegal char :"+m};return b},X=function(k){var O=k,b={getMode:function(){return 2},getLength:function(c){return O.length},write:function(c){for(var m=O,v=0;v+1<m.length;)c.put(45*f(m.charAt(v))+f(m.charAt(v+1)),11),v+=2;v<m.length&&c.put(f(m.charAt(v)),6)}},f=function(c){if("0"<=c&&c<="9")return c.charCodeAt(0)-"0".charCodeAt(0);if("A"<=c&&c<="Z")return c.charCodeAt(0)-"A".charCodeAt(0)+10;switch(c){case" ":return 36;case"$":return 37;case"%":return 38;case"*":return 39;case"+":return 40;case"-":return 41;case".":return 42;case"/":return 43;case":":return 44;default:throw"illegal char :"+c}};return b},ce=function(k){var O=_.stringToBytes(k);return{getMode:function(){return 4},getLength:function(b){return O.length},write:function(b){for(var f=0;f<O.length;f+=1)b.put(O[f],8)}}},se=function(k){var O=_.stringToBytesFuncs.SJIS;if(!O)throw"sjis not supported.";(function(){var c=O("\u53CB");if(c.length!=2||(c[0]<<8|c[1])!=38726)throw"sjis not supported."})();var b=O(k),f={getMode:function(){return 8},getLength:function(c){return~~(b.length/2)},write:function(c){for(var m=b,v=0;v+1<m.length;){var p=(255&m[v])<<8|255&m[v+1];if(33088<=p&&p<=40956)p-=33088;else{if(!(57408<=p&&p<=60351))throw"illegal char at "+(v+1)+"/"+p;p-=49472}p=192*(p>>>8&255)+(255&p),c.put(p,13),v+=2}if(v<m.length)throw"illegal char at "+(v+1)}};return f},re=function(){var k=[],O={writeByte:function(b){k.push(255&b)},writeShort:function(b){O.writeByte(b),O.writeByte(b>>>8)},writeBytes:function(b,f,c){f=f||0,c=c||b.length;for(var m=0;m<c;m+=1)O.writeByte(b[m+f])},writeString:function(b){for(var f=0;f<b.length;f+=1)O.writeByte(b.charCodeAt(f))},toByteArray:function(){return k},toString:function(){var b="";b+="[";for(var f=0;f<k.length;f+=1)f>0&&(b+=","),b+=k[f];return b+"]"}};return O},$e=function(k){var O=k,b=0,f=0,c=0,m={read:function(){for(;c<8;){if(b>=O.length){if(c==0)return-1;throw"unexpected end of file./"+c}var p=O.charAt(b);if(b+=1,p=="=")return c=0,-1;p.match(/^\s$/)||(f=f<<6|v(p.charCodeAt(0)),c+=6)}var E=f>>>c-8&255;return c-=8,E}},v=function(p){if(65<=p&&p<=90)return p-65;if(97<=p&&p<=122)return p-97+26;if(48<=p&&p<=57)return p-48+52;if(p==43)return 62;if(p==47)return 63;throw"c:"+p};return m},Ne=function(k,O,b){for(var f=function(M,z){var I=M,B=z,N=new Array(M*z),D={setPixel:function(g,$,w){N[$*I+g]=w},write:function(g){g.writeString("GIF87a"),g.writeShort(I),g.writeShort(B),g.writeByte(128),g.writeByte(0),g.writeByte(0),g.writeByte(0),g.writeByte(0),g.writeByte(0),g.writeByte(255),g.writeByte(255),g.writeByte(255),g.writeString(","),g.writeShort(0),g.writeShort(0),g.writeShort(I),g.writeShort(B),g.writeByte(0);var $=A(2);g.writeByte(2);for(var w=0;$.length-w>255;)g.writeByte(255),g.writeBytes($,w,255),w+=255;g.writeByte($.length-w),g.writeBytes($,w,$.length-w),g.writeByte(0),g.writeString(";")}},A=function(g){for(var $=1<<g,w=1+(1<<g),P=g+1,V=R(),H=0;H<$;H+=1)V.add(String.fromCharCode(H));V.add(String.fromCharCode($)),V.add(String.fromCharCode(w));var W,ee,ae,ie=re(),ue=(W=ie,ee=0,ae=0,{write:function(le,Q){if(le>>>Q!=0)throw"length over";for(;ee+Q>=8;)W.writeByte(255&(le<<ee|ae)),Q-=8-ee,le>>>=8-ee,ae=0,ee=0;ae|=le<<ee,ee+=Q},flush:function(){ee>0&&W.writeByte(ae)}});ue.write($,P);var ye=0,he=String.fromCharCode(N[ye]);for(ye+=1;ye<N.length;){var fe=String.fromCharCode(N[ye]);ye+=1,V.contains(he+fe)?he+=fe:(ue.write(V.indexOf(he),P),V.size()<4095&&(V.size()==1<<P&&(P+=1),V.add(he+fe)),he=fe)}return ue.write(V.indexOf(he),P),ue.write(w,P),ue.flush(),ie.toByteArray()},R=function(){var g={},$=0,w={add:function(P){if(w.contains(P))throw"dup key:"+P;g[P]=$,$+=1},size:function(){return $},indexOf:function(P){return g[P]},contains:function(P){return g[P]!==void 0}};return w};return D}(k,O),c=0;c<O;c+=1)for(var m=0;m<k;m+=1)f.setPixel(m,c,b(m,c));var v=re();f.write(v);for(var p=function(){var M=0,z=0,I=0,B="",N={},D=function(R){B+=String.fromCharCode(A(63&R))},A=function(R){if(!(R<0)){if(R<26)return 65+R;if(R<52)return R-26+97;if(R<62)return R-52+48;if(R==62)return 43;if(R==63)return 47}throw"n:"+R};return N.writeByte=function(R){for(M=M<<8|255&R,z+=8,I+=1;z>=6;)D(M>>>z-6),z-=6},N.flush=function(){if(z>0&&(D(M<<6-z),M=0,z=0),I%3!=0)for(var R=3-I%3,g=0;g<R;g+=1)B+="="},N.toString=function(){return B},N}(),E=v.toByteArray(),T=0;T<E.length;T+=1)p.writeByte(E[T]);return p.flush(),"data:image/gif;base64,"+p};return _}();h.stringToBytesFuncs["UTF-8"]=function(_){return function(y){for(var x=[],S=0;S<y.length;S++){var C=y.charCodeAt(S);C<128?x.push(C):C<2048?x.push(192|C>>6,128|63&C):C<55296||C>=57344?x.push(224|C>>12,128|C>>6&63,128|63&C):(S++,C=65536+((1023&C)<<10|1023&y.charCodeAt(S)),x.push(240|C>>18,128|C>>12&63,128|C>>6&63,128|63&C))}return x}(_)},(d=typeof(u=function(){return h})=="function"?u.apply(s,[]):u)===void 0||(o.exports=d)}},r={};function i(o){var s=r[o];if(s!==void 0)return s.exports;var u=r[o]={exports:{}};return n[o](u,u.exports,i),u.exports}i.n=o=>{var s=o&&o.__esModule?()=>o.default:()=>o;return i.d(s,{a:s}),s},i.d=(o,s)=>{for(var u in s)i.o(s,u)&&!i.o(o,u)&&Object.defineProperty(o,u,{enumerable:!0,get:s[u]})},i.o=(o,s)=>Object.prototype.hasOwnProperty.call(o,s);var a={};return(()=>{i.d(a,{default:()=>O});const o=b=>!!b&&typeof b=="object"&&!Array.isArray(b);function s(b,...f){if(!f.length)return b;const c=f.shift();return c!==void 0&&o(b)&&o(c)?(b=Object.assign({},b),Object.keys(c).forEach(m=>{const v=b[m],p=c[m];Array.isArray(v)&&Array.isArray(p)?b[m]=p:o(v)&&o(p)?b[m]=s(Object.assign({},v),p):b[m]=p}),s(b,...f)):b}function u(b,f){const c=document.createElement("a");c.download=f,c.href=b,document.body.appendChild(c),c.click(),document.body.removeChild(c)}const d={L:.07,M:.15,Q:.25,H:.3};class h{constructor({svg:f,type:c,window:m}){this._svg=f,this._type=c,this._window=m}draw(f,c,m,v){let p;switch(this._type){case"dots":p=this._drawDot;break;case"classy":p=this._drawClassy;break;case"classy-rounded":p=this._drawClassyRounded;break;case"rounded":p=this._drawRounded;break;case"extra-rounded":p=this._drawExtraRounded;break;default:p=this._drawSquare}p.call(this,{x:f,y:c,size:m,getNeighbor:v})}_rotateFigure({x:f,y:c,size:m,rotation:v=0,draw:p}){var E;const T=f+m/2,M=c+m/2;p(),(E=this._element)===null||E===void 0||E.setAttribute("transform",`rotate(${180*v/Math.PI},${T},${M})`)}_basicDot(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(m+c/2)),this._element.setAttribute("cy",String(v+c/2)),this._element.setAttribute("r",String(c/2))}}))}_basicSquare(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(m)),this._element.setAttribute("y",String(v)),this._element.setAttribute("width",String(c)),this._element.setAttribute("height",String(c))}}))}_basicSideRounded(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${m} ${v}v ${c}h `+c/2+`a ${c/2} ${c/2}, 0, 0, 0, 0 ${-c}`)}}))}_basicCornerRounded(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${m} ${v}v ${c}h ${c}v `+-c/2+`a ${c/2} ${c/2}, 0, 0, 0, ${-c/2} ${-c/2}`)}}))}_basicCornerExtraRounded(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${m} ${v}v ${c}h ${c}a ${c} ${c}, 0, 0, 0, ${-c} ${-c}`)}}))}_basicCornersRounded(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("d",`M ${m} ${v}v `+c/2+`a ${c/2} ${c/2}, 0, 0, 0, ${c/2} ${c/2}h `+c/2+"v "+-c/2+`a ${c/2} ${c/2}, 0, 0, 0, ${-c/2} ${-c/2}`)}}))}_drawDot({x:f,y:c,size:m}){this._basicDot({x:f,y:c,size:m,rotation:0})}_drawSquare({x:f,y:c,size:m}){this._basicSquare({x:f,y:c,size:m,rotation:0})}_drawRounded({x:f,y:c,size:m,getNeighbor:v}){const p=v?+v(-1,0):0,E=v?+v(1,0):0,T=v?+v(0,-1):0,M=v?+v(0,1):0,z=p+E+T+M;if(z!==0)if(z>2||p&&E||T&&M)this._basicSquare({x:f,y:c,size:m,rotation:0});else{if(z===2){let I=0;return p&&T?I=Math.PI/2:T&&E?I=Math.PI:E&&M&&(I=-Math.PI/2),void this._basicCornerRounded({x:f,y:c,size:m,rotation:I})}if(z===1){let I=0;return T?I=Math.PI/2:E?I=Math.PI:M&&(I=-Math.PI/2),void this._basicSideRounded({x:f,y:c,size:m,rotation:I})}}else this._basicDot({x:f,y:c,size:m,rotation:0})}_drawExtraRounded({x:f,y:c,size:m,getNeighbor:v}){const p=v?+v(-1,0):0,E=v?+v(1,0):0,T=v?+v(0,-1):0,M=v?+v(0,1):0,z=p+E+T+M;if(z!==0)if(z>2||p&&E||T&&M)this._basicSquare({x:f,y:c,size:m,rotation:0});else{if(z===2){let I=0;return p&&T?I=Math.PI/2:T&&E?I=Math.PI:E&&M&&(I=-Math.PI/2),void this._basicCornerExtraRounded({x:f,y:c,size:m,rotation:I})}if(z===1){let I=0;return T?I=Math.PI/2:E?I=Math.PI:M&&(I=-Math.PI/2),void this._basicSideRounded({x:f,y:c,size:m,rotation:I})}}else this._basicDot({x:f,y:c,size:m,rotation:0})}_drawClassy({x:f,y:c,size:m,getNeighbor:v}){const p=v?+v(-1,0):0,E=v?+v(1,0):0,T=v?+v(0,-1):0,M=v?+v(0,1):0;p+E+T+M!==0?p||T?E||M?this._basicSquare({x:f,y:c,size:m,rotation:0}):this._basicCornerRounded({x:f,y:c,size:m,rotation:Math.PI/2}):this._basicCornerRounded({x:f,y:c,size:m,rotation:-Math.PI/2}):this._basicCornersRounded({x:f,y:c,size:m,rotation:Math.PI/2})}_drawClassyRounded({x:f,y:c,size:m,getNeighbor:v}){const p=v?+v(-1,0):0,E=v?+v(1,0):0,T=v?+v(0,-1):0,M=v?+v(0,1):0;p+E+T+M!==0?p||T?E||M?this._basicSquare({x:f,y:c,size:m,rotation:0}):this._basicCornerExtraRounded({x:f,y:c,size:m,rotation:Math.PI/2}):this._basicCornerExtraRounded({x:f,y:c,size:m,rotation:-Math.PI/2}):this._basicCornersRounded({x:f,y:c,size:m,rotation:Math.PI/2})}}const _={dot:"dot",square:"square",extraRounded:"extra-rounded"},y=Object.values(_);class x{constructor({svg:f,type:c,window:m}){this._svg=f,this._type=c,this._window=m}draw(f,c,m,v){let p;switch(this._type){case _.square:p=this._drawSquare;break;case _.extraRounded:p=this._drawExtraRounded;break;default:p=this._drawDot}p.call(this,{x:f,y:c,size:m,rotation:v})}_rotateFigure({x:f,y:c,size:m,rotation:v=0,draw:p}){var E;const T=f+m/2,M=c+m/2;p(),(E=this._element)===null||E===void 0||E.setAttribute("transform",`rotate(${180*v/Math.PI},${T},${M})`)}_basicDot(f){const{size:c,x:m,y:v}=f,p=c/7;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${m+c/2} ${v}a ${c/2} ${c/2} 0 1 0 0.1 0zm 0 ${p}a ${c/2-p} ${c/2-p} 0 1 1 -0.1 0Z`)}}))}_basicSquare(f){const{size:c,x:m,y:v}=f,p=c/7;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${m} ${v}v ${c}h ${c}v `+-c+`zM ${m+p} ${v+p}h `+(c-2*p)+"v "+(c-2*p)+"h "+(2*p-c)+"z")}}))}_basicExtraRounded(f){const{size:c,x:m,y:v}=f,p=c/7;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","path"),this._element.setAttribute("clip-rule","evenodd"),this._element.setAttribute("d",`M ${m} ${v+2.5*p}v `+2*p+`a ${2.5*p} ${2.5*p}, 0, 0, 0, ${2.5*p} ${2.5*p}h `+2*p+`a ${2.5*p} ${2.5*p}, 0, 0, 0, ${2.5*p} ${2.5*-p}v `+-2*p+`a ${2.5*p} ${2.5*p}, 0, 0, 0, ${2.5*-p} ${2.5*-p}h `+-2*p+`a ${2.5*p} ${2.5*p}, 0, 0, 0, ${2.5*-p} ${2.5*p}M ${m+2.5*p} ${v+p}h `+2*p+`a ${1.5*p} ${1.5*p}, 0, 0, 1, ${1.5*p} ${1.5*p}v `+2*p+`a ${1.5*p} ${1.5*p}, 0, 0, 1, ${1.5*-p} ${1.5*p}h `+-2*p+`a ${1.5*p} ${1.5*p}, 0, 0, 1, ${1.5*-p} ${1.5*-p}v `+-2*p+`a ${1.5*p} ${1.5*p}, 0, 0, 1, ${1.5*p} ${1.5*-p}`)}}))}_drawDot({x:f,y:c,size:m,rotation:v}){this._basicDot({x:f,y:c,size:m,rotation:v})}_drawSquare({x:f,y:c,size:m,rotation:v}){this._basicSquare({x:f,y:c,size:m,rotation:v})}_drawExtraRounded({x:f,y:c,size:m,rotation:v}){this._basicExtraRounded({x:f,y:c,size:m,rotation:v})}}const S={dot:"dot",square:"square"},C=Object.values(S);class F{constructor({svg:f,type:c,window:m}){this._svg=f,this._type=c,this._window=m}draw(f,c,m,v){let p;p=this._type===S.square?this._drawSquare:this._drawDot,p.call(this,{x:f,y:c,size:m,rotation:v})}_rotateFigure({x:f,y:c,size:m,rotation:v=0,draw:p}){var E;const T=f+m/2,M=c+m/2;p(),(E=this._element)===null||E===void 0||E.setAttribute("transform",`rotate(${180*v/Math.PI},${T},${M})`)}_basicDot(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","circle"),this._element.setAttribute("cx",String(m+c/2)),this._element.setAttribute("cy",String(v+c/2)),this._element.setAttribute("r",String(c/2))}}))}_basicSquare(f){const{size:c,x:m,y:v}=f;this._rotateFigure(Object.assign(Object.assign({},f),{draw:()=>{this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect"),this._element.setAttribute("x",String(m)),this._element.setAttribute("y",String(v)),this._element.setAttribute("width",String(c)),this._element.setAttribute("height",String(c))}}))}_drawDot({x:f,y:c,size:m,rotation:v}){this._basicDot({x:f,y:c,size:m,rotation:v})}_drawSquare({x:f,y:c,size:m,rotation:v}){this._basicSquare({x:f,y:c,size:m,rotation:v})}}const q="circle",U=[[1,1,1,1,1,1,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,0,0,0,0,0,1],[1,1,1,1,1,1,1]],L=[[0,0,0,0,0,0,0],[0,0,0,0,0,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,1,1,1,0,0],[0,0,0,0,0,0,0],[0,0,0,0,0,0,0]];class Y{constructor(f,c){this._roundSize=m=>this._options.dotsOptions.roundSize?Math.floor(m):m,this._window=c,this._element=this._window.document.createElementNS("http://www.w3.org/2000/svg","svg"),this._element.setAttribute("width",String(f.width)),this._element.setAttribute("height",String(f.height)),this._element.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),f.dotsOptions.roundSize||this._element.setAttribute("shape-rendering","crispEdges"),this._element.setAttribute("viewBox",`0 0 ${f.width} ${f.height}`),this._defs=this._window.document.createElementNS("http://www.w3.org/2000/svg","defs"),this._element.appendChild(this._defs),this._imageUri=f.image,this._instanceId=Y.instanceCount++,this._options=f}get width(){return this._options.width}get height(){return this._options.height}getElement(){return this._element}async drawQR(f){const c=f.getModuleCount(),m=Math.min(this._options.width,this._options.height)-2*this._options.margin,v=this._options.shape===q?m/Math.sqrt(2):m,p=this._roundSize(v/c);let E={hideXDots:0,hideYDots:0,width:0,height:0};if(this._qr=f,this._options.image){if(await this.loadImage(),!this._image)return;const{imageOptions:T,qrOptions:M}=this._options,z=T.imageSize*d[M.errorCorrectionLevel],I=Math.floor(z*c*c);E=function({originalHeight:B,originalWidth:N,maxHiddenDots:D,maxHiddenAxisDots:A,dotSize:R}){const g={x:0,y:0},$={x:0,y:0};if(B<=0||N<=0||D<=0||R<=0)return{height:0,width:0,hideYDots:0,hideXDots:0};const w=B/N;return g.x=Math.floor(Math.sqrt(D/w)),g.x<=0&&(g.x=1),A&&A<g.x&&(g.x=A),g.x%2==0&&g.x--,$.x=g.x*R,g.y=1+2*Math.ceil((g.x*w-1)/2),$.y=Math.round($.x*w),(g.y*g.x>D||A&&A<g.y)&&(A&&A<g.y?(g.y=A,g.y%2==0&&g.x--):g.y-=2,$.y=g.y*R,g.x=1+2*Math.ceil((g.y/w-1)/2),$.x=Math.round($.y/w)),{height:$.y,width:$.x,hideYDots:g.y,hideXDots:g.x}}({originalWidth:this._image.width,originalHeight:this._image.height,maxHiddenDots:I,maxHiddenAxisDots:c-14,dotSize:p})}this.drawBackground(),this.drawDots((T,M)=>{var z,I,B,N,D,A;return!(this._options.imageOptions.hideBackgroundDots&&T>=(c-E.hideYDots)/2&&T<(c+E.hideYDots)/2&&M>=(c-E.hideXDots)/2&&M<(c+E.hideXDots)/2||((z=U[T])===null||z===void 0?void 0:z[M])||((I=U[T-c+7])===null||I===void 0?void 0:I[M])||((B=U[T])===null||B===void 0?void 0:B[M-c+7])||((N=L[T])===null||N===void 0?void 0:N[M])||((D=L[T-c+7])===null||D===void 0?void 0:D[M])||((A=L[T])===null||A===void 0?void 0:A[M-c+7]))}),this.drawCorners(),this._options.image&&await this.drawImage({width:E.width,height:E.height,count:c,dotSize:p})}drawBackground(){var f,c,m;const v=this._element,p=this._options;if(v){const E=(f=p.backgroundOptions)===null||f===void 0?void 0:f.gradient,T=(c=p.backgroundOptions)===null||c===void 0?void 0:c.color;let M=p.height,z=p.width;if(E||T){const I=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");this._backgroundClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._backgroundClipPath.setAttribute("id",`clip-path-background-color-${this._instanceId}`),this._defs.appendChild(this._backgroundClipPath),!((m=p.backgroundOptions)===null||m===void 0)&&m.round&&(M=z=Math.min(p.width,p.height),I.setAttribute("rx",String(M/2*p.backgroundOptions.round))),I.setAttribute("x",String(this._roundSize((p.width-z)/2))),I.setAttribute("y",String(this._roundSize((p.height-M)/2))),I.setAttribute("width",String(z)),I.setAttribute("height",String(M)),this._backgroundClipPath.appendChild(I),this._createColor({options:E,color:T,additionalRotation:0,x:0,y:0,height:p.height,width:p.width,name:`background-color-${this._instanceId}`})}}}drawDots(f){var c,m;if(!this._qr)throw"QR code is not defined";const v=this._options,p=this._qr.getModuleCount();if(p>v.width||p>v.height)throw"The canvas is too small.";const E=Math.min(v.width,v.height)-2*v.margin,T=v.shape===q?E/Math.sqrt(2):E,M=this._roundSize(T/p),z=this._roundSize((v.width-p*M)/2),I=this._roundSize((v.height-p*M)/2),B=new h({svg:this._element,type:v.dotsOptions.type,window:this._window});this._dotsClipPath=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),this._dotsClipPath.setAttribute("id",`clip-path-dot-color-${this._instanceId}`),this._defs.appendChild(this._dotsClipPath),this._createColor({options:(c=v.dotsOptions)===null||c===void 0?void 0:c.gradient,color:v.dotsOptions.color,additionalRotation:0,x:0,y:0,height:v.height,width:v.width,name:`dot-color-${this._instanceId}`});for(let N=0;N<p;N++)for(let D=0;D<p;D++)f&&!f(N,D)||!((m=this._qr)===null||m===void 0)&&m.isDark(N,D)&&(B.draw(z+D*M,I+N*M,M,(A,R)=>!(D+A<0||N+R<0||D+A>=p||N+R>=p)&&!(f&&!f(N+R,D+A))&&!!this._qr&&this._qr.isDark(N+R,D+A)),B._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(B._element));if(v.shape===q){const N=this._roundSize((E/M-p)/2),D=p+2*N,A=z-N*M,R=I-N*M,g=[],$=this._roundSize(D/2);for(let w=0;w<D;w++){g[w]=[];for(let P=0;P<D;P++)w>=N-1&&w<=D-N&&P>=N-1&&P<=D-N||Math.sqrt((w-$)*(w-$)+(P-$)*(P-$))>$?g[w][P]=0:g[w][P]=this._qr.isDark(P-2*N<0?P:P>=p?P-2*N:P-N,w-2*N<0?w:w>=p?w-2*N:w-N)?1:0}for(let w=0;w<D;w++)for(let P=0;P<D;P++)g[w][P]&&(B.draw(A+P*M,R+w*M,M,(V,H)=>{var W;return!!(!((W=g[w+H])===null||W===void 0)&&W[P+V])}),B._element&&this._dotsClipPath&&this._dotsClipPath.appendChild(B._element))}}drawCorners(){if(!this._qr)throw"QR code is not defined";const f=this._element,c=this._options;if(!f)throw"Element code is not defined";const m=this._qr.getModuleCount(),v=Math.min(c.width,c.height)-2*c.margin,p=c.shape===q?v/Math.sqrt(2):v,E=this._roundSize(p/m),T=7*E,M=3*E,z=this._roundSize((c.width-m*E)/2),I=this._roundSize((c.height-m*E)/2);[[0,0,0],[1,0,Math.PI/2],[0,1,-Math.PI/2]].forEach(([B,N,D])=>{var A,R,g,$,w,P,V,H,W,ee,ae,ie,ue,ye;const he=z+B*E*(m-7),fe=I+N*E*(m-7);let le=this._dotsClipPath,Q=this._dotsClipPath;if((((A=c.cornersSquareOptions)===null||A===void 0?void 0:A.gradient)||((R=c.cornersSquareOptions)===null||R===void 0?void 0:R.color))&&(le=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),le.setAttribute("id",`clip-path-corners-square-color-${B}-${N}-${this._instanceId}`),this._defs.appendChild(le),this._cornersSquareClipPath=this._cornersDotClipPath=Q=le,this._createColor({options:(g=c.cornersSquareOptions)===null||g===void 0?void 0:g.gradient,color:($=c.cornersSquareOptions)===null||$===void 0?void 0:$.color,additionalRotation:D,x:he,y:fe,height:T,width:T,name:`corners-square-color-${B}-${N}-${this._instanceId}`})),((w=c.cornersSquareOptions)===null||w===void 0?void 0:w.type)&&y.includes(c.cornersSquareOptions.type)){const _e=new x({svg:this._element,type:c.cornersSquareOptions.type,window:this._window});_e.draw(he,fe,T,D),_e._element&&le&&le.appendChild(_e._element)}else{const _e=new h({svg:this._element,type:((P=c.cornersSquareOptions)===null||P===void 0?void 0:P.type)||c.dotsOptions.type,window:this._window});for(let be=0;be<U.length;be++)for(let te=0;te<U[be].length;te++)!((V=U[be])===null||V===void 0)&&V[te]&&(_e.draw(he+te*E,fe+be*E,E,(xe,Ee)=>{var Oe;return!!(!((Oe=U[be+Ee])===null||Oe===void 0)&&Oe[te+xe])}),_e._element&&le&&le.appendChild(_e._element))}if((((H=c.cornersDotOptions)===null||H===void 0?void 0:H.gradient)||((W=c.cornersDotOptions)===null||W===void 0?void 0:W.color))&&(Q=this._window.document.createElementNS("http://www.w3.org/2000/svg","clipPath"),Q.setAttribute("id",`clip-path-corners-dot-color-${B}-${N}-${this._instanceId}`),this._defs.appendChild(Q),this._cornersDotClipPath=Q,this._createColor({options:(ee=c.cornersDotOptions)===null||ee===void 0?void 0:ee.gradient,color:(ae=c.cornersDotOptions)===null||ae===void 0?void 0:ae.color,additionalRotation:D,x:he+2*E,y:fe+2*E,height:M,width:M,name:`corners-dot-color-${B}-${N}-${this._instanceId}`})),((ie=c.cornersDotOptions)===null||ie===void 0?void 0:ie.type)&&C.includes(c.cornersDotOptions.type)){const _e=new F({svg:this._element,type:c.cornersDotOptions.type,window:this._window});_e.draw(he+2*E,fe+2*E,M,D),_e._element&&Q&&Q.appendChild(_e._element)}else{const _e=new h({svg:this._element,type:((ue=c.cornersDotOptions)===null||ue===void 0?void 0:ue.type)||c.dotsOptions.type,window:this._window});for(let be=0;be<L.length;be++)for(let te=0;te<L[be].length;te++)!((ye=L[be])===null||ye===void 0)&&ye[te]&&(_e.draw(he+te*E,fe+be*E,E,(xe,Ee)=>{var Oe;return!!(!((Oe=L[be+Ee])===null||Oe===void 0)&&Oe[te+xe])}),_e._element&&Q&&Q.appendChild(_e._element))}})}loadImage(){return new Promise((f,c)=>{var m;const v=this._options;if(!v.image)return c("Image is not defined");if(!((m=v.nodeCanvas)===null||m===void 0)&&m.loadImage)v.nodeCanvas.loadImage(v.image).then(p=>{var E,T;if(this._image=p,this._options.imageOptions.saveAsBlob){const M=(E=v.nodeCanvas)===null||E===void 0?void 0:E.createCanvas(this._image.width,this._image.height);(T=M==null?void 0:M.getContext("2d"))===null||T===void 0||T.drawImage(p,0,0),this._imageUri=M==null?void 0:M.toDataURL()}f()}).catch(c);else{const p=new this._window.Image;typeof v.imageOptions.crossOrigin=="string"&&(p.crossOrigin=v.imageOptions.crossOrigin),this._image=p,p.onload=async()=>{this._options.imageOptions.saveAsBlob&&(this._imageUri=await async function(E,T){return new Promise(M=>{const z=new T.XMLHttpRequest;z.onload=function(){const I=new T.FileReader;I.onloadend=function(){M(I.result)},I.readAsDataURL(z.response)},z.open("GET",E),z.responseType="blob",z.send()})}(v.image||"",this._window)),f()},p.src=v.image}})}async drawImage({width:f,height:c,count:m,dotSize:v}){const p=this._options,E=this._roundSize((p.width-m*v)/2),T=this._roundSize((p.height-m*v)/2),M=E+this._roundSize(p.imageOptions.margin+(m*v-f)/2),z=T+this._roundSize(p.imageOptions.margin+(m*v-c)/2),I=f-2*p.imageOptions.margin,B=c-2*p.imageOptions.margin,N=this._window.document.createElementNS("http://www.w3.org/2000/svg","image");N.setAttribute("href",this._imageUri||""),N.setAttribute("x",String(M)),N.setAttribute("y",String(z)),N.setAttribute("width",`${I}px`),N.setAttribute("height",`${B}px`),this._element.appendChild(N)}_createColor({options:f,color:c,additionalRotation:m,x:v,y:p,height:E,width:T,name:M}){const z=T>E?T:E,I=this._window.document.createElementNS("http://www.w3.org/2000/svg","rect");if(I.setAttribute("x",String(v)),I.setAttribute("y",String(p)),I.setAttribute("height",String(E)),I.setAttribute("width",String(T)),I.setAttribute("clip-path",`url('#clip-path-${M}')`),f){let B;if(f.type==="radial")B=this._window.document.createElementNS("http://www.w3.org/2000/svg","radialGradient"),B.setAttribute("id",M),B.setAttribute("gradientUnits","userSpaceOnUse"),B.setAttribute("fx",String(v+T/2)),B.setAttribute("fy",String(p+E/2)),B.setAttribute("cx",String(v+T/2)),B.setAttribute("cy",String(p+E/2)),B.setAttribute("r",String(z/2));else{const N=((f.rotation||0)+m)%(2*Math.PI),D=(N+2*Math.PI)%(2*Math.PI);let A=v+T/2,R=p+E/2,g=v+T/2,$=p+E/2;D>=0&&D<=.25*Math.PI||D>1.75*Math.PI&&D<=2*Math.PI?(A-=T/2,R-=E/2*Math.tan(N),g+=T/2,$+=E/2*Math.tan(N)):D>.25*Math.PI&&D<=.75*Math.PI?(R-=E/2,A-=T/2/Math.tan(N),$+=E/2,g+=T/2/Math.tan(N)):D>.75*Math.PI&&D<=1.25*Math.PI?(A+=T/2,R+=E/2*Math.tan(N),g-=T/2,$-=E/2*Math.tan(N)):D>1.25*Math.PI&&D<=1.75*Math.PI&&(R+=E/2,A+=T/2/Math.tan(N),$-=E/2,g-=T/2/Math.tan(N)),B=this._window.document.createElementNS("http://www.w3.org/2000/svg","linearGradient"),B.setAttribute("id",M),B.setAttribute("gradientUnits","userSpaceOnUse"),B.setAttribute("x1",String(Math.round(A))),B.setAttribute("y1",String(Math.round(R))),B.setAttribute("x2",String(Math.round(g))),B.setAttribute("y2",String(Math.round($)))}f.colorStops.forEach(({offset:N,color:D})=>{const A=this._window.document.createElementNS("http://www.w3.org/2000/svg","stop");A.setAttribute("offset",100*N+"%"),A.setAttribute("stop-color",D),B.appendChild(A)}),I.setAttribute("fill",`url('#${M}')`),this._defs.appendChild(B)}else c&&I.setAttribute("fill",c);this._element.appendChild(I)}}Y.instanceCount=0;const ne=Y,Z="canvas",K={};for(let b=0;b<=40;b++)K[b]=b;const X={type:Z,shape:"square",width:300,height:300,data:"",margin:0,qrOptions:{typeNumber:K[0],mode:void 0,errorCorrectionLevel:"Q"},imageOptions:{saveAsBlob:!0,hideBackgroundDots:!0,imageSize:.4,crossOrigin:void 0,margin:0},dotsOptions:{type:"square",color:"#000",roundSize:!0},backgroundOptions:{round:0,color:"#fff"}};function ce(b){const f=Object.assign({},b);if(!f.colorStops||!f.colorStops.length)throw"Field 'colorStops' is required in gradient";return f.rotation?f.rotation=Number(f.rotation):f.rotation=0,f.colorStops=f.colorStops.map(c=>Object.assign(Object.assign({},c),{offset:Number(c.offset)})),f}function se(b){const f=Object.assign({},b);return f.width=Number(f.width),f.height=Number(f.height),f.margin=Number(f.margin),f.imageOptions=Object.assign(Object.assign({},f.imageOptions),{hideBackgroundDots:Boolean(f.imageOptions.hideBackgroundDots),imageSize:Number(f.imageOptions.imageSize),margin:Number(f.imageOptions.margin)}),f.margin>Math.min(f.width,f.height)&&(f.margin=Math.min(f.width,f.height)),f.dotsOptions=Object.assign({},f.dotsOptions),f.dotsOptions.gradient&&(f.dotsOptions.gradient=ce(f.dotsOptions.gradient)),f.cornersSquareOptions&&(f.cornersSquareOptions=Object.assign({},f.cornersSquareOptions),f.cornersSquareOptions.gradient&&(f.cornersSquareOptions.gradient=ce(f.cornersSquareOptions.gradient))),f.cornersDotOptions&&(f.cornersDotOptions=Object.assign({},f.cornersDotOptions),f.cornersDotOptions.gradient&&(f.cornersDotOptions.gradient=ce(f.cornersDotOptions.gradient))),f.backgroundOptions&&(f.backgroundOptions=Object.assign({},f.backgroundOptions),f.backgroundOptions.gradient&&(f.backgroundOptions.gradient=ce(f.backgroundOptions.gradient))),f}var re=i(873),$e=i.n(re);function Ne(b){if(!b)throw new Error("Extension must be defined");b[0]==="."&&(b=b.substring(1));const f={bmp:"image/bmp",gif:"image/gif",ico:"image/vnd.microsoft.icon",jpeg:"image/jpeg",jpg:"image/jpeg",png:"image/png",svg:"image/svg+xml",tif:"image/tiff",tiff:"image/tiff",webp:"image/webp",pdf:"application/pdf"}[b.toLowerCase()];if(!f)throw new Error(`Extension "${b}" is not supported`);return f}class k{constructor(f){f!=null&&f.jsdom?this._window=new f.jsdom("",{resources:"usable"}).window:this._window=window,this._options=f?se(s(X,f)):X,this.update()}static _clearContainer(f){f&&(f.innerHTML="")}_setupSvg(){if(!this._qr)return;const f=new ne(this._options,this._window);this._svg=f.getElement(),this._svgDrawingPromise=f.drawQR(this._qr).then(()=>{var c;this._svg&&((c=this._extension)===null||c===void 0||c.call(this,f.getElement(),this._options))})}_setupCanvas(){var f,c;this._qr&&(!((f=this._options.nodeCanvas)===null||f===void 0)&&f.createCanvas?(this._nodeCanvas=this._options.nodeCanvas.createCanvas(this._options.width,this._options.height),this._nodeCanvas.width=this._options.width,this._nodeCanvas.height=this._options.height):(this._domCanvas=document.createElement("canvas"),this._domCanvas.width=this._options.width,this._domCanvas.height=this._options.height),this._setupSvg(),this._canvasDrawingPromise=(c=this._svgDrawingPromise)===null||c===void 0?void 0:c.then(()=>{var m;if(!this._svg)return;const v=this._svg,p=new this._window.XMLSerializer().serializeToString(v),E=btoa(p),T=`data:${Ne("svg")};base64,${E}`;if(!((m=this._options.nodeCanvas)===null||m===void 0)&&m.loadImage)return this._options.nodeCanvas.loadImage(T).then(M=>{var z,I;M.width=this._options.width,M.height=this._options.height,(I=(z=this._nodeCanvas)===null||z===void 0?void 0:z.getContext("2d"))===null||I===void 0||I.drawImage(M,0,0)});{const M=new this._window.Image;return new Promise(z=>{M.onload=()=>{var I,B;(B=(I=this._domCanvas)===null||I===void 0?void 0:I.getContext("2d"))===null||B===void 0||B.drawImage(M,0,0),z()},M.src=T})}}))}async _getElement(f="png"){if(!this._qr)throw"QR code is empty";return f.toLowerCase()==="svg"?(this._svg&&this._svgDrawingPromise||this._setupSvg(),await this._svgDrawingPromise,this._svg):((this._domCanvas||this._nodeCanvas)&&this._canvasDrawingPromise||this._setupCanvas(),await this._canvasDrawingPromise,this._domCanvas||this._nodeCanvas)}update(f){k._clearContainer(this._container),this._options=f?se(s(this._options,f)):this._options,this._options.data&&(this._qr=$e()(this._options.qrOptions.typeNumber,this._options.qrOptions.errorCorrectionLevel),this._qr.addData(this._options.data,this._options.qrOptions.mode||function(c){switch(!0){case/^[0-9]*$/.test(c):return"Numeric";case/^[0-9A-Z $%*+\-./:]*$/.test(c):return"Alphanumeric";default:return"Byte"}}(this._options.data)),this._qr.make(),this._options.type===Z?this._setupCanvas():this._setupSvg(),this.append(this._container))}append(f){if(f){if(typeof f.appendChild!="function")throw"Container should be a single DOM node";this._options.type===Z?this._domCanvas&&f.appendChild(this._domCanvas):this._svg&&f.appendChild(this._svg),this._container=f}}applyExtension(f){if(!f)throw"Extension function should be defined.";this._extension=f,this.update()}deleteExtension(){this._extension=void 0,this.update()}async getRawData(f="png"){if(!this._qr)throw"QR code is empty";const c=await this._getElement(f),m=Ne(f);if(!c)return null;if(f.toLowerCase()==="svg"){const v=`<?xml version="1.0" standalone="no"?>\r
${new this._window.XMLSerializer().serializeToString(c)}`;return typeof Blob>"u"||this._options.jsdom?Buffer.from(v):new Blob([v],{type:m})}return new Promise(v=>{const p=c;if("toBuffer"in p)if(m==="image/png")v(p.toBuffer(m));else if(m==="image/jpeg")v(p.toBuffer(m));else{if(m!=="application/pdf")throw Error("Unsupported extension");v(p.toBuffer(m))}else"toBlob"in p&&p.toBlob(v,m,1)})}async download(f){if(!this._qr)throw"QR code is empty";if(typeof Blob>"u")throw"Cannot download in Node.js, call getRawData instead.";let c="png",m="qr";typeof f=="string"?(c=f,console.warn("Extension is deprecated as argument for 'download' method, please pass object { name: '...', extension: '...' } as argument")):typeof f=="object"&&f!==null&&(f.name&&(m=f.name),f.extension&&(c=f.extension));const v=await this._getElement(c);if(v)if(c.toLowerCase()==="svg"){let p=new XMLSerializer().serializeToString(v);p=`<?xml version="1.0" standalone="no"?>\r
`+p,u(`data:${Ne(c)};charset=utf-8,${encodeURIComponent(p)}`,`${m}.svg`)}else u(v.toDataURL(Ne(c)),`${m}.${c}`)}}const O=k})(),a.default})())})(oo);const Lp=yu(oo.exports),Up=l.defineComponent({name:"QrCodeBox",data(){return{qrcode:null}},props:{data:String,image:String,width:Number,height:Number,circleType:String,circleColor:String},methods:{},computed:{},components:{},watch:{$props:{handler(){const t={dotsOptions:{}};Object.keys(this.$props).forEach(e=>{this.$props[e]!=null&&this.$props[e]!==""&&(t[e]=this.$props[e])}),delete t.formCreateInject,t.circleType&&(t.dotsOptions.type=t.circleType),t.circleColor&&(t.dotsOptions.color=t.circleColor),delete t.circleColor,delete t.circleType,this.$nextTick(()=>{this.qrcode?this.qrcode.update(t):(this.qrcode=l.markRaw(new Lp(t)),this.qrcode.append(this.$refs.qr))})},deep:!0,immediate:!0}}}),zp={class:"_fc-qrcode",ref:"qr"};function qp(t,e,n,r,i,a){return l.openBlock(),l.createElementBlock("div",zp,null,512)}const Gp=we(Up,[["render",qp]]),vm="",ym="",Hp=Object.assign||function(t){for(let e,n=1;n<arguments.length;n++)for(let r in e=arguments[n],e)Object.prototype.hasOwnProperty.call(e,r)&&dt(t,r,e[r]);return t};function Wp(){return Hp.apply(this,arguments)}const Xp=(t,e)=>Sr.ElMessage({message:t,type:e||"info",customClass:"_fc-message-tip"});function Yp(t){const e=document.createElement("textarea");e.style.position="fixed",e.style.top=0,e.style.left="-9999px",e.value=t,document.body.appendChild(e),e.focus(),e.select();try{document.execCommand("copy")}catch{console.log("Oops, unable to copy")}Xp("\u5DF2\u590D\u5236!","success"),document.body.removeChild(e)}const Jp=(t,e)=>{let n=[t];const r=e.split(".");let i=1;for(;r[i];){let a=[];n.forEach(o=>{Object.values(o.ctxs).forEach(s=>{if(s.rule._fc_id===r[i-1]){const u=o.subForm[s.id];a.push(...Array.isArray(u)?u:[u])}})}),n=a.map(o=>o.rule[0].__fc__.$handle),i++}return[n,r[i-1]]},cn=(t,e,n)=>{const r=t.vm.setupState.top.setupState.fc.$handle;let i=[t.$handle];t.$handle!==r&&i.push(r);let a=e;e.indexOf(".")>-1&&([i,a]=Jp(r,e)),i.forEach(o=>{Object.values(o.ctxs).forEach(s=>{s.rule._fc_id===a&&n(s.rule,o.api)})})},ao={openModel(t,e){e.api.open(t.model)},closeModel(t,e){e.api.close(t.model)},hidden(t,e,n){const r=typeof t.id=="string"?[t.id]:t.id||[];let i=!!t.status;t.compute&&(i=!!n.$handle.compute(e.self.__fc__,t.formula)),r.forEach(a=>{cn(n,a,o=>{o.hidden=i})})},disabled(t,e,n){const r=typeof t.id=="string"?[t.id]:t.id||[];let i=!!t.status;t.compute&&(i=!!n.$handle.compute(e.self.__fc__,t.formula)),r.forEach(a=>{cn(n,a,o=>{o.props||(o.props={}),o.props.disabled=i})})},resetFields(t,e){e.api.top.resetFields()},clearFields(t,e){e.api.top.coverValue({})},validate(t,e){return e.api.top.validate()},validateFields(t,e,n){const r=typeof t.id=="string"?[t.id]:t.id||[],i=[];return r.forEach(a=>{cn(n,a,(o,s)=>{o.field&&i.push(s.validateField(o.field))})}),i.length?Promise.all(i):void 0},submit(t,e){return e.api.top.submit()},setValue(t,e,n){(t.formData||[]).forEach(r=>{let i=r.value;r.compute&&(i=n.$handle.compute(e.self.__fc__,r.formula)),cn(n,r.id,a=>{a.value=i})})},fetch(t,e){const{append:n,response:r,fetch:i}=t;return n&&(i.data={...e.api.formData(),...i.data||{}}),new Promise((a,o)=>{e.api.fetch(t.fetch).then(s=>{r&&e.api.setData(r,s),a(s)}).catch(s=>{o(s)})})},copy(t,e,n){let r=t.content||"";t.compute&&(r=n.$handle.compute(e.self.__fc__,t.formula)),Yp(r)},callback(t,e){return t.callback&&t.callback(e)},message(t){Sr.ElMessage(t||{})}};function Kp(t,e){return t.reduce((n,r)=>n.then(()=>r(e)),Promise.resolve())}const Qp=function(t){return{name:"behavior",load(e,n){const r=e.getValue(),i={},a={};r&&Object.keys(r).forEach(o=>{if(Array.isArray(r[o])){const s=[];if(r[o].forEach(u=>{const{method:d,config:h,expression:_,stopPropagation:y,ignoreError:x}=u;s.push(S=>new Promise(C=>{if(_&&t.$handle.compute(n.__fc__,_)===!1){C();return}const F=(...U)=>{(!y||t.$handle.compute(n.__fc__,y)!==!0)&&C(...U)};let q;try{q=ao[d](h||{},S,t)}catch(U){console.error(U),x!==!1&&F();return}q&&q.then?q.then(F).catch(()=>{x!==!1&&F()}):F(q)}))}),s.length){const u=t.$handle.inject(n,function(d){Kp(s,d)},n.inject||t.$handle.options.injectEvent);o.indexOf("hook_")>-1?a[o.replace("hook_","")]=u:i[o]=u}}}),e.getProp().on=i,e.getProp().hook=a}}};function Zp(t){t.__proto__.setBehavior=e=>{Wp(ao,e)},t.extendApi(e=>({open(n,...r){(e.el(n)||e.top.el(n)).open(...r)},close(n){n?(e.el(n)||e.top.el(n)).close():(e.top.bus.$emit("fc.closeDialog"),e!==e.top&&e.bus.$emit("fc.closeDialog"))}}))}const $r={name:"easySlots",load(t){const e=t.getValue(),n={};e&&Object.keys(e).forEach(r=>{n[r]=e[r].type==="icon"?{type:"i",class:"fc-icon iconfont "+e[r].value}:{type:"div",children:[""+(e[r].value||"")]}}),t.getProp().renderSlots=n}};$r.watch=$r.load;const so={date:"YYYY-MM-DD",month:"YYYY-MM",datetime:"YYYY-MM-DD HH:mm:ss",timerange:"HH:mm:ss",daterange:"YYYY-MM-DD",monthrange:"YYYY-MM",datetimerange:"YYYY-MM-DD HH:mm:ss",year:"YYYY"};function em(t){return t/20+"em"}function tm(t){var e;!((e=t.wrap)!=null&&e.class)||(t.wrap.class==="fc-wrap-top"?(t.wrap.labelAlign="top",delete t.wrap.class):t.wrap.class==="fc-wrap-left"?(t.wrap.labelAlign="left",delete t.wrap.class):t.wrap.class==="fc-wrap-right"&&(t.wrap.labelAlign="right",delete t.wrap.class))}const uo=function(t,e,n,r){const i=r||{};return e.forEach(a=>{t.indexOf(a[n||"id"])>-1&&t.splice(t.indexOf(a[n||"id"]),1,a[i.label||"label"]||a.text),Te.trueArray(a[i.children||"children"])&&uo(t,a[i.children||"children"],n,r)}),t},nm=function(t,e){return e.forEach(n=>{t.indexOf(n.value)>-1&&(t[t.indexOf(n.value)]=n.label||n.text)}),t};function Or(t){if(t){if(!Array.isArray(t))return[t]}else return[];return t}function lo(t){Object.keys(St).forEach(e=>{t.setFormula(e,St[e])}),Zp(t),t.register("behavior",Qp),t.register("easySlots",$r),t.setDriver("elm",{defaultPreview(e,n){let r=e.rule.value;const i=e.$render.vNode.h,a=e.type,o=e.$handle.subForm[e.id],s=e.prop.readMode;if(s===!1||s==="custom"||!e.input||e.rule.subForm||(Array.isArray(o)?o.length:o)||["fcGroup","fcSubForm","tableForm","stepForm","nestedTableForm","infiniteTableForm","upload"].indexOf(e.trueType)>-1)return e.parser.render(n,e);if(["radio","select","checkbox"].indexOf(a)>-1)r=nm([...Or(r)],e.prop.props.options||e.prop.props.formCreateInject.options||[]).join(", ");else if(["timePicker","datePicker","slider"].indexOf(a)>-1)r=Array.isArray(r)?r.join(" - "):r;else if(a==="cascader")r=[...Or(r)],Array.isArray(r[0])||(r=[r]),r=r.map(u=>uo(u,e.prop.props.options||e.prop.props.formCreateInject.options||[],"value").join("/")).join(", ");else{if(a==="fcEditor"||s==="html")return i("div",{innerHTML:r});if(a==="uploader"||s==="image")return r=Or(r),i("div",{class:"_fc-upload"},r.map(function(u){return i("div",{class:"_fc-upload-preview"},[i("van-image",{src:u,fit:"cover"})])}));if(a==="signaturePad"&&r)return i("van-image",{src:r,fit:"cover",style:{height:"90px"}});(typeof r=="boolean"||a==="switch")&&(r=r?"\u662F":"\u5426")}return i("span",{class:"_fc-read-view"},[""+(r==null?"":r)])},updateWrap(e){var r,i;let n=(i=(r=e.prop)==null?void 0:r.wrap)==null?void 0:i.style;e.prop.col&&e.prop.col.span&&(e.prop.col.span=24),n&&(n=Array.isArray(n)?n:[n],n.forEach(a=>{delete a.marginBottom})),tm(e.prop)},updateOptions(e){var n,r,i;((n=e.form)==null?void 0:n.labelWidth)&&e.form.labelWidth.indexOf("px")>-1&&(e.form.labelWidth=em(parseInt(e.form.labelWidth))),(r=e.form)!=null&&r.labelPosition&&(e.form.labelAlign=e.form.labelPosition,delete e.form.labelPosition),(i=e.form)!=null&&i.hideRequiredAsterisk&&(e.form.colon=e.form.hideRequiredAsterisk,delete e.form.hideRequiredAsterisk)},parsers:{inputNumber:{mergeProp(e){e.prop.component="vanStepper";const n=e.prop.props;n.decimalLength=n.precision,n.integer=n.precision===0,delete n.precision}},radio:{mergeProp(e){e.prop.options&&(e.prop.props.options=e.prop.options)}},checkbox:{mergeProp(e){e.prop.options&&(e.prop.props.options=e.prop.options)}},select:{mergeProp(e){e.prop.props.multiple===!0?e.prop.component=vs:(e.prop.options&&(e.prop.props.options=e.prop.options),e.prop.props.options=(e.prop.props.options||[]).map(n=>({text:n.label,value:n.value})))}},rate:{mergeProp(e){e.prop.props.count=e.prop.props.max,delete e.prop.props.max}},timePicker:{mergeProp(e){e.prop.component="elTimePicker";const n=e.prop.props;n.valueFormat||(n.valueFormat="HH:mm:ss")}},datePicker:{mergeProp(e){e.prop.component="elDatePicker";const n=e.prop.props;n.valueFormat||(n.valueFormat=so[n.type]||so.date)}},colorPicker:{mergeProp(e){e.prop.component="elColorPicker"}},cascader:{mergeProp(e){e.prop.component="elCascader"}},elDivider:{mergeProp(e){e.prop.component="vanDivider"}},upload:{mergeProp(e){e.prop.component=Rs}},tree:{mergeProp(e){e.prop.component=bs}},row:{mergeProp(e){e.prop.component="vanRow"}},col:{mergeProp(e){e.prop.component="vanCol",e.prop.props.xs&&(e.prop.props={...e.prop.props,...e.prop.props.xs})}},fcDialog:{mergeProp(e){e.prop.component="FcPopup"}},fcDrawer:{mergeProp(e){e.prop.component="FcPopup"}}}})}lo(ge),ge.component("FcSlot",du),ge.component("FcJson",hu),ge.component("StepForm",Ts),ge.component("FcPopup",Ls),ge.component("FcTable",Xs),ge.component("FcCell",su),ge.component("FcInlineForm",vu),ge.component("TableForm",Ks),ge.component("NestedTableForm",ru),ge.component("InfiniteTableForm",eu),ge.component("DataTable",qs),ge.component("FcValue",fu),ge.component("AudioBox",Wu),ge.component("VideoBox",Np),ge.component("BarCodeBox",Fp),ge.component("IframeBox",Ku),ge.component("QrCodeBox",Gp),ge.component("SignaturePad",ku),ge.component("FcEcharts",Ou),ge.component("FcTitle",Uu),ge.component("FcId",Bu),ge.loadjs=Xe,Ue.default=ge,Ue.useAdvanced=lo,Object.defineProperties(Ue,{__esModule:{value:!0},[Symbol.toStringTag]:{value:"Module"}})});
