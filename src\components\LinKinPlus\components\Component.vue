<script lang="jsx">
// 类似<component is="el-input" />
import renderComponent from '../render/renderComponent'
import { defineComponent } from 'vue'

export default defineComponent({
  name: 'lk-component',
  props: {
    model: { type: Object, default: () => ({}) }
  },
  setup(props, { attrs, slots }) {
    // renderComponent中会修改slots.default, 这里必须遍历才能保证修改成功
    return () => renderComponent({ ...attrs, slots: { ...slots } }, props.model)
  }
})
</script>
