import request from '@/config/axios'
import { aw } from 'dist/assets/index-88ce5358'

// 运营端文件二维码管理 VO
export interface FileQrcodeVO {
  fileCode: string // 文件编号
  fileName: string // 文件名称
  fileVersion: string // 版本号
  fileStatus: string // 文件状态
}

// 运营端文件二维码管理 API
export const FileQrcodeApi = {
  // 查询运营端文件二维码管理分页
  getFileQrcodePage: async (params: any) => {
    return await request.get({ url: `/system/file-qrcode/page`, params })
  },

  // 查询运营端文件二维码管理详情
  getFileQrcode: async (id: number) => {
    return await request.get({ url: `/system/file-qrcode/get?id=` + id })
  },

  // 新增运营端文件二维码管理
  createFileQrcode: async (data: FileQrcodeVO) => {
    return await request.post({ url: `/system/file-qrcode/create`, data })
  },

  // 修改运营端文件二维码管理
  updateFileQrcode: async (data: FileQrcodeVO) => {
    return await request.put({ url: `/system/file-qrcode/update`, data })
  },

  // 删除运营端文件二维码管理
  deleteFileQrcode: async (id: number) => {
    return await request.delete({ url: `/system/file-qrcode/delete?id=` + id })
  },

  // 导出运营端文件二维码管理 Excel
  exportFileQrcode: async (params) => {
    return await request.download({ url: `/system/file-qrcode/export-excel`, params })
  },
  // 根据二维码ID获取文件二维码内容
  getFileQrcodeInfo: async (id: string) => {
    return await request.get({ url: `/system/file-qrcode/get?id=` + id })
  },
  // 更新所有二维码
  updateAllCode: async () => {
    return await request.post({ url: `/system/file-qrcode/update-all-code` })
  }
}
