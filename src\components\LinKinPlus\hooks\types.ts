//类型
export type ComponentProps<D = Record<any, any>> = {
  prop: keyof D
  label?: string
  componentName?: string
  type?: string
  options?: Option[]
  render?: (val: D) => JSX.Element
  slots?: any
  [key: string]: any
}

export type FormStateType<D> = {
  visible?: boolean
  data?: object
  formProps: {
    columns: ComponentProps<D>[]
    [key: string]: any
  }
  type?: string
  [key: string]: any
}

export type Option =
  | string
  | number
  | {
      label: string | number
      value: string | number
      [key: string]: any
    }
// Get方法返回的类型, 对象键值可能是 data\list\items这三种
export type ResType<D> = {
  data?: D[]
  list?: D[]
  items?: D[]
  total: number
}
