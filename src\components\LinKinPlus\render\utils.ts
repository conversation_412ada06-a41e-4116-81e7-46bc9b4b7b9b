import dayjs from '@/utils/dayjs.js'
import type { Option } from '../hooks/types'
export const isSelect = (item: { type?: string; componentName?: string; options?: Option[] }): Boolean => {
  return (
    Array.isArray(item.options) ||
    ['select', 'date', 'checkbox'].includes(item.type || '') ||
    ['el-select', 'el-date-picker', 'el-checkbox', 'RemoteSelect'].includes(item.componentName || '')
  )
}
export const getPropRules = (c: any, rule = []) => {
  const rules = c.rules ?? []
  const message = `请${isSelect(c) ? '选择' : '输入'}${c.label}`
  if (Array.isArray(rule)) rules.push(...rule)
  else rules.push(rule)
  if (c.required) rules.push({ required: true, message })
  return rules
}

export const getFormatValue = (item: any, data: any) => {
  const value = String(item.prop)
    .split('.')
    .reduce((t, c) => t?.[c], data)
  // value为0时应该继续
  if (value == void 0 || value === '') return ''

  if (item.type == 'select' || item.options?.length || Array.isArray(value)) {
    return getSelectLabel(item.options, value)
  }

  if (item.type?.includes('date')) {
    return dayjs(value).format(
      item['value-format'] ?? item.type.includes('time') ? 'YYYY-MM-DD HH:mm:ss' : 'YYYY-MM-DD'
    )
  }

  if (item.type == 'duration') {
    return dayjs.duration(value, 'seconds').format('HH:mm:ss')
  }
  
  //增加字典转义
  if (item.filterOptions) {
    for (const v of item.filterOptions) {
      if (v.value === value) {
        return v.label
      }
    }
  }

  return value
}

export const getSelectLabel = (options = [], value = '') => {
  if (value == void 0 || value === '') return ''
  if (!options || options.length === 0) return String(value)

  const map = options.reduce((t: any = {}, o: any = {}) => {
    t[o.value] = o.label
    return t
  }, {})

  let tempArr = Array.isArray(value) && value
  const tempStr = value.toString()
  if (tempStr.startsWith('[') && tempStr.endsWith(']')) {
    tempArr = eval('(' + value + ')')
  }

  return Array.isArray(tempArr) ? tempArr.map(t => map[t] ?? t).join('，') : map[value] || value
}
