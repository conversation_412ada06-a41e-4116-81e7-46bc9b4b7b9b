import type { InjectionKey } from 'vue'

export const formCreateInjectionData = Symbol() as InjectionKey<{
  currentFormId: globalThis.Ref<string, string>
  msId: globalThis.Ref<string, string>
  templateTreeData: globalThis.Ref<any[], any[]>
  formType: globalThis.Ref<string, string>
  msFormType: globalThis.Ref<string, string>
  formVisibleSettingList: globalThis.Ref<any[], any[]>
  msSccsId: globalThis.Ref<string, string>
  replyFormId: globalThis.Ref<string, string>
  designerReadonly: globalThis.Ref<boolean>
}>
