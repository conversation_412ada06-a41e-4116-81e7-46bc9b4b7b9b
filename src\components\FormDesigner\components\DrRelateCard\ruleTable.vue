<template>
  <div class="dr-rule-table">
    <VueDraggable
      :model-value="modelValue.rules"
      item-key="value"
      :force-fallback="true"
      :animation="200"
      handle=".drag-icon"
      @change="onDraggableChange"
    >
      <template #item="{ element, index }">
        <div class="drag-item">
          <p class="label">{{ element.title }}</p>
          <el-icon class="cursor-pointer" :size="18" @click="deleteItem(index)">
            <Delete class="text-red-5" />
          </el-icon>
          <el-icon class="drag-icon" :size="18">
            <Rank />
          </el-icon>
        </div>
      </template>
    </VueDraggable>
    <div class="bottom-tools">
      <el-button type="primary" size="small" style="width: 100%" @click="openSelectRulesDialog">
        批量添加
      </el-button>
    </div>

    <el-dialog
      v-model="dialogVisible"
      title="关联引用"
      width="500"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :before-close="handleClose"
    >
      <el-alert
        description="请选择表单，及表单要关联的字段（可多选）"
        type="info"
        :closable="false"
        style="margin-bottom: 10px"
      />
      <el-select
        v-model="selectedMainKey"
        placeholder="请选择模板"
        size="default"
        style="width: 100%"
      >
        <el-option
          v-for="item in flatTemplateList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
      <el-tree-select
        :model-value="selectedFieldKeys"
        :data="renderSelectTreeData"
        node-key="field"
        :props="{
          label: 'title',
          children: 'childrenList',
          disabled: (data) => {
            return data.type === 'parent'
          }
        }"
        multiple
        :render-after-expand="false"
        show-checkbox
        check-strictly
        check-on-click-node
        size="default"
        style="width: 100%; margin-top: 10px"
        filterable
        @check="onFieldCheck"
        @remove-tag="removeTag"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button size="default" @click="dialogVisible = false">取消</el-button>
          <el-button size="default" type="primary" @click="onConfirm"> 确定 </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { arrayMoveImmutable } from 'array-move'
import VueDraggable from 'vuedraggable'
import { Rank, Delete } from '@element-plus/icons-vue'
import { cloneDeep } from 'lodash-es'
import { formCreateInjectionData } from '../../inject'
import e from '@/plugins/fcDesignerPro/locale/en.es'
import { select } from '@/components/LinKinPlus/render/static'

type PropsType = {
  modelValue?: {
    formId: string
    rules: any[]
  }
  formCreateInject: any
}

const props = withDefaults(defineProps<PropsType>(), {
  modelValue: () => ({
    formId: '',
    rules: []
  })
})

const emit = defineEmits(['update:modelValue', 'change'])

const injectionData = inject(formCreateInjectionData)

const selectedMainKey = ref<string>()
const selectedFormType = ref<string>('')
const selectedFieldKeys = ref<string[]>([])
const selectedMain = ref<any>({})

const onFieldCheck = (data: any) => {
  if (!selectedFieldKeys.value.includes(data.field)) {
    selectedFieldKeys.value.push(data.field)
  } else {
    const index = selectedFieldKeys.value.findIndex((key) => key === data.field)
    selectedFieldKeys.value.splice(index, 1)
  }
}

const removeTag = (tabValue) => {
  const index = selectedFieldKeys.value.findIndex((key) => key === tabValue)
  selectedFieldKeys.value.splice(index, 1)
}

const flatTemplateCloneDataList = computed(() => {
  const list: any = []
  // console.log('injectionData', injectionData?.formType.value, injectionData?.msFormType.value)
  // console.log('injectionData?.templateTreeData.value', injectionData?.templateTreeData.value)

  // 当前编辑主表单，数据源来自所有里程碑批复表单、所有里程碑工单及工单批复
  if (injectionData?.formType.value === 'MAIN_FORM') {
    injectionData?.templateTreeData.value.forEach((template) => {
      if (template.formType === 'MAIN_FORM') return
      if (template.formType === 'MILESTONE') {
        // 是否开启批复
        if (template.needReply && template.replyType) {
          // 是否需要里程碑批复
          if (
            template.replyType === 'REPLY_TO_ALL' ||
            template.replyType === 'REPLY_TO_MILESTONE'
          ) {
            list.push({
              formType: template.replyForm.formType,
              value: template.replyForm.id,
              label: `里程碑批复-${template.replyForm.name}`,
              id: template.replyForm.id,
              templateId: template.replyForm.templateId,
              children: (template.replyForm.widgetJsonList || []).map((widget) => {
                if (widget.type === 'DrTableForm') {
                  const tableWidget = cloneDeep(widget)
                  // delete tableWidget['children']
                  return tableWidget
                } else {
                  return widget
                }
              })
            })
          }
        }

        // 工单
        const formList = template.formList || []

        // 是否有工单批复
        if (template.replyType === 'REPLY_TO_ALL' || template.replyType === 'REPLY_TO_WORK_ORDER') {
          formList.unshift({
            ...template.workOrderReplyForm,
            name: `工单批复-${template.workOrderReplyForm.name}`
          })
        }
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: (formList || []).map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
    })
  }

  // 当前编辑的是里程碑批复表单，数据源来自所有主表单、所有里程碑批复表单（排除当前里程碑批复表单）、所有里程碑（含工单批复，排除当前里程碑）
  if (injectionData?.msFormType.value === 'REPLY_TO_MILESTONE') {
    injectionData?.templateTreeData.value.forEach((template) => {
      if (template.formType === 'MAIN_FORM') {
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: (template.widgetJsonList || []).map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
      if (template.formType === 'MILESTONE') {
        // 排除当前里程碑
        if (template.id === injectionData?.currentFormId.value) return
        // 是否开启批复
        if (template.needReply && template.replyType) {
          // 是否需要里程碑批复
          if (
            template.replyType === 'REPLY_TO_ALL' ||
            template.replyType === 'REPLY_TO_MILESTONE'
          ) {
            list.push({
              formType: template.replyForm.formType,
              value: template.replyForm.id,
              label: `里程碑批复-${template.replyForm.name}`,
              id: template.replyForm.id,
              templateId: template.replyForm.templateId,
              children: (template.replyForm.widgetJsonList || []).map((widget) => {
                if (widget.type === 'DrTableForm') {
                  const tableWidget = cloneDeep(widget)
                  // delete tableWidget['children']
                  return tableWidget
                } else {
                  return widget
                }
              })
            })
          }
        }

        // 工单
        const formList = template.formList || []

        // 是否有工单批复
        if (template.replyType === 'REPLY_TO_ALL' || template.replyType === 'REPLY_TO_WORK_ORDER') {
          formList.unshift({
            ...template.workOrderReplyForm,
            name: `工单批复-${template.workOrderReplyForm.name}`
          })
        }
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: (formList || []).map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
    })
  }

  // 当前编辑的是工单表单，数据源来自所有主表单、所有里程碑批复表单、所有里程碑（含工单批复，排除当前里程碑工单表单但不排除工单批复）
  if (injectionData?.formType.value === 'FORM') {
    injectionData?.templateTreeData.value.forEach((template) => {
      if (template.formType === 'MAIN_FORM') {
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: (template.widgetJsonList || []).map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
      if (template.formType === 'MILESTONE') {
        // 是否开启批复
        if (template.needReply && template.replyType) {
          // 是否需要里程碑批复
          if (
            template.replyType === 'REPLY_TO_ALL' ||
            template.replyType === 'REPLY_TO_MILESTONE'
          ) {
            list.push({
              formType: template.replyForm.formType,
              value: template.replyForm.id,
              label: `里程碑批复-${template.replyForm.name}`,
              id: template.replyForm.id,
              templateId: template.replyForm.templateId,
              children: (template.replyForm.widgetJsonList || []).map((widget) => {
                if (widget.type === 'DrTableForm') {
                  const tableWidget = cloneDeep(widget)
                  // delete tableWidget['children']
                  return tableWidget
                } else {
                  return widget
                }
              })
            })
          }
        }

        // 工单
        const formList =
          template.id === injectionData?.currentFormId.value ? [] : template.formList || []

        // 是否有工单批复
        if (template.replyType === 'REPLY_TO_ALL' || template.replyType === 'REPLY_TO_WORK_ORDER') {
          formList.unshift({
            ...template.workOrderReplyForm,
            name: `工单批复-${template.workOrderReplyForm.name}`
          })
        }
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: formList.map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
    })
  }

  // 当前编辑的是工单批复表单，数据源来自所有主表单、所有里程碑批复表单、所有里程碑（含工单批复，排除当前里程碑工单批复表单但不排除工单表单）
  if (injectionData?.msFormType.value === 'REPLY_TO_WORK_ORDER') {
    injectionData?.templateTreeData.value.forEach((template) => {
      if (template.formType === 'MAIN_FORM') {
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: (template.widgetJsonList || []).map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
      if (template.formType === 'MILESTONE') {
        // 是否开启批复
        if (template.needReply && template.replyType) {
          // 是否需要里程碑批复
          if (
            template.replyType === 'REPLY_TO_ALL' ||
            template.replyType === 'REPLY_TO_MILESTONE'
          ) {
            list.push({
              formType: template.replyForm.formType,
              value: template.replyForm.id,
              label: `里程碑批复-${template.replyForm.name}`,
              id: template.replyForm.id,
              templateId: template.replyForm.templateId,
              children: (template.replyForm.widgetJsonList || []).map((widget) => {
                if (widget.type === 'DrTableForm') {
                  const tableWidget = cloneDeep(widget)
                  // delete tableWidget['children']
                  return tableWidget
                } else {
                  return widget
                }
              })
            })
          }
        }

        // 工单
        const formList = template.formList || []

        // 是否有工单批复
        if (
          template.id === injectionData?.currentFormId.value &&
          (template.replyType === 'REPLY_TO_ALL' || template.replyType === 'REPLY_TO_WORK_ORDER')
        ) {
          formList.unshift({
            ...template.workOrderReplyForm,
            name: `工单批复-${template.workOrderReplyForm.name}`
          })
        }
        list.push({
          formType: template.formType,
          value: template.id,
          label: template.name,
          id: template.id,
          templateId: template.templateId,
          children: formList.map((widget) => {
            if (widget.type === 'DrTableForm') {
              const tableWidget = cloneDeep(widget)
              // delete tableWidget['children']
              return tableWidget
            } else {
              return widget
            }
          })
        })
      }
    })
  }

  return list
})

const flatTemplateList = computed(() => {
  const formList =
    injectionData?.formType.value === 'MAIN_FORM'
      ? injectionData?.templateTreeData.value.filter((form) => form.formType !== 'MAIN_FORM')
      : injectionData?.templateTreeData.value

  return formList?.map((form) => {
    if (form.formType === 'MAIN_FORM') {
      return {
        formType: form.formType,
        value: form.id,
        label: form.name,
        id: form.id,
        templateId: form.templateId,
        children: form.widgetJsonList
      }
    } else {
      let formListChildren: any[] = []
      if (form.formList.length > 0) {
        formListChildren.push(...form.formList)
      }
      if (form.workOrderReplyForm) {
        formListChildren.push(form.workOrderReplyForm)
      }
      if (form.replyForm) {
        formListChildren.push(form.replyForm)
      }
      return {
        formType: form.formType,
        value: form.id,
        label: form.name,
        id: form.id,
        templateId: form.templateId,
        children: formListChildren
      }
    }
  })
})

watch(
  () => selectedMainKey.value,
  () => {
    selectedFieldKeys.value = []
  }
)

const renderSelectTreeData = computed<any[]>(() => {
  const formType = injectionData?.formType.value
  const msFormType = injectionData?.msFormType.value
  const currentFormId = injectionData?.msId.value

  if (formType === 'MAIN_FORM') {
    //@ts-ignore
    const selectMain: any = flatTemplateList.value.find((item) => item.id === selectedMainKey.value)
    if (!selectMain) return []
    if (selectMain.formType === 'MAIN_FORM') {
      const formList = selectMain.children
      return formList.map((item) => {
        const formTypeName =
          item.formType === 'WORK_ORDER_REPLY_FORM'
            ? `工单批复-${item.name}`
            : item.formType === 'MS_REPLY_FORM'
              ? `里程碑批复-${item.name}`
              : item.name
        return {
          field: item.id,
          type: 'parent',
          title: formTypeName,
          childrenList: getRelateFieldFromTree(item.widgetJsonList)
        }
      })
    }
  } else if (formType === 'FORM') {
    // @ts-ignore
    const selectMain: any = flatTemplateList.value.find((item) => item.id === selectedMainKey.value)
    if (!selectMain) return []
    if (selectMain.formType === 'MAIN_FORM') {
      return getRelateFieldFromTree(selectMain.children)
    } else {
      if (selectMain.id === currentFormId) {
        const formList = selectMain.children.filter((form) => form.formType !== 'FORM')
        return formList.map((item) => {
          const formTypeName =
            item.formType === 'WORK_ORDER_REPLY_FORM'
              ? `工单批复-${item.name}`
              : item.formType === 'MS_REPLY_FORM'
                ? `里程碑批复-${item.name}`
                : item.name
          return {
            field: item.id,
            type: 'parent',
            title: formTypeName,
            childrenList: getRelateFieldFromTree(item.widgetJsonList)
          }
        })
      } else {
        const formList = selectMain.children
        return formList.map((item) => {
          const formTypeName =
            item.formType === 'WORK_ORDER_REPLY_FORM'
              ? `工单批复-${item.name}`
              : item.formType === 'MS_REPLY_FORM'
                ? `里程碑批复-${item.name}`
                : item.name
          return {
            field: item.id,
            type: 'parent',
            title: formTypeName,
            childrenList: getRelateFieldFromTree(item.widgetJsonList)
          }
        })
      }
    }
  } else if (formType === 'MILESTONE') {
    // 里程碑批复，工单批复
    if (msFormType === 'REPLY_TO_MILESTONE') {
      // @ts-ignore
      const selectMain: any = flatTemplateList.value.find(
        (item) => item.id === selectedMainKey.value
      )
      if (selectMain) {
        if (selectMain.formType === 'MAIN_FORM') {
          return getRelateFieldFromTree(selectMain.children)
        } else {
          if (selectMain.id === currentFormId) {
            const formList = selectMain.children.filter((form) => form.formType !== 'MS_REPLY_FORM')
            return formList.map((item) => {
              const formTypeName =
                item.formType === 'WORK_ORDER_REPLY_FORM'
                  ? `工单批复-${item.name}`
                  : item.formType === 'MS_REPLY_FORM'
                    ? `里程碑批复-${item.name}`
                    : item.name
              return {
                field: item.id,
                type: 'parent',
                title: formTypeName,
                childrenList: getRelateFieldFromTree(item.widgetJsonList)
              }
            })
          } else {
            const formList = selectMain.children
            return formList.map((item) => {
              const formTypeName =
                item.formType === 'WORK_ORDER_REPLY_FORM'
                  ? `工单批复-${item.name}`
                  : item.formType === 'MS_REPLY_FORM'
                    ? `里程碑批复-${item.name}`
                    : item.name
              return {
                field: item.id,
                type: 'parent',
                title: formTypeName,
                childrenList: getRelateFieldFromTree(item.widgetJsonList)
              }
            })
          }
        }
      } else {
        return []
      }
    } else if (msFormType === 'REPLY_TO_WORK_ORDER') {
      // @ts-ignore
      const selectMain: any = flatTemplateList.value.find(
        (item) => item.id === selectedMainKey.value
      )
      if (selectMain) {
        if (selectMain.formType === 'MAIN_FORM') {
          return getRelateFieldFromTree(selectMain.children)
        } else {
          if (selectMain.id === currentFormId) {
            const formList = selectMain.children.filter(
              (form) => form.formType !== 'WORK_ORDER_REPLY_FORM'
            )
            return formList.map((item) => {
              const formTypeName =
                item.formType === 'WORK_ORDER_REPLY_FORM'
                  ? `工单批复-${item.name}`
                  : item.formType === 'MS_REPLY_FORM'
                    ? `里程碑批复-${item.name}`
                    : item.name
              return {
                field: item.id,
                type: 'parent',
                title: formTypeName,
                childrenList: getRelateFieldFromTree(item.widgetJsonList)
              }
            })
          } else {
            const formList = selectMain.children
            return formList.map((item) => {
              const formTypeName =
                item.formType === 'WORK_ORDER_REPLY_FORM'
                  ? `工单批复-${item.name}`
                  : item.formType === 'MS_REPLY_FORM'
                    ? `里程碑批复-${item.name}`
                    : item.name
              return {
                field: item.id,
                type: 'parent',
                title: formTypeName,
                childrenList: getRelateFieldFromTree(item.widgetJsonList)
              }
            })
          }
        }
      }
    }
  }
})

const deepCloneRelateFieldData = (widgetJsonList: any[]) => {
  return widgetJsonList.flatMap((widget) => {
    if (widget.type === 'DrCard') {
      const widgetChildList = widget.children.map((widgetChild) => widgetChild.children[0])
      return deepCloneRelateFieldData(widgetChildList)
    } else {
      return widget
    }
  })
}

const getRelateFieldFromTree = (widgetJsonList: any[]) => {
  const relateFieldWidgetJsonList = deepCloneRelateFieldData(widgetJsonList)
  const relateWidgetList = ['DrDivider', 'DrPlaceholder', 'DrRelateCard']

  const relateWidgetJsonList = relateFieldWidgetJsonList.filter((relateWidget) => {
    return !relateWidgetList.includes(relateWidget.type)
  })
  return relateWidgetJsonList
}

const onConfirm = () => {
  const selectedRules = findRulesByFieldKeys(selectedFieldKeys.value)
  // console.log('selectedRules', selectedRules, selectedFormType.value)
  const formType =
    selectedFormType.value === 'MS_REPLY_FORM'
      ? 'msReplyForm'
      : selectedFormType.value === 'MAIN_FORM'
        ? 'mainForm'
        : 'form'
  emit('update:modelValue', {
    formId: selectedMainKey.value,
    formType: formType,
    formLabel: selectedMain.value.label,
    rules: selectedRules
  })
  emit('change', {
    formId: selectedMainKey.value,
    formType: formType,
    formLabel: selectedMain.value.label,
    rules: selectedRules
  })

  dialogVisible.value = false
}

const findRulesByFieldKeys = (fieldKeys: string[]) => {
  //@ts-ignore
  const flatRenderSelectTreeData: any = flatTemplateList.value.find((item) => item.id === selectedMainKey.value)
  const allChildren = flatRenderSelectTreeData.children.reduce((prev, current) => {
    if (current.formType === 'FORM') {
      return [...prev, ...current.widgetJsonList]
    }
    return [...prev, current]
  }, [])
  return fieldKeys.map((key) => {
    return allChildren.find((item) => item.field === key || item.id === key)
  })
}

const onDraggableChange = (e: any) => {
  if (e.moved) {
    const newValue = cloneDeep(props.modelValue)
    const newRules = arrayMoveImmutable(props.modelValue.rules, e.moved.oldIndex, e.moved.newIndex)
    emit('update:modelValue', { ...newValue, rules: newRules })
    emit('change', { ...newValue, rules: newRules })
  }
  if (e.added) {
  }
  if (e.removed) {
  }
}

const deleteItem = (index: number) => {
  const newValue = cloneDeep(props.modelValue)
  newValue.rules.splice(index, 1)
  const formType =
    selectedFormType.value === 'MS_REPLY_FORM'
      ? 'msReplyForm'
      : selectedFormType.value === 'MAIN_FORM'
        ? 'mainForm'
        : 'form'
  emit('update:modelValue', {
    formId: newValue.formId,
    formType: formType,
    rules: newValue.rules
  })
  emit('change', {
    formId: newValue.formId,
    formType: formType,
    rules: newValue.rules
  })
}

const dialogVisible = ref<boolean>(false)

const openSelectRulesDialog = () => {
  selectedMainKey.value = props.modelValue.formId

  nextTick(() => {
    selectedFieldKeys.value = cloneDeep(props.modelValue.rules).map((item) =>
      item ? item.field : ''
    )
  })

  dialogVisible.value = true
}

const handleClose = (done: () => void) => {
  done()
}
</script>

<style scoped lang="scss">
.dr-rule-table {
  width: 100%;

  .drag-item {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 6px;
    user-select: none;
    padding: 5px 0;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }

    .label {
      flex: 1;
      margin: 0;
    }

    .drag-icon {
      cursor: move;
    }

    :deep(.el-radio) {
      margin-right: 0;
    }
  }

  .bottom-tools {
    margin-top: 10px;
  }
}
</style>
