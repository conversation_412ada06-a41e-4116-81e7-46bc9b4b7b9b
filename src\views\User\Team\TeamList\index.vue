<template>
  <!-- 搜索工作栏 -->
  <ContentWrap>
    <Query :columns="queryState.columns" :data="queryParams" :btnList="queryState.btnList" @click="handleQuery" />
  </ContentWrap>

  <ContentWrap>
    <Table ref="tableRef" :columns="tableState.columns" :data="tableState.data" :btnList="tableState.btnList"
      :loading="tableState.loading" @handle-table="handleTable" @SortChange="handleSortChange">
    </Table>
    <Pagination :total="tableState.total" v-model:page="queryParams.pageNo" v-model:limit="queryParams.pageSize"
      @pagination="getList" />
  </ContentWrap>
</template>

<script setup lang="ts">
import { TeamApi, TeamVO } from '@/api/user/team'
import businessConfig from './components/index.config'
import { findJsonIndex } from '@/utils';
const { push } = useRouter() // 路由
const { t } = useI18n() // 国际化

const queryState = reactive({
  columns: businessConfig.searchConfig,
  btnList: businessConfig.searchBtnList
})

const tableState = reactive({
  columns: businessConfig.tableThs,
  btnList: businessConfig.tableBtnList,
  loading: false,
  total: 0,
  data: [] as TeamVO[]
})

defineOptions({ name: 'TeamList' })
class DefaultQuery {
  pageNo = 1
  pageSize = 10
  keyword = ''
  createTime = []
  sortBy = ''
  descending: any = null
}
const queryParams = reactive(new DefaultQuery())
const tableRef = ref()


const handleSortChange = (data: any) => {
  if (data.order == null) {
    queryParams.sortBy = ''
    queryParams.descending = null
  } else {
    queryParams.sortBy = data.prop
    queryParams.descending = data.order == 'descending'
  }
  getList()
}

const handleQuery = (type: string) => {
  switch (type) {
    case 'reset':
      Object.assign(queryParams, new DefaultQuery())
      tableRef.value.clearSort()
      getList()
      break;
    case 'search':
      queryParams.pageNo = 1
      getList()
      break;
    case 'export':
      console.log('export')
      break;
    default:
      break;
  }
}

const handleTable = (type: string, row: any) => {
  switch (type) {
    case 'detail':
      push('/user/teamDetail?id=' + row.id)
      break;
    case 'delete':
      handleDelete(row.id)
      break;
    default:
      break;
  }
}

const message = useMessage() // 消息弹窗
/** 删除按钮操作 */
const handleDelete = async (id: number) => {
  try {
    // 删除的二次确认
    await message.delConfirm()
    // 发起删除
    await TeamApi.deleteTeam(id)
    message.success(t('admin_common_delSuccess'))
    // 刷新列表
    await getList()
  } catch { }
}
/** 查询列表 */
const getList = async () => {
  tableState.loading = true
  try {
    const data = await TeamApi.getTeamPage(queryParams)
    tableState.data = data.list
    tableState.total = data.total
  } finally {
    tableState.loading = false
  }
}

/** 添加/修改操作 */
const formRef = ref()
/** 初始化 **/
onMounted(() => {
  getList()
})

onActivated(() => {
  getList()
})
</script>
