<template>
  <div class="custom-conditions-control-row">
    <!-- 第一个控件类型 -->
    <el-cascader
      ref="CascaderRef"
      v-model="conditionsForm.firstData"
      :options="planSettingSelect"
      :show-all-levels="false"
      :collapse-tags="true"
      :collapse-tags-tooltip="true"
      :props="{
        disabled: 'deleted'
      }"
      @change="handleSelectCascader"
    />
    <!-- 第二个控件：值比较规则控件 -->
    <el-select-v2
      v-model="conditionsForm.operator"
      class="custom-conditions-operator-control"
      filterable
      :options="MathOperatorSelectOptions"
      clearable
      @change="handleSelectOperator"
    >
      <template #label="{ label }">
        {{ t(label) }}
      </template>
      <template #default="{ item }">
        {{ t(item.label) }}
      </template>
    </el-select-v2>
    <!-- 第二个控件：日期时间控件类型 -->
    <div
      v-if="
        firstOpertatorSelectControl &&
        !['AFTER', 'EQ', 'BEFORE', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(
          conditionsForm.operator
        )
      "
    >
      <el-select-v2
        v-model="conditionsForm.rangeDateType"
        class="custom-conditions-operator-control"
        filterable
        :options="getRangeTypeOptions()"
        clearable
        @change="handleSelectDateRangeType"
      >
        <template #label="{ label }">
          {{ t(label) }}
        </template>
        <template #default="{ item }">
          {{ t(item.label) }}
        </template>
      </el-select-v2>
      <el-date-picker
        v-if="conditionsForm.rangeDateType === 'DATE_RANGE'"
        v-model="dateRange"
        range-separator="-"
        :start-placeholder="t('trade_common_beginDate')"
        :end-placeholder="t('trade_common_endDate')"
        style="width: 236px"
        :type="`${widgetDate.type}range`"
        :value-format="widgetDate.valueFormat"
        :format="widgetDate.format"
        :time-format="widgetDate.timeFormat"
        @change="handleDatePickerValue"
      />
      <el-input
        v-if="['LAST', 'NEXT'].includes(conditionsForm.rangeDateType)"
        v-model="conditionsForm.value"
        clearable
        class="conditions-form-input"
        @input="handleEdit"
      >
        <template #append>{{ getTimeDateType() }}</template>
      </el-input>
    </div>
    <div
      v-if="
        (firstOpertatorSelectControl &&
          !['BETWEEN', 'IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(conditionsForm.operator)) ||
        !firstOpertatorSelectControl
      "
    >
      <el-select-v2
        v-if="!['IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(conditionsForm.operator)"
        v-model="conditionsForm.fieldTypeEnum"
        class="custom-conditions-operator-control"
        filterable
        :options="FieldTypeSelectOptions"
        clearable
        @change="handleSelectFieldTypeEnum"
      >
        <template #label="{ label }">
          {{ t(label) }}
        </template>
        <template #default="{ item }">
          {{ t(item.label) }}
        </template>
      </el-select-v2>
      <el-cascader
        v-if="conditionsForm.fieldTypeEnum === 'OTHER_FIELD'"
        ref="TwoCascaderRef"
        v-model="conditionsForm.twoData"
        style="width: 120px; margin-right: 5px"
        :options="planSettingTwoSelect"
        :show-all-levels="false"
        @change="handleSelectTwoCascader"
      />
      <!-- 第四个区域类型控件 -->
      <div
        v-if="!['IS_NULL', 'IS_NOT_NULL', 'CHANGE'].includes(conditionsForm.operator)"
        class="custom-conditions-col"
      >
        <el-select-v2
          v-if="conditionsForm.fieldTypeEnum === 'CUSTOM' && firstOpertatorSelectControl"
          v-model="conditionsForm.customerDateType"
          class="custom-conditions-operator-control"
          filterable
          :options="getDatePickerOptionsByType(DatePickerOptions)"
          clearable
          @change="conditionsForm.value = ''"
        >
          <template #label="{ label }">
            {{ t(label) }}
          </template>
          <template #default="{ item }">
            {{ t(item.label) }}
          </template>
        </el-select-v2>
        <el-date-picker
          v-if="conditionsForm.customerDateType === 'ASSIGN'"
          v-model="conditionsForm.value"
          :type="widgetDate.type"
          :value-format="widgetDate.valueFormat"
          :format="widgetDate.format"
          :time-format="widgetDate.timeFormat"
          style="width: 140px"
          placeholder="请选择日期时间"
        />
        <el-input
          v-if="['BEFORE', 'AFTER'].includes(conditionsForm.customerDateType)"
          v-model="conditionsForm.value"
          clearable
          class="conditions-form-input"
          @input="handleEdit"
        >
          <template #append>{{ getTimeDateType() }}</template>
        </el-input>
        <CustomConditionControl
          v-if="!firstOpertatorSelectControl && conditionsForm.fieldTypeEnum === 'CUSTOM'"
          ref="customConditionControlRef"
          :widget="widgetForm"
          :widgetValue="conditionsForm.value"
          :widgetOptions="widgetOptions"
          :widgetDateShowType="widgetDateShowType"
          :multiple="multiple"
          style="display: inline-flex"
          @handleWidgetValue="handleWidgetValue"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, watch, nextTick, watchEffect, PropType } from 'vue'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash-es'
import CustomConditionControl from './CustomConditionControl.vue'
import { formCreateInjectionData } from '../../../inject'
import {
  MathOperatorOptions,
  DateRangeOptions,
  DatePickerOptions,
  FieldTypeSelectOptions
} from './enum'

const props = defineProps({
  condition: {
    type: Object as PropType<any>,
    default: () => {}
  },
  conditionList: {
    type: Object as PropType<any>,
    default: () => {}
  },
  labelConditionMap: {
    type: Object as PropType<any>,
    default: () => {}
  },
  teleported: {
    type: Boolean as PropType<boolean>,
    default: false
  }
})

const { t } = useI18n()
const widgetDate = ref<any>({
  valueFormat: 'YYYY-MM-DD',
  type: 'date'
})
const TwoCascaderRef = ref<HTMLElement | any>(null)
const conditionsForm = ref<any>({
  firstData: [],
  firstDataObject: {},
  twoData: [],
  twoDataObject: {},
  operator: '',
  fieldTypeEnum: 'CUSTOM',
  workOrderRange: null,
  rangeDateType: null,
  customerDateType: null,
  value: '',
  dateType: ''
})
const injectionData: any = inject(formCreateInjectionData)
const injectDesigner: any = inject('designer')
const dateRange = ref<any[]>([])
const MathOperatorSelectOptions = ref<any>([])
const firstOpertatorSelectControl = ref<boolean>(false)
const CascaderRef = ref<HTMLElement | any>(null)
const customConditionControlRef = ref<HTMLElement | any>(null)
const planSettingSelect = ref<any[]>([])
const planSettingTwoSelect = ref<any[]>([])
const widgetForm = ref<any>({})
const multiple = ref<boolean>(false)
const widgetOptions = ref<any>([])
const widgetDateShowType = ref<string>('')

const emit = defineEmits<{
  (e: 'handleChange', data: any): void
  (e: 'handleChangeConditions', data: any): void
  (e: 'handleConditionFirstData', bool: boolean, data: any, formPubId: string): void
}>()

/**
 * 第一个控件选择值类型
 */
const handleSelectCascader = async (): Promise<void> => {
  const cascaderRefs = CascaderRef.value.getCheckedNodes()[0]
  const { fieldType } = cascaderRefs.data
  conditionsForm.value.firstDataObject = CascaderRef.value.getCheckedNodes()[0].data

  if (['DrInputNumber', 'DrRate', 'DrPercentage'].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions['number']
    firstOpertatorSelectControl.value = false
  } else if (['DrDatePicker'].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions['date']
    firstOpertatorSelectControl.value = true
  } else if (['DrSCCSMemberSelect', 'DrSCCSGroupMemberSelect'].includes(fieldType)) {
    MathOperatorSelectOptions.value = MathOperatorOptions['empty']
    firstOpertatorSelectControl.value = true
  } else if (fieldType === 'DrFormulas') {
    const formatConfigType = cascaderRefs.data?.props?.formatConfig.type
    if (formatConfigType === 'number' || formatConfigType === 'percentage') {
      MathOperatorSelectOptions.value = MathOperatorOptions['number']
      firstOpertatorSelectControl.value = false
      widgetForm.value.type = 'DrInputNumber'
    } else if (formatConfigType === 'text') {
      MathOperatorSelectOptions.value = MathOperatorOptions['widget']
      firstOpertatorSelectControl.value = true
      widgetForm.value.type = 'DrInput'
    } else if (formatConfigType === 'date') {
      MathOperatorSelectOptions.value = MathOperatorOptions['date']
      firstOpertatorSelectControl.value = false
      widgetForm.value.type = 'DrDatePicker'
    }
  } else {
    MathOperatorSelectOptions.value = MathOperatorOptions['widget']
    firstOpertatorSelectControl.value = false
  }

  if (['DrRadio', 'DrCheckbox', 'DrDatePicker'].includes(fieldType)) {
    const { options, showType } = cascaderRefs.data.props
    widgetOptions.value = options
    widgetDate.value.type = ['datetimesecond', 'datetime'].includes(showType)
      ? 'datetime'
      : showType
    const formatValue = {
      datetimesecond: 'YYYY-MM-DD HH:mm:ss',
      datetime: 'YYYY-MM-DD HH:mm',
      date: 'YYYY-MM-DD',
      month: 'YYYY-MM',
      year: 'YYYY'
    }
    widgetDate.value.valueFormat = formatValue[showType]
    widgetDate.value.format = formatValue[showType]
    widgetDate.value.timeFormat = showType === 'datetime' ? 'HH:mm' : 'HH:mm:ss'
    conditionsForm.value.dateType =
      showType === 'year' ? 'year' : showType === 'month' ? 'month' : 'day'
  } else if (
    cascaderRefs.pathNodes[1].data.type === 'main' &&
    ['DrRadio', 'DrCheckbox', 'DrDatePicker'].includes(fieldType)
  ) {
    widgetDate.value.type = 'date'
    widgetDate.value.valueFormat = 'YYYY-MM-DD'
    widgetDate.value.format = 'YYYY-MM-DD'
    conditionsForm.value.dateType = 'day'
  } else if (fieldType === 'DrFormulas') {
    const { options, formatDateType } = cascaderRefs.data.props.formatConfig
    widgetOptions.value = options
    widgetDate.value.type = ['datetimesecond', 'datetime'].includes(formatDateType)
      ? 'datetime'
      : formatDateType
    const formatValue = {
      datetimesecond: 'YYYY-MM-DD HH:mm:ss',
      datetime: 'YYYY-MM-DD HH:mm',
      date: 'YYYY-MM-DD',
      month: 'YYYY-MM',
      year: 'YYYY'
    }
    widgetDate.value.valueFormat = formatValue[formatDateType]
    widgetDate.value.format = formatValue[formatDateType]
    widgetDate.value.timeFormat = formatDateType === 'datetime' ? 'HH:mm' : 'HH:mm:ss'
    conditionsForm.value.dateType =
      formatDateType === 'year' ? 'year' : formatDateType === 'month' ? 'month' : 'day'
  }

  multiple.value =
    ([
      'DrRadio',
      'DrSCCSMemberSingleSelect',
      'DrSCCSMemberMultipleSelect',
      'DrSCCSGroupMemberSingleSelect',
      'DrSCCSGroupMemberMultipleSelect'
    ].includes(fieldType) &&
      ['IN', 'NOT_IN'].includes(conditionsForm.value.operator)) ||
    fieldType === 'DrCheckbox'

  if (fieldType !== 'DrFormulas') {
    widgetForm.value = Object.assign(cloneDeep(cascaderRefs.data), {
      type: cascaderRefs.data.fieldType
    })
  }

  conditionsForm.value.operator = ''
  conditionsForm.value.twoData = []
  conditionsForm.value.fieldTypeEnum = 'CUSTOM'
  conditionsForm.value.workOrderRange = null
  conditionsForm.value.rangeDateType = null
  conditionsForm.value.customerDateType = null
  conditionsForm.value.value = ''
  customConditionControlRef.value?.clearData()
}

/**
 * 第二个控件切换值
 */
const handleSelectOperator = () => {
  const cascaderRefs = CascaderRef.value.getCheckedNodes()[0]
  const { fieldType } = cascaderRefs.data
  multiple.value =
    ([
      'DrRadio',
      'DrSCCSMemberSingleSelect',
      'DrSCCSMemberMultipleSelect',
      'DrSCCSGroupMemberSingleSelect',
      'DrSCCSGroupMemberMultipleSelect'
    ].includes(fieldType) &&
      ['IN', 'NOT_IN'].includes(conditionsForm.value.operator)) ||
    fieldType === 'DrCheckbox'
  conditionsForm.value.twoData = []
  conditionsForm.value.fieldTypeEnum = 'CUSTOM'
  conditionsForm.value.workOrderRange = null
  conditionsForm.value.rangeDateType = null
  conditionsForm.value.customerDateType = null
  conditionsForm.value.value = ''
  customConditionControlRef.value?.clearData()
}

const allowedUseWidgetList = (widgetJsonList: any, widgetType: any, widgetId: string | null) => {
  const widgetTypeList = widgetType
    ? widgetType
    : [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrDatePicker',
        'DrLocation',
        'DrRate',
        'DrFormulas',
        'DrSCCSMemberSelect',
        'DrSCCSGroupMemberSelect'
      ]

  const widgetItem = widgetJsonList.find((widget) => widget._fc_id === widgetId)

  let formulasFormulas = {
    DrInputNumber: 'number',
    DrPercentage: 'number',
    DrInput: 'text',
    DrTextarea: 'text',
    DrDatePicker: 'date'
  }

  return widgetId
    ? widgetJsonList
        .filter((widget) => widgetTypeList.includes(widget.type) && widget._fc_id !== widgetId)
        .filter(
          (widget) =>
            (widget.type === 'DrFormulas' &&
              formulasFormulas[widgetItem.type] === widget.props.formatConfig.type) ||
            widget.type !== 'DrFormulas'
        )
    : widgetJsonList
        .filter((widget) => widgetTypeList.includes(widget.type))
        .filter(
          (widget) =>
            (widget.type === 'DrFormulas' &&
              formulasFormulas[widgetItem.type] === widget.props.formatConfig.type) ||
            widget.type !== 'DrFormulas'
        )
}

const getMainFormCascaderData = (conditionList: any, widgetType: any, widgetId: any) => {
  const mainFormData = conditionList.filter((widgetForm) => widgetForm.formType === 'MAIN_FORM')

  const list = mainFormData.map((widgetForm) => {
    const type = widgetForm.formType.toLowerCase().replace(/_([a-z])/g, function (match, letter) {
      return letter.toUpperCase()
    })

    let childrenList: any[] = []
    childrenList.push({
      deleted: false,
      field: null,
      fieldType: null,
      id: null,
      label: '表单字段',
      type: widgetForm.formType === 'MAIN_FORM' ? 'form' : type,
      value: widgetForm.id,
      children: allowedUseWidgetList(widgetForm.widgetJsonList, widgetType, widgetId).map(
        (widgetChild) => {
          return {
            deleted: false,
            field: widgetChild._fc_id,
            fieldType: widgetChild.type,
            fromId: widgetForm.id,
            id: null,
            label: widgetChild.title,
            type: 'form',
            value: widgetChild._fc_id,
            props: widgetChild.props
          }
        }
      )
    })

    return {
      deleted: false,
      field: null,
      fieldType: null,
      id: null,
      label: widgetForm.name,
      type: widgetForm.formType === 'MAIN_FORM' ? 'main' : type,
      value: widgetForm.formType === 'MAIN_FORM' ? 'mainForm' : widgetForm.id,
      children: childrenList
    }
  })
  return list
}

const handleObtainAppointWidgetType = (widgetType?: string, widgetId?: any) => {
  const index = injectionData?.templateTreeData.value.findIndex(
    (template) => template.id === injectionData.currentFormId.value
  )
  const templateData = injectionData?.templateTreeData.value.find(
    (template) => template.id === injectionData.currentFormId.value
  )
  if (templateData) {
    // eslint-disable-next-line vue/no-side-effects-in-computed-properties
    injectionData?.templateTreeData.value.splice(
      index,
      1,
      Object.assign(templateData, {
        widgetJsonList: injectDesigner.setupState.getFormDescription()
      })
    )
  }
  const conditionList = injectionData?.templateTreeData.value
  let cascaderData: any[] = []
  const mainFormData = getMainFormCascaderData(conditionList, widgetType, widgetId)
  cascaderData.push(...mainFormData)

  if (injectionData?.formType.value === 'MAIN_FORM') {
    // 主表单
    const milestoneDataList = conditionList.filter(
      (widgetForm) => widgetForm.formType === 'MILESTONE'
    )
    const mileStoneWidgetData = milestoneDataList.map((milestoneItem) => {
      const type = milestoneItem.formType
        .toLowerCase()
        .replace(/_([a-z])/g, function (match, letter) {
          return letter.toUpperCase()
        })
      let childrenList: any[] = []
      if (milestoneItem.replyForm) {
        childrenList.push({
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: '里程碑批复',
          type: 'msReplyForm',
          value: milestoneItem.replyForm.id,
          children: allowedUseWidgetList(
            milestoneItem.replyForm.widgetJsonList,
            widgetType,
            widgetId
          ).map((widgetChild) => {
            return {
              deleted: false,
              field: widgetChild._fc_id,
              fieldType: widgetChild.type,
              fromId: milestoneItem.replyForm.id,
              id: null,
              label: widgetChild.title,
              type: 'msReplyForm',
              value: widgetChild._fc_id,
              props: widgetChild.props
            }
          })
        })
      }

      return {
        deleted: false,
        field: null,
        fieldType: null,
        id: null,
        label: milestoneItem.name,
        type: type,
        value: milestoneItem.id,
        children: childrenList
      }
    })
    cascaderData.push(...mileStoneWidgetData)
  } else if (injectionData?.formType.value === 'FORM') {
    // 工单
    const milestoneDataList = conditionList
      .filter((widgetForm) => widgetForm.formType === 'MILESTONE')
      .find(
        (milestoneTemplateData) =>
          milestoneTemplateData.formList.filter(
            (data) => data.id === injectionData?.currentFormId.value
          ).length > 0
      )
    const mileStoneWidgetData = [milestoneDataList].map((milestoneItem) => {
      const type = milestoneItem.formType
        .toLowerCase()
        .replace(/_([a-z])/g, function (match, letter) {
          return letter.toUpperCase()
        })
      let childrenList: any[] = []
      if (milestoneDataList.formList.length > 0) {
        milestoneDataList.formList.forEach((formData) => {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: formData.name,
            type: 'formSysField',
            value: formData.id,
            children: allowedUseWidgetList(formData.widgetJsonList, widgetType, widgetId).map(
              (widgetChild) => {
                return {
                  deleted: false,
                  field: widgetChild._fc_id,
                  fieldType: widgetChild.type,
                  fromId: formData.id,
                  id: null,
                  label: widgetChild.title,
                  type: 'form',
                  value: widgetChild._fc_id,
                  props: widgetChild.props
                }
              }
            )
          })
        })
      }

      return {
        deleted: false,
        field: null,
        fieldType: null,
        id: null,
        label: milestoneItem.name,
        type: type,
        value: milestoneItem.id,
        children: childrenList
      }
    })
    cascaderData.push(...mileStoneWidgetData)
  } else if (injectionData?.formType.value === 'MILESTONE') {
    if (injectionData?.msFormType.value === 'REPLY_TO_WORK_ORDER') {
      // 工单批复
      const milestoneDataList = conditionList.find(
        (widgetForm) => widgetForm.id === injectionData?.currentFormId.value
      )
      const mileStoneWidgetData = [milestoneDataList].map((milestoneItem) => {
        const type = milestoneItem.formType
          .toLowerCase()
          .replace(/_([a-z])/g, function (match, letter) {
            return letter.toUpperCase()
          })
        let childrenList: any[] = []
        if (milestoneItem.replyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: '里程碑批复',
            type: 'msReplyForm',
            value: milestoneItem.replyForm.id,
            children: allowedUseWidgetList(
              milestoneItem.replyForm.widgetJsonList,
              widgetType,
              widgetId
            ).map((widgetChild) => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.replyForm.id,
                id: null,
                label: widgetChild.title,
                type: 'msReplyForm',
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }
        if (milestoneItem.workOrderReplyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: '工单批复',
            type: 'replyForm',
            value: milestoneItem.replyForm.id,
            children: allowedUseWidgetList(
              milestoneItem.workOrderReplyForm.widgetJsonList,
              widgetType,
              widgetId
            ).map((widgetChild) => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.replyForm.id,
                id: null,
                label: widgetChild.title,
                type: 'workOrderReply',
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }
        if (milestoneDataList.formList.length > 0) {
          milestoneDataList.formList.forEach((formData) => {
            childrenList.push({
              deleted: false,
              field: null,
              fieldType: null,
              id: null,
              label: formData.name,
              type: 'formSysField',
              value: formData.id,
              children: allowedUseWidgetList(formData.widgetJsonList, widgetType, widgetId).map(
                (widgetChild) => {
                  return {
                    deleted: false,
                    field: widgetChild._fc_id,
                    fieldType: widgetChild.type,
                    fromId: formData.id,
                    id: null,
                    label: widgetChild.title,
                    type: 'form',
                    value: widgetChild._fc_id,
                    props: widgetChild.props
                  }
                }
              )
            })
          })
        }

        return {
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: milestoneItem.name,
          type: type,
          value: milestoneItem.id,
          children: childrenList
        }
      })
      cascaderData.push(...mileStoneWidgetData)
    } else {
      // 里程碑批复
      const milestoneDataList = conditionList.find(
        (widgetForm) => widgetForm.id === injectionData?.currentFormId.value
      )
      const mileStoneWidgetData = [milestoneDataList].map((milestoneItem) => {
        const type = milestoneItem.formType
          .toLowerCase()
          .replace(/_([a-z])/g, function (match, letter) {
            return letter.toUpperCase()
          })
        let childrenList: any[] = []
        if (milestoneItem.replyForm) {
          childrenList.push({
            deleted: false,
            field: null,
            fieldType: null,
            id: null,
            label: '里程碑批复',
            type: 'msReplyForm',
            value: milestoneItem.replyForm.id,
            children: allowedUseWidgetList(
              milestoneItem.replyForm.widgetJsonList,
              widgetType,
              widgetId
            ).map((widgetChild) => {
              return {
                deleted: false,
                field: widgetChild._fc_id,
                fieldType: widgetChild.type,
                fromId: milestoneItem.replyForm.id,
                id: null,
                label: widgetChild.title,
                type: 'msReplyForm',
                value: widgetChild._fc_id,
                props: widgetChild.props
              }
            })
          })
        }

        return {
          deleted: false,
          field: null,
          fieldType: null,
          id: null,
          label: milestoneItem.name,
          type: type,
          value: milestoneItem.id,
          children: childrenList
        }
      })
      cascaderData.push(...mileStoneWidgetData)
    }
  }
  return cascaderData
}

/**
 * 第三个控件切换值
 */
const handleSelectFieldTypeEnum = async () => {
  conditionsForm.value.twoData = []
  conditionsForm.value.workOrderRange = null
  conditionsForm.value.rangeDateType = null
  conditionsForm.value.customerDateType = null
  conditionsForm.value.value = ''
  const { fieldTypeEnum } = conditionsForm.value
  if (fieldTypeEnum === 'OTHER_FIELD') {
    const WIDGET_CONTRAST = {
      DrInput: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrLocation',
        'DrDatePicker',
        'DrRate',
        'DrFormulas'
      ],
      DrTextarea: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrLocation',
        'DrDatePicker',
        'DrRate',
        'DrFormulas'
      ],
      DrLocation: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrLocation',
        'DrDatePicker',
        'DrRate',
        'DrFormulas'
      ],
      DrRadio: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrLocation',
        'DrDatePicker',
        'DrRate',
        'DrFormulas'
      ],
      DrCheckbox: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrLocation',
        'DrDatePicker',
        'DrRate',
        'DrFormulas'
      ],
      DrDatePicker: ['DrDatePicker'],
      DrInputNumber: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
      DrPercentage: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
      DrRate: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
      DrEditor: ['DrEditor'],
      DrFilesUpload: ['DrFilesUpload'],
      DrImagesUpload: ['DrImagesUpload'],
      DrSignature: ['DrSignature'],
      DrFormulas: [
        'DrInput',
        'DrTextarea',
        'DrInputNumber',
        'DrPercentage',
        'DrRadio',
        'DrCheckbox',
        'DrDatePicker',
        'DrLocation',
        'DrRate',
        'DrFormulas'
      ]
    }
    let twoSelectData = handleObtainAppointWidgetType(
      WIDGET_CONTRAST[conditionsForm.value.firstDataObject.fieldType],
      conditionsForm.value.firstDataObject.value
    )

    if (conditionsForm.value.firstDataObject.fieldType === 'DrFormulas') {
      const HIDDEN_WIDGET = {
        number: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
        percentage: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
        text: [
          'DrInput',
          'DrTextarea',
          'DrInputNumber',
          'DrPercentage',
          'DrRadio',
          'DrCheckbox',
          'DrDatePicker',
          'DrLocation',
          'DrRate',
          'DrFormulas'
        ],
        date: ['DrDatePicker', 'DrFormulas']
      }
      const twoItemData = twoSelectData.map((twoData) => {
        return Object.assign(twoData, {
          children: twoData.children.map((twoChildData) => {
            return Object.assign(twoChildData, {
              children: (twoChildData.children instanceof Array ? twoChildData.children : [])
                .filter((child) =>
                  HIDDEN_WIDGET[
                    conditionsForm.value.firstDataObject.props.formatConfig.type
                  ].includes(child.fieldType)
                )
                .filter((childItem) => {
                  return (
                    (childItem.fieldType === 'DrFormulas' &&
                      childItem.props.formatConfig &&
                      childItem.props.formatConfig.type ===
                        conditionsForm.value.firstDataObject.props.formatConfig.type) ||
                    childItem.fieldType !== 'DrFormulas'
                  )
                })
            })
          })
        })
      })
      planSettingTwoSelect.value = filterEmptyChildren(twoItemData)
    } else {
      planSettingTwoSelect.value = filterEmptyChildren(twoSelectData)
    }
  }
  if (fieldTypeEnum === 'CUSTOM') {
    widgetForm.value = CascaderRef.value.getCheckedNodes()[0].data.props
  }
}

/**
 * 第二个级联控件数据选择
 */
const handleSelectTwoCascader = () => {
  conditionsForm.value.twoDataObject = TwoCascaderRef.value.getCheckedNodes()[0].data
}

const handleSelectDateRangeType = () => {
  conditionsForm.value.value = ''
  customConditionControlRef.value?.clearData()
}

const handleWidgetValue = (bindValue: string, label: string) => {
  conditionsForm.value.value = bindValue
  if (label) {
    conditionsForm.value.label = label
  }
}

const getRangeTypeOptions = () => {
  if (conditionsForm.value.dateType) {
    const dateTypeOptions = {
      year: ['THIS_YEAR', 'LAST_YEAR', 'NEXT_YEAR'],
      month: ['THIS_MONTH', 'LAST_MONTH', 'NEXT_MONTH'],
      day: [
        'THIS_YEAR',
        'LAST_YEAR',
        'NEXT_YEAR',
        'THIS_MONTH',
        'LAST_MONTH',
        'NEXT_MONTH',
        'THIS_WEEK',
        'LAST_WEEK',
        'NEXT_WEEK'
      ]
    }
    return DateRangeOptions.filter(
      (date) =>
        dateTypeOptions[conditionsForm.value.dateType].includes(date.value) ||
        ['DATE_RANGE', 'LAST', 'NEXT'].includes(date.value)
    )
  } else {
    return DateRangeOptions
  }
}

const handleDatePickerValue = () => {
  conditionsForm.value.value = dateRange.value.join(',')
}

const handleEdit = (e) => {
  let value = e.replace(/^(0+)|[^\d]+/g, '') // 以0开头或者输入非数字，会被替换成空
  value = value.replace(/(\d{15})\d*/, '$1') // 最多保留15位整数
  conditionsForm.value.value = value
}

const getDatePickerOptionsByType = (options) => {
  const dateTypeOptions = {
    year: ['THIS_YEAR', 'LAST_YEAR', 'NEXT_YEAR'],
    month: ['THIS_MONTH', 'LAST_MONTH', 'NEXT_MONTH'],
    day: ['TODAY', 'YESTERDAY', 'TOMORROW']
  }
  return dateTypeOptions[conditionsForm.value.dateType]
    ? options.filter(
        (option) =>
          dateTypeOptions[conditionsForm.value.dateType].includes(option.value) ||
          ['ASSIGN', 'BEFORE', 'AFTER'].includes(option.value)
      )
    : options
}

watch(
  conditionsForm,
  async () => {
    handleSetTableRowConditions()
  },
  {
    deep: true
  }
)

watch(
  () => props.condition,
  (newVal) => {
    if (newVal && newVal.firstDataObject && Object.keys(newVal).length > 0) {
      conditionsForm.value = newVal
      nextTick(async () => {
        const { fieldType, field } = newVal.firstDataObject
        if (['DrInputNumber', 'DrRate', 'DrPercentage'].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions['number']
          firstOpertatorSelectControl.value = false
        } else if (['DrDatePicker'].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions['date']
          firstOpertatorSelectControl.value = true
          if (newVal.rangeDateType === 'DATE_RANGE') {
            dateRange.value = newVal.value.split(',')
          }
          if (newVal.dateType === 'year') {
            widgetDate.value.valueFormat = 'YYYY'
            widgetDate.value.format = 'YYYY'
            conditionsForm.value.dateType = 'year'
          }
        } else if (
          ['DrRadio', 'DrCheckbox', 'DrDatePicker'].includes(fieldType) &&
          field !== 'orderState'
        ) {
          MathOperatorSelectOptions.value = MathOperatorOptions['widget']
          firstOpertatorSelectControl.value = false
          const { options } = CascaderRef.value.getCheckedNodes()[0].data.props
          widgetOptions.value = options
        } else if (['DrSCCSMemberSelect', 'DrSCCSGroupMemberSelect'].includes(fieldType)) {
          MathOperatorSelectOptions.value = MathOperatorOptions['empty']
          firstOpertatorSelectControl.value = true
        } else if (fieldType === 'DrFormulas') {
          const formatConfigType =
            newVal.firstDataObject?.props?.formatConfig.type ||
            CascaderRef.value.getCheckedNodes()[0].data.props.formatConfig.type
          if (formatConfigType === 'number' || formatConfigType === 'percentage') {
            MathOperatorSelectOptions.value = MathOperatorOptions['number']
            firstOpertatorSelectControl.value = false
            widgetForm.value.type = 'DrInputNumber'
          } else if (formatConfigType === 'text') {
            MathOperatorSelectOptions.value = MathOperatorOptions['widget']
            firstOpertatorSelectControl.value = false
            widgetForm.value.type = 'DrInput'
          } else if (formatConfigType === 'date') {
            MathOperatorSelectOptions.value = MathOperatorOptions['date']
            firstOpertatorSelectControl.value = true
            widgetForm.value.type = 'DrDatePicker'
          }
        } else {
          MathOperatorSelectOptions.value = MathOperatorOptions['widget']
          firstOpertatorSelectControl.value = false
        }
        if (newVal.firstDataObject.fieldType !== 'DrFormulas') {
          widgetForm.value = Object.assign(cloneDeep(newVal.firstDataObject), {
            type: newVal.firstDataObject.fieldType
          })
        }
        multiple.value =
          ([
            'DrRadio',
            'DrSCCSMemberSingleSelect',
            'DrSCCSMemberMultipleSelect',
            'DrSCCSGroupMemberSingleSelect',
            'DrSCCSGroupMemberMultipleSelect'
          ].includes(newVal.firstDataObject.fieldType) &&
            ['IN', 'NOT_IN'].includes(conditionsForm.value.operator)) ||
          newVal.firstDataObject.fieldType === 'DrCheckbox'

        if (newVal.fieldTypeEnum === 'OTHER_FIELD') {
          const WIDGET_CONTRAST = {
            DrInput: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrCheckbox',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ],
            DrTextarea: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrCheckbox',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ],
            DrRadio: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ],
            DrCheckbox: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrCheckbox',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ],
            DrDatePicker: ['DrDatePicker'],
            DrInputNumber: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
            DrPercentage: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
            DrLocation: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrCheckbox',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ],
            DrRate: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
            DrFormulas: [
              'DrInput',
              'DrTextarea',
              'DrInputNumber',
              'DrPercentage',
              'DrRadio',
              'DrCheckbox',
              'DrDatePicker',
              'DrLocation',
              'DrRate',
              'DrFormulas'
            ]
          }
          const twoSelectData = handleObtainAppointWidgetType(
            WIDGET_CONTRAST[conditionsForm.value.firstDataObject.fieldType],
            conditionsForm.value.firstDataObject.value
          )
          if (conditionsForm.value.firstDataObject.fieldType === 'DrFormulas') {
            const HIDDEN_WIDGET = {
              number: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
              percentage: ['DrInputNumber', 'DrPercentage', 'DrRate', 'DrFormulas'],
              text: [
                'DrInput',
                'DrTextarea',
                'DrInputNumber',
                'DrPercentage',
                'DrRadio',
                'DrCheckbox',
                'DrDatePicker',
                'DrLocation',
                'DrRate',
                'DrFormulas'
              ],
              date: ['DrDatePicker', 'DrFormulas']
            }
            const twoItemData = twoSelectData.map((twoData) => {
              return Object.assign(twoData, {
                children: twoData.children.map((twoChildData) => {
                  return Object.assign(twoChildData, {
                    children: (twoChildData.children instanceof Array ? twoChildData.children : [])
                      .filter((child) =>
                        HIDDEN_WIDGET[
                          conditionsForm.value.firstDataObject.props.formatConfig.type
                        ].includes(child.fieldType)
                      )
                      .filter((childItem) => {
                        return (
                          (childItem.fieldType === 'DrFormulas' &&
                            childItem.props.formatConfig &&
                            childItem.props.formatConfig.type ===
                              conditionsForm.value.firstDataObject.props.formatConfig.type) ||
                          childItem.fieldType !== 'DrFormulas'
                        )
                      })
                  })
                })
              })
            })
            planSettingTwoSelect.value = filterEmptyChildren(twoItemData)
          } else {
            planSettingTwoSelect.value = filterEmptyChildren(twoSelectData)
          }
        }
      })
    } else {
      conditionsForm.value = {
        firstData: [],
        firstDataObject: {},
        twoData: [],
        twoDataObject: {},
        operator: '',
        fieldTypeEnum: 'CUSTOM',
        workOrderRange: null,
        rangeDateType: null,
        customerDateType: null,
        value: '',
        dateType: ''
      }
    }
  },
  { deep: true, immediate: true }
)

watch(
  () => props.conditionList,
  () => {
    if (props.conditionList) {
      planSettingSelect.value = filterEmptyChildren(props.conditionList)
    }
  },
  {
    deep: true,
    immediate: true
  }
)

/**
 * 过滤数组对象，移除所有没有有效 children 的项
 * @param {Array} arr - 要过滤的数组
 * @returns {Array} 过滤后的新数组
 */
function filterEmptyChildren(arr) {
  if (!Array.isArray(arr)) return arr

  return arr
    .map((item) => {
      // 深拷贝当前项，避免修改原对象
      const newItem = { ...item }

      // 如果存在 children 且是数组，递归处理
      if (newItem.children && Array.isArray(newItem.children)) {
        const filteredChildren = filterEmptyChildren(newItem.children)

        // 如果过滤后的 children 不为空，则保留
        if (filteredChildren.length > 0) {
          newItem.children = filteredChildren
          return newItem
        }
        // 否则返回 undefined 将在下一步被过滤掉
        return undefined
      }

      // 没有 children 属性的项直接保留
      return newItem
    })
    .filter((item) => item !== undefined) // 过滤掉 undefined 的项
}

const handleSetTableRowConditions = () => {
  emit('handleChangeConditions', conditionsForm.value)
}

const getTimeDateType = () => {
  return conditionsForm.value.dateType === 'year'
    ? '年'
    : conditionsForm.value.dateType === 'month'
      ? '月'
      : '日'
}

defineExpose({
  CascaderRef: CascaderRef.value,
  conditionsForm: conditionsForm.value
})
</script>
<style lang="scss" scoped>
.custom-conditions-control-row {
  display: flex;
  margin-right: 10px;

  ::v-deep(.el-cascader) {
    width: 140px;
    margin-right: 10px;
  }

  ::v-deep(.el-select) {
    margin-right: 10px;
  }

  ::v-deep(.el-date-editor) {
    margin-right: 10px;
  }

  ::v-deep(.el-textarea) {
    margin-right: 10px;
  }

  ::v-deep(.el-input) {
    margin-right: 10px;
  }

  ::v-deep(.el-cascader-node) {
    max-width: 300px;
  }

  .conditions-form-input {
    width: 100px;

    ::v-deep(.el-input-group__append) {
      padding: 0 10px;
    }
  }

  .custom-conditions-operator-control {
    width: 130px;
  }

  .custom-conditions-person-operator-control {
    width: 180px;
  }

  .custom-conditions-operator-small-control {
    width: 80px;
  }

  .custom-conditions-col {
    display: inline-flex;
  }
}
</style>
